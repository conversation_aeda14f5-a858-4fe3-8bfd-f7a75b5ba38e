<section class="topbar-container z-30 ">
  <nav [ngClass]="isSidebarOpen?'topbarmargin':'topbarfull'"
       class="navbar-top z-20 gap-3 bg-n0 py-3 shadow-sm duration-300 border-b border-n0 dark:border-n700 dark:bg-bg4 xl:py-4 xxxl:py-6"
       id="topbar">
    <div class="topbar-inner flex items-center justify-between">
      <div class="flex grow items-center gap-4 xxl:gap-6">
        <a class="topbar-logo hidden shrink-0" routerLink="/dashboards/style-01">
          <img [src]="colorMode=='light'?'assets/images/logo.svg':'assets/images/logo.svg'"
               alt="logo"
               class="logo-full2 hidden lg:block"
               height="38" width="174"/>
        </a>
                <button (click)="toggleSidebar()" aria-label="sidebar-toggle-btn"
                        class="flex md:hidden items-center rounded-s-2xl bg-primary px-0.5 py-3 text-xl text-n0" id="sidebar-toggle-btn">
                  <i class="las la-angle-left text-lg"></i>
                </button>
        <!-- Search bar -->
        <form >
         <p> {{ "welcome" | translate}}  {{profileData?.name}}</p>
        </form>
      </div>
      <div class="flex items-center gap-3 sm:gap-4 xxl:gap-6">
        <!-- mobile Search  -->
        <div class="relative lg:hidden">
          <button
            class="flex h-10 w-10 cursor-pointer select-none items-center justify-center gap-2 rounded-full border border-n30 bg-primary/5 dark:border-n500 dark:bg-bg3 md:h-12 md:w-12"
            id="mobile-search-btn">
            <i class="las la-search"></i>
          </button>
          <div
            class="hide invisible absolute -left-8 top-full z-20 flex min-w-max max-w-[250px] origin-[20%_20%] gap-3 overflow-y-auto rounded-md bg-n0 p-3 opacity-0 shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)] duration-300 dark:bg-bg4"
            id="mobile-search">
            <form
              class="flex w-full items-center justify-between gap-3 rounded-[30px] border border-n30 bg-secondary1/5 p-1 focus-within:border-primary dark:border-n500 dark:bg-bg3 xxl:p-2">
              <input class="w-full bg-transparent py-1 ltr:pl-4 rtl:pr-4" placeholder="Search" type="text"/>
              <button
                class="flex h-7 w-7 shrink-0 items-center justify-center rounded-full bg-primary text-n0 lg:h-8 lg:w-8">
                <i class="las la-search text-lg"></i>
              </button>
            </form>
          </div>
        </div>

        @if(!notCompleteUser()){
          <app-create-sub-accounts [isComplete]="isComplete()"/>
        }
        <!-- dark mode toggle -->
        <button (click)="toggleDarkMode()" aria-label="dark mode switch"
                class="h-10 w-10 shrink-0 rounded-full border border-n30 bg-primary/5 dark:border-n500 dark:bg-bg3 md:h-12 md:w-12"
                id="darkModeToggle">
          <i class="las la-sun text-2xl dark:hidden"></i>
          <span class="hidden text-n30 dark:block">
            <i class="las la-moon text-2xl"></i>
          </span>
        </button>
        <!-- Notification -->
        <app-notification-dropdown/>
        <!-- language dropdown -->
        <app-language/>
        <!-- Profile dropdown -->
        <app-profile-dropdown/>
      </div>
    </div>
  </nav>
  <!-- Vertical -->
  <aside [ngClass]="isSidebarOpen?'sidebarshow':'sidebarhide'" class="sidebar pb-12 w-[336px] z-[21] bg-n0 dark:!bg-bg4" id="sidebar">
    <div class="sidebar-inner relative">
      <div class="logo-column">
        <div class="logo-container ">
          <div class="logo-inner flex flex-col items-center justify-center">
            <a class="logo-wrapper" routerLink="/">
              <img [src]="colorMode=='light'?'assets/images/logo.svg':'assets/images/logo.svg'"
                   alt="logo" class="logo-full" height="38" width="174"/>
              <img [src]="colorMode=='light'?'assets/images/logo.svg':'assets/images/logo.svg'" alt="logo"
                   class="logo-icon hidden" height="36" width="37"/>
            </a>
            <img [height]="38"
                 [src]="colorMode=='light'?'assets/images/logo.svg':'assets/images/logo.svg'"
                 [width]="141"
                 alt="logo text"
                 class="logo-text !pb-[26px] dark:!pb-[38px] hidden"/>

          </div>
        </div>


        <div class="menu-container ">
          <div class="menu-wrapper">
            <p class="menu-heading !my-0 !mb-3 !py-0 !pb-3"></p>
            <ul class="menu-ul">
              @for (menu of filteredSidebarData; track menu) {
                <li class="menu-li">
                  <a [routerLink]="menu.url" (click)="clickRoute()" routerLinkActive="text-primary"
                     [routerLinkActiveOptions]="{exact: true}"
                  >
                    <button (click)="setActiveMenu(menu.name)" [ngClass]="menu.name === activeMenu ? 'active':''"
                            class="menu-btn border-n30 bg-n0 dark:!border-n500 dark:bg-bg4">
                    <span class="flex items-center justify-center gap-2">
                      <span class="menu-icon">
                        <i [ngClass]="menu.icon"></i>
                      </span>
                      <span class="menu-title font-medium">{{ menu.name | translate }}</span>
                    </span>
                      <span class="chevron-down hidden">
                      <i class="las la-angle-down text-base"></i>
                    </span>
                    </button>
                  </a>
                </li>
              }
            </ul>
            <div class="px-4 pb-8 mb-14 xxl:px-6 xxxl:px-8">
              <div class="balance-part">
                <p class="border-t-2 border-dashed border-primary/20 py-4 text-xs font-semibold"></p>
              </div>
              <div class="upgrade-part overflow-auto p-2 text-center">
               <p class="font-extrabold text-base">{{"walletbalance"| translate}}</p>
               <p class="text-[#015C8E] ">  {{authService.userBalance() || 0}} {{ "SR" | translate }}</p>

            <div class="flex my-5 mx-5 ">
              <span class="mx-3 z-50 cursor-auto " (click)="openCreateDeposit()">
                <span class="flex center">
                  <img src="../../../../../assets/images/Wallet.svg" class="m-2" />
                  <span class="chevron-down mt-5">
                      <i class="las la-angle-down text-base"></i>
                  </span>
              </span>

                <p class="my-1 font-normal text-xs">{{'Depositmethod' | translate}}</p>
              </span>
              <span>

                <img src="../../../../../assets/images/download.svg" class="m-[10px]"/>
                <p class="my-3 font-normal text-xs">{{"accountstatement"| translate }}</p>
              </span>
            </div>


              </div>
          
            </div>
          </div>

        </div>
      </div>
    </div>
  </aside>
</section>
<main [ngClass]="{'has-sidebar':isSidebarOpen}" class="main-content">
  <div class="main-inner">

    
<app-deposit-create
  [id]="'deposit-create'"
/>

    @if(notCompleteUser()){
      @if(createSubAccount()){
        <app-select-account [userMainAccount]="userMainAccount()" />
      }@else {
        @if(isKYCPending()||accountstatuspending){
          @switch (userType()) {
            @case (1){
               <div [formGroup]="quickForm" (ngSubmit)="sendRequest()" class="w-full h-5/6 rounded-2xl m-[10px] p-[30px] flex flex-col items-start justify-start gap-[15px] bg-white dark:bg-gray-900 ">
                <p class="text-xl font-semibold">{{"practicalexperiencerelatedtothefinancialsector"|translate}}</p>
                <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                  <button (click)="quickForm.get('haveExperience')?.setValue(false)" [ngClass]="!quickForm.get('haveExperience')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#054060bc] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                  {{ 'no' | translate }}
                  </button>
                  <button  (click)="quickForm.get('haveExperience')?.setValue(true)" [ngClass]="quickForm.get('haveExperience')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#054060bc] hover:text-white hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'yes' | translate }}
                  </button>
                </div>
                <div class="flex flex-col items-start justify-start gap-[15px] w-[500px]">
                   <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'educationLevel'|translate" [options]="educationLevel" [selectModel]="quickForm.get('educationLevel')?.valueChanges|async" (selectModelChange)="quickForm.get('educationLevel')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'annualIncome'|translate" [options]="annualIncomeRange" [selectModel]="quickForm.get('annualIncome')?.valueChanges|async" (selectModelChange)="quickForm.get('annualIncome')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'sourceOfIncome'|translate" [options]="sourceOfIncome" [selectModel]="quickForm.get('sourceOfIncome')?.valueChanges|async" (selectModelChange)="quickForm.get('sourceOfIncome')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'pureOwnership'|translate" [options]="totalAssetsRange" [selectModel]="quickForm.get('pureOwnership')?.valueChanges|async" (selectModelChange)="quickForm.get('pureOwnership')?.setValue($event)"/> 
  
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'nationality'|translate" [options]="nationalities" [selectModel]="quickForm.get('nationality')?.valueChanges|async" (selectModelChange)="quickForm.get('nationality')?.setValue($event)"/>
                </div>
                <div class="grid grid-cols-3 items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold">{{"theboardofdirectorsofalistedcompany"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isPoliticallyExposedPerson')?.setValue(false)"  [ngClass]="!quickForm.get('isPoliticallyExposedPerson')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#054060bc]hover:text-white hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button (click)="quickForm.get('isPoliticallyExposedPerson')?.setValue(true)"  [ngClass]="quickForm.get('isPoliticallyExposedPerson')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#054060bc] hover:text-white hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
  
                <div class="grid grid-cols-3 items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2  font-semibold"> {{"Areyoupoliticallyexposed"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isAssociatedWithPEP')?.setValue(false)"    [ngClass]="!quickForm.get('isAssociatedWithPEP')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button (click)="quickForm.get('isAssociatedWithPEP')?.setValue(true)" [ngClass]="quickForm.get('isAssociatedWithPEP')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                  </button>
                  </div>
                </div>
  
                <div class="grid grid-cols-3  items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold"> {{"politicallyexposedperson"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isRealBeneficiary')?.setValue(false)" [ngClass]="!quickForm.get('isRealBeneficiary')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button  (click)="quickForm.get('isRealBeneficiary')?.setValue(true)" [ngClass]="quickForm.get('isRealBeneficiary')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
                <div class="grid grid-cols-3  items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold">{{"therealbeneficiaryoftheaccount"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isBoardMember')?.setValue(false)" [ngClass]="!(quickForm.get('isBoardMember')?.value)?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button  (click)="quickForm.get('isBoardMember')?.setValue(true)" [ngClass]="(quickForm.get('isBoardMember')?.value)?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
                <div class="  w-full
                  flex flex-row items-center justify-start gap-[15px]">
                    <app-input
                    [control]="quickForm.get('jobTitle')"
                    [label]="'jobTitle'"
                    [type]="'text'"
                    [autofocus]="true"
                  />
                </div>
                <div class="  w-full
                flex flex-row items-center justify-start gap-[15px]">
                  <app-input
                  [control]="quickForm.get('companyName')"
                  [label]="'companyName'"
                  [type]="'text'"
                  [autofocus]="true"
                />
              </div>
  
                <div class="w-full
                  flex flex-row items-center justify-start gap-[15px]">
                    <app-input
                    [control]="quickForm.get('companyAddress')"
                    [label]="'companyAddress'"
                    [type]="'text'"
                    [autofocus]="true"
                  />
                </div>
          
  
                <div class="flex  items-start justify-end mt-[15px] w-full">
                  <button
                  (click)="sendRequest()"
                  class="border border-gray-500 rounded-2xl dark:bg-black-light dark:text-black h-[42px] w-[250px] flex items-center justify-center gap-[15px] text-black bg-gray-100  hover:bg-[#015C8E] hover:text-black hover:border-[#015C8E ] transition-all ease-in-out duration-100 disabled:cursor-not-allowed">
                    {{'Save'|translate}}
                    <img src="assets/images/arrownext.svg" class="text-black" alt="arrow">
                  </button>
                </div>
              </div>
  
            }
            @case(2){
              <app-company-layout [update]="notCompleteUser()" (changeStatus)="changeStatus($event)"></app-company-layout>
            }
            @default{
              <app-company-layout [update]="notCompleteUser()" (changeStatus)="changeStatus($event)"></app-company-layout>
            }
          }
        }@else{ 

          @switch (userType()) {
            @case (1 ||accountstatuspending){
               <div [formGroup]="quickForm" (ngSubmit)="sendRequest()" class="w-full h-5/6 rounded-2xl m-[10px] p-[30px] flex flex-col items-start justify-start gap-[15px] bg-white dark:bg-gray-900 ">
                <p class="text-xl font-semibold">{{"practicalexperiencerelatedtothefinancialsector"|translate}}</p>
                <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                  <button (click)="quickForm.get('haveExperience')?.setValue(false)" [ngClass]="!quickForm.get('haveExperience')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                  {{ 'no' | translate }}
                  </button>
                  <button  (click)="quickForm.get('haveExperience')?.setValue(true)" [ngClass]="quickForm.get('haveExperience')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'yes' | translate }}
                  </button>
                </div>
                <div class="flex flex-col items-start justify-start gap-[15px] w-[500px]">
                   <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'educationLevel'|translate" [options]="educationLevel" [selectModel]="quickForm.get('educationLevel')?.valueChanges|async" (selectModelChange)="quickForm.get('educationLevel')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'annualIncome'|translate" [options]="annualIncomeRange" [selectModel]="quickForm.get('annualIncome')?.valueChanges|async" (selectModelChange)="quickForm.get('annualIncome')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'sourceOfIncome'|translate" [options]="sourceOfIncome" [selectModel]="quickForm.get('sourceOfIncome')?.valueChanges|async" (selectModelChange)="quickForm.get('sourceOfIncome')?.setValue($event)"/>
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'pureOwnership'|translate" [options]="totalAssetsRange" [selectModel]="quickForm.get('pureOwnership')?.valueChanges|async" (selectModelChange)="quickForm.get('pureOwnership')?.setValue($event)"/> 
  
                  <app-npx-dropdown [btnClassProps]="'rounded-3xl'" [dropdownClassProps]="'w-full'" [placeholder]="'nationality'|translate" [options]="nationalities" [selectModel]="quickForm.get('nationality')?.valueChanges|async" (selectModelChange)="quickForm.get('nationality')?.setValue($event)"/>
                </div>
                <div class="grid grid-cols-3 items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold">{{"theboardofdirectorsofalistedcompany"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isPoliticallyExposedPerson')?.setValue(false)"  [ngClass]="!quickForm.get('isPoliticallyExposedPerson')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button (click)="quickForm.get('isPoliticallyExposedPerson')?.setValue(true)"  [ngClass]="quickForm.get('isPoliticallyExposedPerson')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
  
                <div class="grid grid-cols-3 items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2  font-semibold"> {{"Areyoupoliticallyexposed"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isAssociatedWithPEP')?.setValue(false)"    [ngClass]="!quickForm.get('isAssociatedWithPEP')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button (click)="quickForm.get('isAssociatedWithPEP')?.setValue(true)" [ngClass]="quickForm.get('isAssociatedWithPEP')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                  </button>
                  </div>
                </div>
  
                <div class="grid grid-cols-3  items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold"> {{"politicallyexposedperson"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isRealBeneficiary')?.setValue(false)" [ngClass]="!quickForm.get('isRealBeneficiary')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button  (click)="quickForm.get('isRealBeneficiary')?.setValue(true)" [ngClass]="quickForm.get('isRealBeneficiary')?.value?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
                <div class="grid grid-cols-3  items-start justify-start gap-[15px] w-full">
                  <span class="text-xl col-span-2 font-semibold">{{"therealbeneficiaryoftheaccount"|translate}}</span>
                  <div class="mx-[8px] flex flex-row items-center justify-start gap-[15px]">
                    <button (click)="quickForm.get('isBoardMember')?.setValue(false)" [ngClass]="!(quickForm.get('isBoardMember')?.value)?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px] hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                    {{ 'no' | translate }}
                    </button>
                    <button  (click)="quickForm.get('isBoardMember')?.setValue(true)" [ngClass]="(quickForm.get('isBoardMember')?.value)?'bg-[#054060bc] text-white border-[#015C8E]': ''" class="border border-gray-500 rounded-2xl h-[42px] w-[139px]  hover:bg-[#015C8E0D] hover:text-black hover:border-[#015C8E] transition-all ease-in-out duration-100">
                      {{ 'yes' | translate }}
                    </button>
                  </div>
                </div>
                <div class="  w-full
                  flex flex-row items-center justify-start gap-[15px]">
                    <app-input
                    [control]="quickForm.get('jobTitle')"
                    [label]="'jobTitle'"
                    [type]="'text'"
                    [autofocus]="true"
                  />
                </div>
                <div class="  w-full
                flex flex-row items-center justify-start gap-[15px]">
                  <app-input
                  [control]="quickForm.get('companyName')"
                  [label]="'companyName'"
                  [type]="'text'"
                  [autofocus]="true"
                />
              </div>
  
                <div class="w-full
                  flex flex-row items-center justify-start gap-[15px]">
                    <app-input
                    [control]="quickForm.get('companyAddress')"
                    [label]="'companyAddress'"
                    [type]="'text'"
                    [autofocus]="true"
                  />
                </div>
          
  
                <div class="flex  items-start justify-end mt-[15px] w-full">
                  <button
                  (click)="sendRequest()"
                  class="border border-gray-500 rounded-2xl dark:bg-black-light dark:text-black h-[42px] w-[250px] flex items-center justify-center gap-[15px] text-black bg-gray-100  hover:bg-[#015C8E] hover:text-black hover:border-[#015C8E ] transition-all ease-in-out duration-100 disabled:cursor-not-allowed">
                    {{'Save'|translate}}
                    <img src="assets/images/arrownext.svg" class="text-black" alt="arrow">
                  </button>
                </div>
              </div>
  
            }
            @case(2 ){
              <app-company-layout [update]="notCompleteUser()" (changeStatus)="changeStatus($event)"></app-company-layout>
            }
            @case(3){
               <app-company-layout [update]="notCompleteUser()" (changeStatus)="changeStatus($event)"></app-company-layout>
              <!-- <app-borrower (closeStatuts)="complateBorrower()"/> -->
            }
          }
        }
      }

    }@else {
      <router-outlet></router-outlet>
    }

  </div>

</main>
<!-- Footer -->
<footer class="footer bg-n0 dark:bg-bg4">
  <div class="flex flex-col items-center justify-center gap-3 px-4 py-5 lg:flex-row lg:justify-between xxl:px-8">
    <p class="text-sm max-md:w-full max-md:text-center lg:text-base">
      Copyright &#64; <span>{{ getCurrentYear() }}</span>
      <a class="text-primary" routerLink="/"> MDDPlus </a>
    </p>
    <div class="justify-center max-md:flex max-md:w-full"></div>
    <ul class="flex gap-2 xxxl:gap-3">
      <li>
        <a aria-label="social link"
           class="inline-flex rounded-full border border-primary p-1 text-primary duration-300 hover:bg-primary hover:text-n0 md:p-1.5 xxxl:p-2"
           href="#">
          <i class="lab la-facebook-f"></i>
        </a>
      </li>
   <li>
  <a aria-label="social link"
     class="inline-flex rounded-full border border-primary p-1 text-primary duration-300 hover:bg-primary hover:text-n0 md:p-1.5 xxxl:p-2"
     href="#">
    <svg xmlns="http://www.w3.org/2000/svg" 
         x="0px" 
         y="0px" 
         width="25" 
         height="25" 
         viewBox="0 0 50 50"
         class="fill-current"> <!-- Add fill-current to inherit text color -->
      <path d="M 5.9199219 6 L 20.582031 27.375 L 6.2304688 44 L 9.4101562 44 L 21.986328 29.421875 L 31.986328 44 L 44 44 L 28.681641 21.669922 L 42.199219 6 L 39.029297 6 L 27.275391 19.617188 L 17.933594 6 L 5.9199219 6 z M 9.7167969 8 L 16.880859 8 L 40.203125 42 L 33.039062 42 L 9.7167969 8 z"></path>
    </svg>
  </a>
</li>
      <li>
        <a aria-label="social link"
           class="inline-flex rounded-full border border-primary p-1 text-primary duration-300 hover:bg-primary hover:text-n0 md:p-1.5 xxxl:p-2"
           href="#">
          <i class="lab la-skype"></i>
        </a>
      </li>
      <li>
        <a aria-label="social link"
           class="inline-flex rounded-full border border-primary p-1 text-primary duration-300 hover:bg-primary hover:text-n0 md:p-1.5 xxxl:p-2"
           href="#">
          <i class="lab la-instagram"></i>
        </a>
      </li>
      <li>
        <a aria-label="social link"
           class="inline-flex rounded-full border border-primary p-1 text-primary duration-300 hover:bg-primary hover:text-n0 md:p-1.5 xxxl:p-2"
           href="#">
          <i class="lab la-linkedin-in"></i>
        </a>
      </li>
    </ul>
    <ul class="flex gap-3 text-sm max-lg:w-full max-lg:justify-center lg:gap-4 lg:text-base">
      <li>
        <a href="#">Privacy Policy</a>
      </li>
      <li>
        <a href="#">Terms of condition</a>
      </li>
    </ul>
  </div>
</footer>
<app-theme-customizer/>
