import { Component, ElementRef, EventEmitter, Output, ViewChild, AfterViewInit, ViewChildren ,QueryList,ChangeDetectorRef,Input } from '@angular/core';

import { TranslateModule } from "@ngx-translate/core";
import { ModalComponent } from '@component/form/modal/modal.component';
import { Async<PERSON>ip<PERSON>, NgClass, NgForOf } from '@angular/common';
import { FormBuilder, FormGroup, Validators, FormArray, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { OtpService } from '../../../shared/otp/services/otp.service';
import { MDModalModule } from '@component/form/modals';
import { ModalService } from '@component/form/modals/modal.service';

@Component({
  selector: 'app-change-status',
    standalone: true,
  imports: [
    MDModalModule,
    TranslateModule,
  
    NgForOf,
  
    ReactiveFormsModule,
    CommonModule
  ],

  templateUrl: './change-status.component.html',
  styleUrls: ['./change-status.component.css']
})
export class ChangeStatusComponent implements AfterViewInit {
  form!: FormGroup;
  otpForm!: FormGroup;
  isOtpVisible = false;
  selectedStatus: any;
  @ViewChild('submitButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() otpConfirmed = new EventEmitter<string>();
  @Output() otpEntered = new EventEmitter<string>();
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef>;
  @Input() modalTitle: string = '';
  @Input() modalDescription: string = '';
  fieldCount: number = 6; // Number of OTP fields
  countdown: number = 30; // Initial countdown value (in seconds)
  countdownInterval: any; // To store the interval reference
  constructor(private cdr: ChangeDetectorRef,
  private OTPService: OtpService, private fb: FormBuilder, protected modalService: ModalService) { }
  ngOnInit(): void {
    this.form = this.fb.group({
      notes: ['', [Validators.required, Validators.minLength(5)]],
    });
    this.otpForm = this.fb.group({
      otp: this.fb.array(new Array(this.fieldCount).fill('').map(() => this.fb.control(''))),
    });
  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges(); 
    this.otpInputs.changes.subscribe(() => {
      setTimeout(() => {
        this.focusInput(0);
      });
    });
  }
  get otpArray() {
    return this.otpForm.get('otp') as FormArray;
  }

  handleKeyDown(event: KeyboardEvent, index: number): void {
    const otpArray = this.otpForm.get('otp') as FormArray;

    if (!/^[0-9]{1}$/.test(event.key) && event.key !== 'Backspace' && event.key !== 'Delete' && event.key !== 'Tab') {
      event.preventDefault();
    }

    if (event.key === 'Backspace' || event.key === 'Delete') {
      otpArray.at(index).setValue('');
      if (index < this.fieldCount - 1) {
        this.focusInput(index + 1); // Move focus to the right on delete
      }
    }
  }

  handleInput(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const otpArray = this.otpForm.get('otp') as FormArray;

    if (input.value) {
      if (index < 5) {
        this.focusInput(index + 1); // Move left when entering input
      } else {
        this.submitButton.nativeElement.focus();
      }
    }
  }

  handlePaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    const numbers = pastedData.replace(/\D/g, '').slice(-this.fieldCount); // Get only last 6 digits
    const otpArray = this.otpForm.get('otp') as FormArray;

    if (numbers.length === this.fieldCount) {
      [...numbers].reverse().forEach((num, i) => {
        otpArray.at(i).setValue(num);
      });
      this.submitButton.nativeElement.focus();
    }
  }

  private focusInput(index: number): void {
    if (!this.otpInputs || this.otpInputs.length === 0) {
      setTimeout(() => this.focusInput(index), 50); // Retry after 50ms if elements are not ready
      return;
    }
    
    const input = this.otpInputs.toArray()[index]?.nativeElement;
    if (input) {
      input.focus();
      input.select();
    }
  }
  sendOtp(): void {
    this.OTPService.send({ operationType: 30 })
  }

  closeModal(): void {
    this.isOtpVisible = false;
    this.modalService.close("changeStatusModal");
  }
  startCountdown(): void {
    this.countdownInterval = setInterval(() => {
      if (this.countdown > 0) {
        this.countdown--;
      } else {
        clearInterval(this.countdownInterval); // Stop the countdown when it reaches 0
      }
    }, 1000);
  }

  rejectRequest(): void {
    this.isOtpVisible = true;
    this.selectedStatus = 'Rejected';
    this.sendOtp();

  }

  acceptRequest(): void {
    this.isOtpVisible = true;
    this.selectedStatus = 'Accepted';
    this.sendOtp();
    this.startCountdown()
  }
  submitForm(): void {
    if (this.form.valid && this.otpForm.valid) {
      const requestData = {
        requestId: "",
        status: this.selectedStatus,
        notes: this.form.value.notes,
        otp: this.otpForm.value.otp.join(''),
      };

      this.modalService.closeWithResult('changeStatusModal', requestData);
    } else {
    
    }
  }

  

  
}
