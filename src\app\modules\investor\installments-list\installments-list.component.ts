import { Component } from '@angular/core';
import { colDef, DataTableModule } from "@bhplugin/ng-datatable";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { Router, RouterLink } from "@angular/router";
import { installmentsService } from "./service/installments.service";
import { DatePipe, DecimalPipe, NgClass, CommonModule } from "@angular/common";
import { ModalService } from "@component/form/modals/modal.service";
import { ToastService } from "../../../core/services/toast.service";
import { AuthService } from '../../auth/services/auth.service';
import { ILoanRequestSearch } from './models/installment.model';
import { CustomFilterComponent } from '../../../shared/modals/custom-filter/custom-filter.component';



@Component({
  selector: 'app-installments-list',
  standalone: true,
  imports: [
    DataTableModule,
    DecimalPipe,
    TranslateModule,
    NgClass,
    CustomFilterComponent,
    CommonModule
  ],
  providers: [DatePipe],
  templateUrl: './installments-list.component.html',
  styleUrl: './installments-list.component.css'
})
export class InstallmentsListComponent {
  columns: Array<colDef> = [];
  search: ILoanRequestSearch = { isServerMode: true } as ILoanRequestSearch;
  loanRequests: any = [];
  params: any = {}
  response: any
  filterResult: any
  isFiltered: boolean = false
  statusId: any
  userList: any
  fields: any

  hasLoanRequestViewPermission = this.adminService.hasPermission('LoanRequest.installment.View')
  constructor(
    public translate: TranslateService,
    protected loanRequestService: installmentsService,
    private router: Router,
    private toast: ToastService,
    protected modalService: ModalService,
    private datePipe: DatePipe,
    private adminService: AuthService
  ) {
    this.loanRequestService.initPagination();
  }

  openLoanRequestCreateModal = () => this.modalService.open('app-loan-request-create')
  StatusMap: { [key: number]: string } = {
    0: 'Draft',
    1: 'pending',
    2: 'Overdue',
    3: 'Paid',
  };
  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }

  async ngOnInit() {
    this.columns = [
      { title: this.translate.instant('loanRequestNo'), field: 'loanRequestNo', },
      { title: this.translate.instant('DaysRemaining'), field: 'dueDays', },
      { title: this.translate.instant('RequestedAmount'), field: 'amount', },
      { title: this.translate.instant('DueDate'), field: 'dueDate', },//
      { title: this.translate.instant('Status'), field: 'status', },
      { title: this.translate.instant('Action'), field: 'Action', },
    ]
   
    await this.getList();
  }

  async getList() {
    if (!this.hasLoanRequestViewPermission) return
    try {
      let data = await this.loanRequestService.getList(this.params, this.search) ?? [];
      this.loanRequests = Array.isArray(data.data) ? data.data : [];
      this.loanRequestService.pagination.totalRows = data.totalRecords
    } catch (e: any) {
      this.toast.error(e);
    }
  }

  async payInstallment(id: any) {
    try {
      await this.loanRequestService.payInstallment(id).then(() => {
        this.toast.success("Sent Successfully")
        this.getList();
      })
    } catch (error) {
      this.toast.error(`Something wrong please try later ,${error}`)
    }

  }
  searchOnTale(term: string) {
    const filteredData = this.loanRequests.filter((item: any) =>
      Object.values(item).some((value: any) =>
        String(value).toLowerCase().includes(term.toLowerCase())
      )
    );
    this.loanRequests = filteredData;
  }

  async onInputChange(e: any) {
    const inputElement = e.target as HTMLInputElement;
    let searchVal = inputElement.value;
    const params = { PageNumber: 1, pageSize: 15, find: inputElement.value }
    if (!this.hasLoanRequestViewPermission) return
    this.response = await this.loanRequestService.getList(
      params
      , this.search);
    this.loanRequests = this.response.data
    this.loanRequestService.pagination.totalRows = this.response.totalRecords - 1
    if (searchVal.length == 0) {
      this.getList();
    } else this.searchOnTale(searchVal);
  }
  async openModal() {
    try {

      const result = await this.modalService.openWithResult('customFilterModal');
      if (result) {
        this.filterResult = result;
        if (result.status == null && result?.toDate == null && result?.fromDate == null && result?.Name == null && result.loanRequestNo == null) {
          this.params = {}
          let response = await this.loanRequestService.getList({}, this.search);
          this.loanRequests = response.data;
          this.isFiltered = false
          return
        }
        this.mappValue(result.status)
        this.params = {
          status: parseInt(this.statusId),
          toDueDate: result.toDate ? result.toDate : null,
          fromDueDate: result.fromDate ? result.fromDate : null,
          name: result.Name,
          loanRequestNo: result.loanRequestNo,
        };
        this.isFiltered = true
        let response = await this.loanRequestService.getList(this.params, this.search);
        this.loanRequests = response.data;
      }
    } catch {
    }
  }

  calculateRemainingDays(dueDateString: string): number {
    const dueDate = new Date(dueDateString);
    const currentDate = new Date();

    // Remove time from both dates for accurate day count
    dueDate.setHours(0, 0, 0, 0);
    currentDate.setHours(0, 0, 0, 0);

    const timeDiff = dueDate.getTime() - currentDate.getTime();
    const dayDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    // Never return negative values
    return Math.max(dayDiff, 0);
  }
  mappValue(status: string) {
    switch (status) {
      case "pending":
        this.statusId = 1
        break;
      case "Overdue":
        this.statusId = 2
        break;
     
      case "معلق":
        this.statusId = 1
        break;
      case "دفعة متأخرة":
        this.statusId = 2
        break;
      case "Overdue":
        this.statusId = 2
        break;
    
      default:
        break;
    }
  }



}
