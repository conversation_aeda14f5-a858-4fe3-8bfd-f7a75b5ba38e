import {Component} from '@angular/core';
import {ButtonComponent} from "@component/form/button/button.component";
import {InputComponent} from "@component/form/input/input.component";
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {phoneNumberValidator} from "../../../shared/validators/phone.validator";
import {AuthService} from "../services/auth.service";

import {ModalService} from "@component/form/modals/modal.service";

import { OtpService } from '../../../shared/otp/services/otp.service';
import { OtpModalComponent } from '../../../shared/otp/otp-modal/otp-modal.component';
import { forgetModalComponent } from '../forget-popus/forget-popup/forget-modal.component';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [
    
    ReactiveFormsModule,
    TranslateModule,
    OtpModalComponent,
    forgetModalComponent
],
  templateUrl: './forgot-password.component.html',
  styleUrl: './forgot-password.component.css'
})
export class ForgotPasswordComponent {
  formGroup!: FormGroup;
  token?: any;
  isPhone: boolean = false
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private otpService: OtpService,
    protected modalService: ModalService,
  ) {
    this.formGroup = this.fb.group({
      mobile: [null, [Validators.required,]],
    });
  }

  ngOnInit() {
    this.modalService.close('forget-modal');
  }

  async onSubmit() {
    if (this.formGroup.valid) {

      const body = {
        mobile: this.formGroup.value.mobile.userValue
      }
      this.token = await this.authService.forgotPassword2(body)
      localStorage.setItem('token', this.token.data);
      this.formGroup.reset()
      this.modalService.close('forget-modal');
      this.modalService.open('otp-modal')
    } else {
      this.formGroup.markAllAsTouched()
    }
  }

  openForgetModalIqamma = () => {this.modalService.open('forget-modal');this.isPhone =false}
  openForgetModalPhone = () => {this.modalService.open('forget-modal');this.isPhone =true}
  submitEnteredValue = (value: string) => { this.formGroup.patchValue({mobile: value});this.onSubmit()}
  async verifyOtp(otp: string) {
    if (this.token) {
      try {
        const body = {
          token: this.token.data ?? localStorage.getItem('token'),
          code: otp,
        }
        const token = await this.authService.forgotPasswordValidateOTP2(body);
        localStorage.setItem('token', token.data ?? '');
        this.modalService.close('otp-modal')
        this.router.navigate(['/auth/reset-password'], {state: {token: token.data ?? localStorage.getItem('token')}});

      } catch (error: any) {
      }
    }
  }
}
