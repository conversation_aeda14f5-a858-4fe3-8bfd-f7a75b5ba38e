import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {WithdrawalsListComponent} from "./withdrawals-list/withdrawals-list.component";
import {WithdrawalsCreateComponent} from "./withdrawals-create/withdrawals-create.component";

const routes: Routes = [
  {
    path: '',
    component: WithdrawalsListComponent,
  },
   {
    path: 'create',
    component: WithdrawalsCreateComponent,
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class WithdrawalsRoutingModule {
}
