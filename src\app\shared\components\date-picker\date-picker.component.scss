



// input:focus {
//   box-shadow:  0 1px 1px rgba(0, 0, 0, 0.075),
//   0 0 0 3px rgba(0, 126, 255, 0.1);
// }
.active {
  background-color: #F2F7F9 !important;
}

input.error {
  @apply border-danger bg-danger/[0.08]  placeholder-danger/70 focus:border-danger text-danger #{!important};
}


input {
  @apply form-input px-3
  rounded-3xl
  border-slate-200
  placeholder:text-slate-400
  focus:outline-none
  focus:border-primary-500
  disabled:bg-slate-100
  disabled:border-slate-300
  disabled:text-slate-500
  dark:border-zink-500
  dark:text-zink-100
  dark:bg-zink-700
  dark:focus:border-primary-500
  dark:placeholder:text-zink-200
  dark:disabled:bg-zink-600
  dark:disabled:border-zink-500
  dark:disabled:text-zink-200  #{!important};
}

:host ::ng-deep .flatpickr-weekdays
{
  height: 60px;
}
/* تغيير لون خلفية التقويم */
:host ::ng-deep  .flatpickr-calendar.open {
  border-radius: 12px; /* زوايا مستديرة */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  border: none;
  padding: 16px !important;
}

/* تغيير لون شريط العنوان (الشهر والسنة) */
:host ::ng-deep .flatpickr-months {
  background: #1E1E2F;
  color: #fff;
  font-size: 16px;
}
:host ::ng-deep .flatpickr-months .flatpickr-prev-month
{
  top:9px !important;
}

:host ::ng-deep .dayContainer {
  padding: 13px;}


