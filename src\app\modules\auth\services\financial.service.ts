import { Injectable } from '@angular/core';
import { financialInformationModel } from '../models/financial-data.model';
import { BaseResponse, ResultResponse } from '../models/api-response.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from '../../../core/services/api.service';
import { environment } from '../../../../environments/environment';
@Injectable({
  providedIn: 'root',
})
export class FinancialService {
  
  private financialInformationPath = environment.baseUrl+'/investor/financial-informations';
  constructor(private http: HttpClient) {}

  financialInformation(): Observable<BaseResponse<financialInformationModel>> {
    return this.http.get<BaseResponse<financialInformationModel>>( 
      this.financialInformationPath
    );
  }

  create(model: financialInformationModel): Observable<ResultResponse> {
    return this.http.post<ResultResponse>(this.financialInformationPath, model);
  }

  update(model: financialInformationModel): Observable<ResultResponse> {
    return this.http.put<ResultResponse>(this.financialInformationPath, model);
  }
}
