import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {OpportunityListComponent} from "./opportunity-list/opportunity-list.component";
import { OpportunityDetailComponent } from './opportunity-detail/opportunity-detail.component';

const routes: Routes = [
  {
    path: '',
    component: OpportunityListComponent,
  },
  {
    path: ':id',
    component: OpportunityDetailComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OpportunitiesRoutingModule {
}
