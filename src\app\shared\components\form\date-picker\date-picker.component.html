@if (label) {
<label
  [for]="control?.name ?? placeholder"
  class="inline-block mb-2 text-base font-medium"
>
  {{ label | translate }}
  @if (required) {
  <span class="text-red-500"> *</span>
  }
</label>
} @if(type == 'h') {
<button
  class="btn btn-outline-primary calender"
  (click)="d.toggle()"
  type="button"
>
  <i class="las la-calendar"></i>
</button>
<input
  [placeholder]="
    placeholder ? (placeholder | translate) : label ? (label | translate) : ''
  "
  name="dp"
  [formControl]="control"
  ngbDatepicker
  #d="ngbDatepicker"
/>
<!-- <div class="input-group">
    <input [id]="dp" [name]="dp" class="form-control" [placeholder]="placeholder" name="dp" [formControl]="control" ngbDatepicker #dp="ngbDatepicker" container="body" [firstDayOfWeek]="7">
    <div class="input-group-append">
      <button class="btn btn-outline-secondary" (click)="dp.toggle()" type="button">
        <i class="las la-calendar" style="width: 1.2rem; height: 1rem; cursor: pointer;"></i>
      </button>
    </div>
  </div> -->
} @else{
<button class="geo-calender calender">
  <i class="las la-calendar"></i>
</button>
<input
  [formControl]="control"
  [placeholder]="
    placeholder ? (placeholder | translate) : label ? (label | translate) : ''
  "
  [readonly]="readonly"
  data-provider="flatpickr"
  mwlFlatpickr
  type="date"
/>

}
