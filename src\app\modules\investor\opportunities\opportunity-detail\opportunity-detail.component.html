<div class="mb-8 pb-6 bb-dashed items-center dark:bg-inherit">
  <!-- Header Section -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <div class="md:col-span-2 h4 header text-xl font-bold text-center md:text-right ltr:text-left">
      {{"opportunity" |translate}} {{ OpportunityData?.title | translate }}
    </div>

    <div class="flex flex-col sm:flex-row justify-center md:justify-end items-center gap-2 dark:text-danger-50">
      <span class="flex items-center border font-medium justify-center w-full sm:w-32 
       rounded-lg px-4 py-2">
     
 @if (OpportunityData?.status === OpportunityStatus.Open) {
  <span class="border-[#EBECEF] text-[#19AD27]">
    <i class="las la-lock-open"></i>
    {{ OpportunityData?.statusName | translate }}
  </span>
}
@if (OpportunityData?.status === OpportunityStatus.Published) {
  <span class="border-[#FFBC0D] text-[#FFBC0D]">
       <i class="las la-hourglass-start"></i>
    {{ OpportunityData?.statusName | translate }}
  </span>
}
@if (OpportunityData?.status === OpportunityStatus.Closed) {
  <span class="border-red-500 text-red-600">
<i class="las la-lock"></i>
    {{ OpportunityData?.statusName | translate }}
  </span>
}
@if (OpportunityData?.status === OpportunityStatus.Expired) {
  <span class="border-[#EBECEF] text-[#3498db]">
   <i class="las la-hourglass-end"></i>
    {{ OpportunityData?.statusName | translate }}
  </span>
}
@if (OpportunityData?.status === OpportunityStatus.Cancelled) {
  <span class="border-[#EBECEF] text-orange-600">
   <i class="las la-times-circle"></i>
    {{ OpportunityData?.statusName | translate }}
  </span>
}

      </span>
      <span
        class="flex items-center justify-center w-full sm:w-40 rounded-lg border-2 bg-[#015C8E80] text-white px-4 py-2">
        <i class="las la-boxes"></i>
        {{ "PurshaseOrder" | translate }}
      </span>
    </div>
  </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
  <!-- Left Column -->
  <div class="space-y-6">
    <!-- Opportunity Card -->
    <div class="card p-4 bg-white dark:bg-inherit dark:text-danger-50 rounded-xl shadow-sm">
      <div class="relative">
        <!-- Character circle - positioned absolutely for better responsive behavior -->
        <div
          class="absolute -top-8  md:right-[29rem] md:left-[25rem] flex w-16 h-16 md:w-18 md:h-18 left-[22rem]
  right-[26rem]
 rounded-full border-2 bg-white  dark:bg-inherit dark:text-danger-50 text-4xl font-bold items-center justify-center z-10">
          {{ OpportunityData?.scoreName || "A" }}
        </div>

        <!-- Card container -->
        <div class="pt-8 pb-6 px-4 md:px-8 bg-[#015C8E0D] rounded-2xl shadow-md border border-gray-200">
          <!-- Card title -->
          <h2 class="text-2xl md:text-3xl font-extrabold text-center mb-6">
            {{ OpportunityData?.title || "0ID-1589-3099" }}
          </h2>

          <!-- Card content -->
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <div class="text-black  dark:text-danger-50 font-bold text-lg">{{ "startinvestment" | translate }}</div>
              <span class="text-black dark:text-danger-50  font-bold text-lg">{{ OpportunityData?.amount || "2,500,000.00" }}</span>
            </div>

            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div class="bg-black h-2.5 rounded-full"
                [style.width.%]="calculateProgressWidth(OpportunityData?.amount || 2500000)"></div>
            </div>

            <div class="grid grid-cols-1 gap-8 mt-3">


              <div class="flex justify-between items-center">
                <span class="text-black dark:text-danger-50  font-semibold">{{"investmentper" | translate}}</span>
                <span class="text-black dark:text-danger-50  font-bold">{{ OpportunityData?.profits ||0}}%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-black dark:text-danger-50  font-semibold">{{"Returnoninvestment" |translate}}</span>
                <span class="text-black dark:text-danger-50  font-bold">{{ OpportunityData?.profitsPer || 0 }}%</span>
              </div>



              <div class="flex justify-between items-center">
                <span class="text-black dark:text-danger-50  font-semibold">{{"Peroid" | translate}}</span>
                <span class="text-black dark:text-danger-50  font-bold">{{ OpportunityData?.durationInMonths ||("notfound"|translate) }} {{""}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Details Section -->
    <div class="bg-white  dark:bg-inherit p-4 md:p-6 rounded-xl shadow-sm">
      <div class="space-y-4">
        <div class="flex justify-between">
          <span class="font-semibold">{{ "Valueaddedtax" | translate }}</span>
          <span class="font-bold">15%</span>
        </div>

        <div class="flex justify-between">
          <span class="font-semibold">{{ "AnnualYield" | translate }}</span>
          <span class="font-bold">14.4%</span>
        </div>
@if(OpportunityData?.expectedDueDate!== "0001-01-01T00:00:00"){
    <div class="flex justify-between">
          <span class="font-semibold">{{ "Expectedduedate" | translate }}</span>
          <span class="font-bold">{{(OpportunityData?.expectedDueDate	|dateFormat) ||("notfound" |translate)}}</span>
        </div>
}
    

        <div class="flex justify-between">
          <span class="font-semibold">{{ "Guarantees" | translate }}</span>
          <span class="font-bold">{{"Promissorynote"|translate}}</span>
        </div>
      </div>
    </div>

    <!-- Investment Form -->
     @if((OpportunityData?.statusName === "Open"  && (investstatusName == "Cancelled" &&listOfSelectedItems?.length>0 ))||(OpportunityData?.statusName === "Open"&&listOfSelectedItems?.length==0) ){
 <div class="bg-white dark:bg-inherit p-4 md:p-6 rounded-xl shadow-sm">
      <h2 class="text-xl font-extrabold text-center border-b-2 border-[#015C8E] border-dashed pb-4 mb-6">
        {{ "Toinvestintheopportunity" | translate }}
      </h2>

      <div class="space-y-4">
<form [formGroup]="amountopportunityForm" *ngIf="amountopportunityForm ">
  <label class="block font-medium">{{ "InvestmentAmount" | translate }}</label>
  <input 
    type="number" 
   
    placeholder="{{ 'InvestmentAmount' | translate }}"
    class="w-full px-4 py-3 rounded-2xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#015C8E]"
    formControlName="amount">

  
<div *ngIf="(amountopportunityForm.get('amount')?.invalid || 
            (OpportunityData?.investableAmount < 1000 && amountopportunityForm.get('amount')?.value != OpportunityData?.investableAmount) || 
            (OpportunityData?.investableAmount >= 1000 && (amountopportunityForm.get('amount')?.value > BalanceCount || amountopportunityForm.get('amount')?.value < 1000))) && 
            (amountopportunityForm.get('amount')?.dirty || amountopportunityForm.get('amount')?.touched)"
     class="text-red-500 text-sm mt-1">
     
  <!-- Required field validation -->
  <div *ngIf="amountopportunityForm.get('amount')?.errors?.['required']">
    {{ 'InvestmentAmountRequired' | translate }}
  </div>
  
  <!-- Case 1: investableAmount < 1000 (must be exact amount) -->
  <div *ngIf="OpportunityData?.investableAmount < 1000 && 
             amountopportunityForm.get('amount')?.value != OpportunityData?.investableAmount &&
             amountopportunityForm.get('amount')?.value">
    {{ 'AmountMustBeExactly' | translate }} {{ OpportunityData?.investableAmount | number }} SAR
  </div>
  
  <!-- Case 2: investableAmount >= 1000 (min 1000) -->
  <div *ngIf="OpportunityData?.investableAmount >= 1000 && 
             amountopportunityForm.get('amount')?.value < 1000">
    {{ 'InvestmentAmountMinimum' | translate }}
  </div>
  
  <!-- Insufficient balance (for both cases) -->
  <div *ngIf="amountopportunityForm.get('amount')?.value > BalanceCount">
    {{ 'InsufficientBalance' | translate }}
  </div>
</div>

  <button 
    class="w-full bg-[#015C8E] mt-4 text-white py-3 rounded-lg font-medium hover:bg-[#014a74] transition-colors disabled:bg-[#3758894b] disabled:pointer-events-none"
    (click)="investInOpportunity()"
    [disabled]="!amountopportunityForm || amountopportunityForm.invalid || (BalanceCount < 1000)||(amountopportunityForm.get('amount')?.value>BalanceCount)">
    {{ "save" | translate }}
  </button>
</form>

    
      </div>
    </div>
     }
    
     <!---------------cancel investment--------------------->
    @if(investstatusName	== "Open" && listOfSelectedItems?.length >0 ){
    <div class="bg-white p-4 md:p-6 rounded-xl shadow-sm">
      <h2 class="text-xl font-extrabold text-center border-b-2 border-[#015C8E] border-dashed pb-4 mb-6">
        {{ "CancelInvestmentdesc" | translate }} {{ amountinvestment}} {{"SAR" |translate}} {{"In" |translate}} {{investDate | date: 'dd/MM/yyyy'}}
      </h2>
      @if (OpportunityData?.status === OpportunityStatus.Open) {
        <div class="space-y-4">
          <button 
            class="w-full bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition-colors"
            (click)="cancelInvestment()">
            {{ "CancelInvestment" | translate }}
          </button>
        </div>
      }

    </div>
    }
   
  </div>

  <!-- Right Column - Tabs -->
  <div class="bg-[#B9BBBD1A] p-3 md:p-3 rounded-xl shadow-sm">
    <!-- Tabs Navigation -->
    <div class="border-b border-gray-200 mb-6">
      <ul class="flex flex-wrap -mb-px">
        <li *ngFor="let tab of tabs" class="me-2">
          <a (click)="changeTab(tab)"
            [ngClass]="{'text-[#015C8E] border-[#015C8E]': activeTab.field === tab.field, 'text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab.field !== tab.field}"
            class="inline-block p-3 border-b-2 rounded-t-lg font-medium cursor-pointer transition-colors dark:text-inherit">
            {{ tab.title! | translate }}
          </a>
        </li>
      </ul>
    </div>

    <!-- Tab Content -->
    <div class="min-h-[300px] dark:text-inherit">
      @switch (activeTab.field) {
      @case("companyInformation") {
      <app-opportunity-company-details [OpportunityId]="OpportunityId"></app-opportunity-company-details>
      }
      @case("Riskreport") {
     <app-opportunity-financial-information [OpportunityId]="OpportunityId"></app-opportunity-financial-information>
      }
      }
    </div>
  </div>
</div>


@if (modalService.isOpen('otp-create-amount-modal')) {
  <app-otp-modal
    [id]="'otp-create-amount-modal'"
    (submitOtp)="createamount($event)"
  />
}
@if (modalService.isOpen('otp-cancel-amount-modal')) {
  <app-otp-modal
    [id]="'otp-cancel-amount-modal'"
    (submitOtp)="CancelInvestment($event)"
  />
}