<ng-modals [id]="id" [placement]="'modal-top'"
           className="fixed flex flex-col transition-all md:w-[30rem] duration-300 ease-in-out left-2/4 z-50 -translate-x-2/4 translate-y-8">
  <div class="flex justify-center">
    <div class="relative max-w-md mx-auto text-center bg-white px-4 sm:px-8 py-10 rounded-xl shadow"> <!-- Added 'relative' class here -->
      <!-- Close button added here -->
      <button (click)="closeModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      
      <header class="mb-8">
        <h1 class="text-2xl font-bold mb-4 text-center">{{ title | translate }}</h1>
        <p class="md:mb-6 pb-4 mb-4 md:pb-6 bb-dashed text-sm md:text-base text-center">{{ subTitle | translate }}</p>
      </header>
      <form [formGroup]="userForm" class="w-full " id="forget-form">
        <div class="flex w-full items-center justify-center gap-3 mb-[15px]">
          <input (focus)="handleFocus($event)" 
                 (input)="handleInput($event)"
                 (keydown)="handleKeyDown($event)"
                 (paste)="handlePaste($event)" 
                 formControlName="userValue"
                 [id]="'user-input'"
                 class="h-[48px] w-[481px] rounded-[32px] text-center font-extrabold text-slate-900 border border-[#EBECEF] appearance-none py-[12px] px-[24px] outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100 placeholder:text-[12px] text-[12px]"
                 placeholder="{{placeholder | translate}}"
                 type="text">
        </div>
        <button
          #submitButton
          (click)="verify()"
          [disabled]="(loadingBar.value$ | async ) || !userForm.valid"
          class="w-[293px] h-[40px] mt-4 inline-flex justify-center whitespace-nowrap rounded-[32px] bg-[#015C8E] px-[24px] py-[12px] text-sm font-medium text-white shadow-sm shadow-indigo-950/10 hover:bg-[#015C8f80] focus:outline-none focus:ring focus:ring-indigo-300 focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 transition-colors duration-150"
          type="submit">
          {{ 'verifyAccount' | translate }}
          <img src="assets/images/Arrow_.svg" class="mr-2" alt="arrow">
        </button>
      </form>
    </div>
  </div>
</ng-modals>