<div class="flex gap-4">
    <div class="box mb-4 flex-1 xxxl:mb-6">
      <form [formGroup]="phoneForm" class="flex flex-col space-y-4">
        <div class="col-span-2 md:col-span-1">
          <app-input
            [control]="phoneForm.get('newMobileNumber')"
            [label]="'phone'"
            [type]="'tel'"
          />
        </div>
        <div class="col-span-2 flex justify-end w-full gap-2 md:gap-6 mt-2">
          <button (click)="changeMobileNumber()" class="btn" type="submit">{{ 'save' | translate }}</button>
        </div>
      </form>
    </div>
    <div class="box mb-4 flex-1 xxxl:mb-6">
      <form [formGroup]="emailForm" class="flex flex-col space-y-4">
        <div class="col-span-2 md:col-span-1">
          <app-input
            [control]="emailForm.get('newEmailAddress')"
            [label]="'email'"
            [type]="'email'"
            [placeholder]="'Enter your new email address'"
          />
        </div>
        <div class="col-span-2 flex justify-end w-full gap-2 md:gap-6 mt-2">
          <button (click)="updateEmail()" class="btn" type="submit">{{ 'save' | translate }}</button>
        </div>
      </form>
    </div>
  </div>


  @if (modalService.isOpen('otp-modal')) {
    <app-otp-modal
      (submitOtp)="updateUserAccountsVerified($event)"
    />
  }
  @if (modalService.isOpen('otp-reset-email-modal')) {
    <app-otp-modal
      [id]="'otp-reset-email-modal'"
      (submitOtp)="resetEmail($event)"
    />
  }
  @if (modalService.isOpen('otp-confirm-reset-email-modal')) {
    <app-otp-modal
      [id]="'otp-confirm-reset-email-modal'"
      [title]="'emailVerification'"
      [subTitle]="'enterEmailVerificationCode'"
      (submitOtp)="confirmResetEmail($event)"
    />
  }
  @if (modalService.isOpen('otp-reset-mobile-modal')) {
    <app-otp-modal
      [id]="'otp-reset-mobile-modal'"
      [title]="'verifyCurrentPhoneNumber'"
      [subTitle]="'enterCurrentPhoneVerificationCode'"
      (submitOtp)="resetMobileNumber($event)"
    />
  }
  
  
  @if (modalService.isOpen('otp-confirm-reset-mobile-modal')) {
    <app-otp-modal
      [id]="'otp-confirm-reset-mobile-modal'"
      [title]="'verifyNewPhoneNumber'"
      [subTitle]="'enterNewPhoneVerificationCode'"
      (submitOtp)="confirmResetMobileNumber($event)"
    />
  }
  