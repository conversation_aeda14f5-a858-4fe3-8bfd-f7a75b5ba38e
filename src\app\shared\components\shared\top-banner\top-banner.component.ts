import {Component, EventEmitter, Input, Output} from '@angular/core'
import {DialogModule} from 'primeng/dialog'
import {TranslateModule} from "@ngx-translate/core";

// Recommended for animation support
@Component({
  selector: 'app-top-banner',
  standalone: true,
  imports: [DialogModule, TranslateModule],
  templateUrl: './top-banner.component.html'
})
export class TopBannerComponent {
  @Input() title: string = 'Dashboard';
  @Input() buttonText: string = 'Dashboard';
  @Output() onClick = new EventEmitter();
}
