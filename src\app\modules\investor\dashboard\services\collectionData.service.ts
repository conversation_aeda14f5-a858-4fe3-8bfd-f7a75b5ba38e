
import { Injectable } from '@angular/core';
import { EncryptStorage } from 'encrypt-storage';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private encryptStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken');

  constructor() {}

  // Set encrypted data
  setCollectionData(data: any): void {
    this.encryptStorage.setItem('CollectioncountData', data);
  }

  // Get encrypted data
  getCollectionData(): any {
    return this.encryptStorage.getItem('CollectioncountData');
  }

  // Clear data
  clearCollectionData(): void {
    this.encryptStorage.removeItem('CollectioncountData');
  }
}