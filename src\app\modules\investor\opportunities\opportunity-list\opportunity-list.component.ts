
import { Component, OnInit, signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Router } from '@angular/router';
import { ToastService } from '../../../../core/services/toast.service';
import { OpportunitiesService } from '../services/opportunities.service';
import { CommonModule, DatePipe } from '@angular/common';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { ModalService } from '@component/form/modals/modal.service';
import { CustomFilterComponent } from '../../../../shared/modals/custom-filter/custom-filter.component';
import { Opportunity } from '../models/opportunity';





interface PaymentRequestSearch {
  isServerMode: boolean;
  find?: string;
  // Add other search properties as needed
}

@Component({
  selector: 'app-opportunity-list',
    standalone: true,
  imports: [
      DataTableModule,
        CustomFilterComponent,
      TranslateModule,

      CommonModule
  ],
    providers: [
      DatePipe
    ],
  templateUrl: './opportunity-list.component.html',
  styleUrls: ['./opportunity-list.component.scss']
})
export class OpportunityListComponent implements OnInit {
  columns: Array<colDef> = [];
  IOpprtunity: Opportunity[] = [];
  
  search: PaymentRequestSearch = {
    isServerMode: false,
    find: ''
  };
    params:any
  response:any
    filterResult:any
  isFiltered:boolean=false
  readonly OpportunityStatus = {
    Published: 1,
    Open: 2,
    Closed: 3,
    Expired: 4,
    Cancelled: 6
  };

fields = [
  { 
    key: 'includeStatuses',
    label: 'Status',
    type: 'select',
    options: [
      { label: this.translate.instant('All'), value: null },          // means "no filter"
      { label: this.translate.instant('Published'), value: 1 },
      { label: this.translate.instant('Open'), value: 2 },
      { label: this.translate.instant('Closed'), value: 3 },
      { label: this.translate.instant('Expired'), value: 4 },
      { label: this.translate.instant('Cancelled'), value: 6 },
    ] 
  },
];


  opprtunityData: any[] = [];
  Opprtunitylist = signal<any[]>([]);

  constructor(
    public translate: TranslateService,
    public opportunitiesService: OpportunitiesService,
    private router: Router,
    private toast: ToastService,
    protected modalService: ModalService,
  ) {
    this.opportunitiesService.initPagination();
  }

  async ngOnInit() {
  
    await this.getList();
  }

  calculateProgressWidth(amount: number, maxAmount: number = 100000): number {
    return Math.min((amount / maxAmount) * 100, 100);
  }

private updateData(response: any) {
  const data = response?.data || [];
  this.IOpprtunity = data;
  this.Opprtunitylist.set(data);
  this.opprtunityData = data;
}
private buildSearchParams(): any {
  const statusId = this.filterResult?.includeStatuses !== undefined ? this.filterResult.includeStatuses : null;

  return {
    find: this.search.find || '',
    sort: null,
    pageNumber: this.opportunitiesService.pagination.page || 1,
    pageSize: this.opportunitiesService.pagination.pageSize || 1,
    includeStatuses: statusId !== null ? [statusId] : null
  };
}

async getList() {
  try {
    const params = this.buildSearchParams();
    const response = await this.opportunitiesService.getList(params);
    this.setOpportunitiesData(response);
    return response;
  } catch (e: any) {
    this.toast.error(e.error?.message || this.translate.instant('nodatarecordedfound'));
    this.setOpportunitiesData([]);
    throw e;
  }
}

// async getList() {
//   try {
//     const data = await this.opportunitiesService.getList(this.search);
//     this.setOpportunitiesData(data);
//     return data;
//   } catch (e: any) {
//     this.toast.error(e.error?.message || this.translate.instant('nodatarecordedfound'));
//     this.setOpportunitiesData([]);
//     throw e;
//   }
// }

async handleSearch(e: any) {
  const searchVal = (e.target as HTMLInputElement).value.trim();
  this.search.find = searchVal;
  this.opportunitiesService.initPagination();
  await this.getList();
}


// async handleSearch(e: any) {
//   const searchVal = (e.target as HTMLInputElement).value.trim();
  
//   // Clear previous results immediately
//   this.Opprtunitylist.set([]);
//   this.opprtunityData = [];
//   this.IOpprtunity = [];
  
//   this.search = {
//     find: searchVal, 
//     isServerMode: false
//   } as PaymentRequestSearch;
  
//   this.opportunitiesService.initPagination();
  
//   try {
//     await this.getList();
//   } finally {
//     this.search.isServerMode = false;
//   }
// }
public minValue(a: number, b: number): number {
  return Math.min(a, b);
}
  getStatusClass(status: number): string {
    switch (status) {
      case 1: return 'border-[#EBECEF] text-[#3498db]';
      case 2: return 'border-[#EBECEF] text-[#19AD27]';
      case 3: return 'border-[#EBECEF] text-[#e74c3c]';
      case 4: return 'border-[#EBECEF] text-[#f39c12]';
      case 6: return 'border-[#EBECEF] text-[#95a5a6]';
      default: return 'border-[#EBECEF] text-[#333333]';
    }
  }

  getStatusIcon(status: number): string {
    switch (status) {
      case 1: return 'las la-bullhorn';
      case 2: return 'las la-unlock';
      case 3: return 'las la-lock';
      case 4: return 'las la-clock';
      case 6: return 'las la-ban';
      default: return 'las la-question-circle';
    }
  }

  handlePageChange(page: number) {
    this.opportunitiesService.changePage(page);
    this.getList();
  }

  handlePageSizeChange(size: number) {
    this.opportunitiesService.changePageSize(size);
    this.getList();
  }

  openDetails(id: number) {
    this.router.navigate(['/opportunities', id]);
  }
private setOpportunitiesData(data: any[]) {
  const safeData = data || [];
  this.Opprtunitylist.set(safeData);
  this.opprtunityData = safeData;
  this.IOpprtunity = safeData;
}
async onInputChange(e: any) {
  const searchVal = (e.target as HTMLInputElement).value.trim();
  this.search.find = searchVal;

  if (!searchVal) {
    await this.getList();
    return;
  }

  await this.getList();
}


// async onInputChange(e: any) {
//   const searchVal = (e.target as HTMLInputElement).value.trim();

//   if (searchVal.length === 0) {
//     await this.getList();
//     return;
//   }

//   const params = { PageNumber: 1, pageSize: 15, find: searchVal };

//   try {
//     const response = await this.opportunitiesService.getList(params, this.search);
//     this.setOpportunitiesData(response);  // response is already data array from service
//     this.opportunitiesService.pagination.totalRows = response.length;
//   } catch (error) {
//     this.toast.error(this.translate.instant('searchFailed'));
//     this.setOpportunitiesData([]);
//   }
// }

//  async onInputChange(e: any) {
//   const inputElement = e.target as HTMLInputElement;
//   const searchVal = inputElement.value.trim();
  
//   // Clear previous results immediately when search starts
//   if (searchVal.length === 0) {
//     this.Opprtunitylist.set([]);
//     this.opprtunityData = [];
//     await this.getList();
//     return;
//   }

//   const params = { 
//     PageNumber: 1, 
//     pageSize: 15,
//     find: searchVal
//   };

//   try {
//     this.response = await this.opportunitiesService.getList(params, this.search);
    
//     // Update both signal and regular property with new data
//     this.Opprtunitylist.set(this.response.data || []);
//     this.opprtunityData = this.response.data || [];
//     this.IOpprtunity = this.response.data || [];
    
//     this.opportunitiesService.pagination.totalRows = this.response.totalRecords || 0;
//   } catch (error) {
//     this.toast.error(this.translate.instant('searchFailed'));
//     // Ensure empty state is shown on error
//     this.Opprtunitylist.set([]);
//     this.opprtunityData = [];
//     this.IOpprtunity = [];
//   }
// }

      searchOnTale(term: string) {
    const filteredData = this.IOpprtunity.filter((item: any) =>
      Object.values(item).some((value: any) =>
        String(value).toLowerCase().includes(term.toLowerCase())
      )
    );
    this.IOpprtunity = filteredData;
  }

 private statusNameToIdMap: {[key: string]: number} = {
    'Draft': 0,
    'Published': 1,
    'Open': 2,
    'Closed': 3,
    'Expired': 4,
    'Cancelled': 6
  };

  async openModal() {
    try {
      const result = await this.modalService.openWithResult('customFilterModal');
      // This will be handled by the filterApplied event
    } catch (error) {
      console.error('Modal error:', error);
    }
  }

  onFilterModalClosed() {
    if (!this.filterResult) {
      this.isFiltered = false;
    }
  }
async handleFilterResult(result: any) {
  if (!result || result.includeStatuses === null) {
    this.isFiltered = false;
    this.filterResult = null;
    await this.getList();
    return;
  }

  this.filterResult = result;
  this.isFiltered = true;

  await this.getList();
}


onModalClosed(result?: any) {
    this.modalService.close('customFilterModal');
    if (result) {
      this.handleFilterResult(result);
    }
  }




}