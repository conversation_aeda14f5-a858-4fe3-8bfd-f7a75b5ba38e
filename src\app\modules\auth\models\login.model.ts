export interface LoginForm {
  mobile?: string;
  password?: string;
  nationalId?: string,
  deviceToken?: string,
  accountType?: number

}

export interface IValidateLoginOTPBody {
  code: string,
  token: string
}

export interface IRegisterMobileForm {
  mobile: string;
}

export interface IResetPasswordForm {
  password: string,
  token: string,
}

export interface IChangePasswordForm {
  currentPassword: string,
  newPassword: string,
  mfaCode: number,
}

export interface IValidateMobileForm extends IRegisterMobileForm {
  code: string;
}

export interface ISendNafathRequestForm {
  "nationalId": number,
  "password": string,
  "token": string,
}

export interface ISendNafathResponse {
  random?: number,
  token?: string
}

export interface ISendNafathResponse2 {
  "transId": string,
  "random": number,
  "token": string
}
export interface ICheckNafathStatusForm {
  "token": string,
}
