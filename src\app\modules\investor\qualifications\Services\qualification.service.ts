import { Observable } from 'rxjs';

import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { QualificationModel } from '../interface/qualification.model';
import { environment } from '../../../../environments/environment';
import {
  BaseResponse,
  ResultResponse,
} from '../../../core/models/api-response.model';

@Injectable({ providedIn: 'root' })
export class QualificationService {
  private qualificationsPath =
    environment.baseUrl + '/investor/QualificationRequests';

  constructor(private http: HttpClient) {}

  investorQualifications(): Observable<BaseResponse<QualificationModel>> {
    return this.http.get<BaseResponse<QualificationModel>>(
      this.qualificationsPath
    );
  }

  create(model: FormData): Observable<ResultResponse> {
    return this.http.post<ResultResponse>(this.qualificationsPath, model);
  }
}
