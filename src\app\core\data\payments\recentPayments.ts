const TransactionStatus = {
  active: 'active',
  cancelled: 'cancelled',
  paused: 'paused'
}

export const recentPaymentData = [
  {
    id: 1,
    title: 'Savings Deposit',
    invoice: '#105986',
    medium: 'Paypal',
    date: '11/05/2028',
    status: TransactionStatus.active,
    time: '05:12 AM',
    money: 7000
  },
  {
    id: 2,
    title: 'Fixed Deposit',
    invoice: '#105987',
    medium: 'Bank Transfer',
    date: '12/15/2028',
    status: TransactionStatus.paused,
    time: '02:45 PM',
    money: 10000
  },
  {
    id: 3,
    title: 'Term Deposit',
    invoice: '#105988',
    medium: 'Credit Card',
    date: '08/20/2028',
    status: TransactionStatus.active,
    time: '10:30 AM',
    money: 5000
  },
  {
    id: 4,
    title: 'Emergency Fund',
    invoice: '#105989',
    medium: 'Venmo',
    date: '03/10/2029',
    status: TransactionStatus.active,
    time: '08:00 PM',
    money: 12000
  },
  {
    id: 5,
    title: 'Holiday Savings',
    invoice: '#105990',
    medium: 'Cash Deposit',
    date: '06/25/2029',
    status: TransactionStatus.paused,
    time: '01:15 PM',
    money: 8000
  },
  {
    id: 6,
    title: 'Investment Portfolio',
    invoice: '#105991',
    medium: 'Wire Transfer',
    date: '02/18/2029',
    status: TransactionStatus.cancelled,
    time: '11:45 AM',
    money: 15000
  },
  {
    id: 7,
    title: 'Education Fund',
    invoice: '#105992',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 8,
    title: 'Home Purchase Savings',
    invoice: '#105993',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 9,
    title: 'Education Fund',
    invoice: '#105992',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 10,
    title: 'Home Purchase Savings',
    invoice: '#105993',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 11,
    title: 'Savings Deposit',
    invoice: '#105986',
    medium: 'Paypal',
    date: '11/05/2028',
    status: TransactionStatus.active,
    time: '05:12 AM',
    money: 7000
  },
  {
    id: 12,
    title: 'Fixed Deposit',
    invoice: '#105987',
    medium: 'Bank Transfer',
    date: '12/15/2028',
    status: TransactionStatus.paused,
    time: '02:45 PM',
    money: 10000
  },
  {
    id: 13,
    title: 'Term Deposit',
    invoice: '#105988',
    medium: 'Credit Card',
    date: '08/20/2028',
    status: TransactionStatus.active,
    time: '10:30 AM',
    money: 5000
  },
  {
    id: 14,
    title: 'Emergency Fund',
    invoice: '#105989',
    medium: 'Venmo',
    date: '03/10/2029',
    status: TransactionStatus.active,
    time: '08:00 PM',
    money: 12000
  },
  {
    id: 15,
    title: 'Holiday Savings',
    invoice: '#105990',
    medium: 'Cash Deposit',
    date: '06/25/2029',
    status: TransactionStatus.paused,
    time: '01:15 PM',
    money: 8000
  },
  {
    id: 16,
    title: 'Investment Portfolio',
    invoice: '#105991',
    medium: 'Wire Transfer',
    date: '02/18/2029',
    status: TransactionStatus.cancelled,
    time: '11:45 AM',
    money: 15000
  },
  {
    id: 17,
    title: 'Education Fund',
    invoice: '#105992',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 18,
    title: 'Home Purchase Savings',
    invoice: '#105993',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 19,
    title: 'Education Fund',
    invoice: '#105992',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 20,
    title: 'Home Purchase Savings',
    invoice: '#105993',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  }
]
