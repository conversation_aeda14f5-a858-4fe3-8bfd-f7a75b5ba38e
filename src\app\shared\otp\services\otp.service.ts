import {Injectable} from '@angular/core';
import {ModalService} from "@component/form/modals/modal.service";
import {ApiService} from "../../../core/services/api.service";

@Injectable({
  providedIn: 'root'
})
export class OtpService {
  path = '/shared/OTPs';
  pathVerify = '/shared/OTPs/verify';

  constructor(
    private apiService: ApiService,
    protected modalService: ModalService,
  ) {
  }

  async send(data: { operationType: number }) {
    this.apiService.post(this.path, data).then((result) => {
      return result;
    }).catch((error) => {
      console.error(error);
      throw error;
    }).finally(() => {
      console.log('finally clause');
    });
  }

  async verify(data: { operationType: number, propertyValue: string }) {
    await this.apiService.post(this.pathVerify, data);
  }
}
