import {PermissionModel} from "./permission.model";

export interface RoleModel {
  roleID: string;
  name?: string;
  description?: string;
  lastUpdatedBy: string;
  lastUpdatedOn: Date;
  actionName?: string;
  permissions: PermissionModel[];
}

export interface RoleSearch {
  find?: string;
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: string;
  page?: number;
  itemsCount?: number;
  limit: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
}

export interface RoleCreateCommand {
  name: string;
  description: string;
  permissions: string[];
}

export interface RoleUpdateCommand {
  roleID: string;
  name: string;
  description: string;
  permissions: string[];
}

export interface FilteredModules {
  moduleName: string;
  permissions: PermissionModel[];
}
