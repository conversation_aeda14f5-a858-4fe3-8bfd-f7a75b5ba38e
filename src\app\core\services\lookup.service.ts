import {Injectable} from '@angular/core';
import {concat, filter, Observable, of, throwError} from "rxjs";
import {environment} from "../../../environments/environment";
import {catchError, map, switchMap, tap} from "rxjs/operators";
import {Lookup} from "../models/key-value.model";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class LookupService {
  endPoints = Object.freeze({
    product: 'ProductsList',
    supplier: 'SuppliersList',
  })

  constructor(
    private _http: HttpClient,
  ) {
  }

  lookups<T>(lookup: Lookup, endPoint: string): Observable<T[]> {
    return concat(
      of([]), // Default items
      lookup.input$.pipe(
        filter(value => !!value && value.length >= lookup.minTermLength),
        tap(() => lookup.loading = true),
        switchMap(term => this.get(endPoint, term,)),
        tap(() => lookup.loading = false),
        catchError(() => of([]))
      )
    );
  }

  private get(endPoint: string, term: string): Observable<any> {
    return this._http.get<any>(`${environment.apiUrl}Shared/Lookups/${endPoint}`, {params: {find: term}})
      .pipe(
        map(resp => resp.data),
        catchError(error => throwError(() => new Error(error.Error || 'Server Error')))
      );
  }
}
