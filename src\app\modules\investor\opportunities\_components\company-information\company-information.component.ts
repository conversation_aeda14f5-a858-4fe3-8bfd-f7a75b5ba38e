import { Component, Input } from '@angular/core';
import { DecimalPipe, CommonModule, DatePipe } from '@angular/common';
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';
import { DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { OpportunitiesService } from '../../services/opportunities.service';
// import { LoanRequestService } from '../../services/loan-request.service';
@Component({
  selector: 'app-company-information',
  standalone: true,
  imports: [ DecimalPipe, DateFormatPipe, CommonModule, DataTableModule, TranslateModule],
  providers: [
    DatePipe
  ],
  templateUrl: './company-information.component.html',
  styleUrl: './company-information.component.css'
})
export class CompanyInformationComponent {
  public companyDetails$?: Promise<any>;
  @Input({ required: true })
  opportunityId!: string;
  constructor(private loanRequestService: OpportunitiesService, private translate: TranslateService) { }

  ngOnInit(): void {
    this.companyDetails$ = this.loanRequestService.getCompanyByOpportunityId(this.opportunityId);
  }

  get ownersColumns() {
    return [
      { title: this.translate.instant('ownerName'), field: 'name' },
      { title: this.translate.instant('ownerIdentity'), field: 'identityID' },
      { title: this.translate.instant('relationName'), field: 'relationName' },
      { title: this.translate.instant('nationality'), field: 'nationalityName' },
      { title: this.translate.instant('shares'), field: 'sharesCount' }
    ];
  }

  get currentLang() {
    return this.translate.currentLang;
  }
}
