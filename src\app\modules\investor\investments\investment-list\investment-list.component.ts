import {Component, OnInit} from '@angular/core';
import {colDef, DataTableModule} from "@bhplugin/ng-datatable";
import {Router, RouterLink} from "@angular/router";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {InvestmentService} from "../services/investment.service";
import {InvestmentModel, InvestmentSearch} from "../models/investment.model";

import {DatePipe, NgClass} from "@angular/common";
import {DropdownComponent} from "@component/shared/dropdown/dropdown.component";
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';

@Component({
  selector: 'app-investment-list',
  standalone: true,
  imports: [
    DataTableModule,
    TranslateModule,
    DateFormatPipe,
    NgClass
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './investment-list.component.html',
  styleUrl: './investment-list.component.css'
})
export class InvestmentListComponent implements OnInit {
  columns: Array<colDef> = [];
  search: InvestmentSearch = {isServerMode: true} as InvestmentSearch;
  investments: InvestmentModel[] = [];

  constructor(
    public translate: TranslateService,
    protected investmentService: InvestmentService,
    private router: Router
  ) {
    this.investmentService.initPagination();
  }

  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }

  onInputChange(e: any) {
  }

  async ngOnInit() {
    this.columns = [
      {title: this.translate.instant('opportunityId'), field: 'opportunityTitle',},
      {title: this.translate.instant('walletName'), field: 'fullName',},
      {title: this.translate.instant('investmentDate'), field: 'investDate',},
      {title: this.translate.instant('amount'), field: 'amount',},
      {title: this.translate.instant('profits'), field: 'profits',},
      {title: this.translate.instant('profitDueDate'), field: 'dueDate',},
      {title: this.translate.instant('actualMaturityDate'), field: 'actualDueDate',},
      {title: this.translate.instant('status'), field: 'statusName',},
    ]
    await this.getList();
  }

  async getList() {
    try {
      this.investments = await this.investmentService.getList() ?? []
      
      
    } catch (e: any) {
      console.log(e);
    }
  }

opportunityDetails(id:number){
    this.router.navigate(['/opportunities', id]);
}
    async handleSearch(e: any) {
      this.search = {find: (e.target as HTMLInputElement).value, isServerMode: false} as InvestmentSearch;
      this.investments = [];
      this.investmentService.initPagination();
      await this.getList();
      this.search.isServerMode = true;
    }
}
