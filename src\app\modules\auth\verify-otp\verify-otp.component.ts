import {Component} from '@angular/core';
import {ButtonComponent} from "@component/form/button/button.component";
import {InputComponent} from "@component/form/input/input.component";
import {FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {Router} from "@angular/router";

@Component({
  selector: 'app-verify-otp',
  standalone: true,
  imports: [
    ButtonComponent,
    InputComponent,
    ReactiveFormsModule,
    TranslateModule
  ],
  templateUrl: './verify-otp.component.html',
  styleUrl: './verify-otp.component.css'
})
export class VerifyOtpComponent {
  otpForm!: FormGroup

  constructor(
    private fb: FormBuilder, private router: Router
  ) {
    this.otpForm = this.fb.group({
      otp: [null, [Validators.required, Validators.minLength(6), Validators.maxLength(6)]],
    });
  }

  ngOnInit() {
  }

  onSubmit() {
    if (this.otpForm.valid) {
      
      this.otpForm.reset()
      this.router.navigate(['/auth'])
    } else {
      this.otpForm.markAllAsTouched()
      console.log('Error')
    }
  }


}
