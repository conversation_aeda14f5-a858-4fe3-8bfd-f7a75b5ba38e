import {Component, EventEmitter, Input , Output} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {AsyncPipe, NgClass} from "@angular/common";
import {LoadingBarService} from "@ngx-loading-bar/core";
import {ApiService} from "../../../../core/services/api.service";

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [
    TranslateModule,
    AsyncPipe,
    NgClass
  ],
  templateUrl: './button.component.html',
  styleUrl: './button.component.scss'
})
export class ButtonComponent{
  @Input() text!: string;
  @Input() type: 'submit' | 'button' | 'reset' = 'submit';
  @Input() class?: string = 'btn-primary';
  @Input() variant?: 'default' | 'soft' = 'default';
  @Input() color: string = 'primary';
  @Output() onClick = new EventEmitter<any>();
  @Input() loading?: boolean;
  @Input() classList?: string;
  constructor(
    protected loadingBar: LoadingBarService,
    protected apiService: ApiService,
  ) {
  }

  handleSubmit() {
    this.onClick.emit();
  }
}
