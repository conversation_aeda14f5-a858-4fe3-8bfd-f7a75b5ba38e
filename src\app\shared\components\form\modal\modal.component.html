<ng-modals [id]="id" [placement]="'modal-top'"
           className="fixed flex flex-col  transition-all duration-300 ease-in-out left-2/4 z-[1050] -translate-x-2/4 translate-y-8">
  <div [ngClass]="{'lg:w-[55rem]': isLarger, 'md:w-[30rem]': !isLarger}"
       class="  bg-white shadow rounded-md dark:bg-zink-600 flex flex-col  space-12">
    <div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-zink-500">
      <h5 class="text-16">{{ title | translate }}</h5>

    </div>
    <!--    <div class="max-h-[calc(theme('height.screen')_-_180px)] p-4 overflow-y-auto"> -->
    <div class=" p-4 pb-0">
      <ng-content></ng-content>
    </div>
    <div class="flex justify-end gap-2 p-4  mt-auto">
      <button
        (click)="onClickClose()"
        class="text-red-500 bg-white btn hover:text-red-500 hover:bg-red-100 focus:text-red-500 focus:bg-red-100 active:text-red-500 active:bg-red-100 dark:bg-zink-600 dark:hover:bg-red-500/10 dark:focus:bg-red-500/10 dark:active:bg-red-500/10"
        type="reset">
        {{ 'cancel' | translate }}
      </button>
      <app-button
        (onClick)="onClickSubmit()"
        [text]="title"
      />
    </div>
  </div>
</ng-modals>

