<div
  class="box xl:p-6 dark:bg-bg4 grid grid-cols-12 gap-4 xxxl:gap-6 items-center shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
  <div class="col-span-12 lg:col-span-7 w-full">
    <div class="box bg-primary/5 dark:bg-bg3 lg:p-6 xl:p-8 border border-n30 dark:border-n500">
      <img [src]="'assets/images/logo-with-text.png'" alt="logo" class="md:hidden p-6 block mx-auto"/>
      <h3 class="h3 mb-4 text-center">{{ 'forgotPassword' | translate }}  <a class="text-primary" href="/auth"> {{ 'Login' | translate }} </a></h3>
      <p class="md:mb-6 pb-4 mb-4 md:pb-6 bb-dashed text-sm md:text-base text-center">{{ 'forgotPassword_short' | translate }}</p>
      <div class="flex flex-col gap-4 justify-center items-start">
        <button (click)="openForgetModalIqamma()" class="h-[50px] w-[481px] rounded-[32px] px-[24px] py-[12px] flex items-center justify-center border-1 border-primary text-[#404A60] bg-n0 hover:bg-white-light  font-[12px] ">
          {{'IdNumber'|translate}}
        </button>
        <button (click)="openForgetModalPhone()" class="h-[50px] w-[481px] rounded-[32px] px-[24px] py-[12px] flex items-center justify-center border-1 border-primary text-[#404A60] bg-n0 hover:bg-white-light  font-[12px] ">
          {{'MobileNo'|translate}}
        </button>
      </div>
      <!-- <form (ngSubmit)="onSubmit()" [formGroup]="formGroup" class=""
            id="signupForm">
         <app-input
              [control]="formGroup.get('mobile')"
              [label]="'phone'"
              [type]="'tel'"
              [autofocus]="true"
            />
      </form> -->
      <!-- <div class="mt-8">
        <app-button
          (onClick)="onSubmit()"
          [text]="'verification'"
        ></app-button>
      </div> -->
    </div>
  </div>
  <div class="hidden md:flex col-span-12 lg:col-span-5 justify-center items-center">
    <img alt="img" height="560" src="assets/images/SecurityShield.svg" width="533"/>
  </div>
</div>
@if (modalService.isOpen('otp-modal')) {
  <app-otp-modal
    (submitOtp)="verifyOtp($event)"
  />
}

@if(modalService.isOpen('forget-modal')) {
  <app-forget-modal [isPhone]="isPhone" [placeholder]="isPhone?'phone':'IdNumber'" (submitEnteredValue)="submitEnteredValue($event)" />
}
