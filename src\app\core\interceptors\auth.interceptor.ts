// src/app/auth.interceptor.ts
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ttpHandlerFn, HttpInterceptorFn, HttpStatusCode} from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../../modules/auth/services/auth.service';
import { ToastService } from '../services/toast.service';
import { inject } from '@angular/core';
import { EncryptStorage } from 'encrypt-storage';
import { TranslateService } from '@ngx-translate/core';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  // Inject the current `AuthService` and use it to get an authentication token:
  const translate = inject(TranslateService);
  const encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')

  const blackList = ['/client/onboarding/send-nafath-request','/CheckNafathStatus','/client/onboarding/check-nafath-status'];
  const check = blackList.some(el=> el.includes(req.url))
  const isAuthPath = location.pathname.includes('/auth')
  const token = check ? null : isAuthPath ? null: decodeURIComponent(encryptTokenStorage.getItem('token')??'')
  // Get the current language
  const currentLang = translate.currentLang || translate.defaultLang;

  // Clone the request and add the authentication header and language header
  let headers = req.headers;

  if (token) {
    headers = headers.set('Authorization', `Bearer ${token}`);
  }

  if (currentLang) {
    headers = headers.set('Accept-Language', currentLang);
  }

  const authReq = req.clone({
    headers: headers
  });

  return next(authReq);
}

export const errorInterceptorHandler :HttpInterceptorFn = (res,next:HttpHandlerFn)=>{
  const authenticationService: AuthService = inject(AuthService)
  const toast: ToastService = inject(ToastService)
    const translate = inject(TranslateService);
  return next(res).pipe(catchError(err => {
        if (err.status === HttpStatusCode.Unauthorized) {
          // auto logout if 401 response returned from api
          localStorage.clear();
          // window.location.reload()
          authenticationService.logout();
          // Replace 'this.translate' with a direct injection of the TranslateService


          toast.error(translate.instant('SessionExpired'));

        } else if (err.status === HttpStatusCode.BadRequest) {
          toast.error(err.error.message);
        }else if (err.status == 0){
          toast.error(err.message)
        }
        const error = err.error.message || err.statusText;
        return throwError(error);
      }))
}

