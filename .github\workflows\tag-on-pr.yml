name: Tag and Branch on PR to Main

on:
  pull_request:
    types: [opened, reopened, synchronize]
    branches:
      - main

jobs:
  tag_and_branch:
    if: github.event.pull_request.merged == false
    runs-on: ubuntu-latest

    permissions:
      contents: write  # needed to push tags and branches

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Extract version from PR title
        id: get_version
        run: |
          TITLE="${{ github.event.pull_request.title }}"
          if [[ "$TITLE" =~ ([0-9]+\.[0-9]+\.[0-9]+) ]]; then
            VERSION="${BASH_REMATCH[1]}"
            echo "VERSION=$VERSION" >> $GITHUB_ENV
          else
            echo "❌ PR title must include version like 1.1.0"
            exit 1
          fi

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Create tag and branch
        run: |
          git fetch origin main
          git checkout main
          git tag -a "v${VERSION}" -m "Release v${VERSION}"
          git push origin "v${VERSION}"
          git checkout -b "releases/v${VERSION}"
          git push origin "releases/v${VERSION}"
