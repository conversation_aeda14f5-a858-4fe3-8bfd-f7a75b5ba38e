import { ChangeDetectionStrategy, Component,  OnInit, inject, output, signal } from '@angular/core';
import { AuthService } from './../../../../../modules/auth/services/auth.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ICompanyInfo } from '../CompayList';
import { NgClass, NgStyle } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FileUploadComponent } from '@component/form/file-upload/file-upload.component';
import { ToastService } from '../../../../../core/services/toast.service';
import { BorrowerService } from '@component/Borrower/Borrower.service';


@Component({
  selector: 'app-registed-company',
  standalone: true,
  imports: [TranslateModule,NgClass,NgStyle,ReactiveFormsModule,FileUploadComponent,FormsModule,],
  templateUrl: './Registed-company.component.html',
  styleUrl: './Registed-company.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegistedCompanyComponent implements OnInit {
  #authService:AuthService =inject(AuthService)
  #toast: ToastService = inject(ToastService)
  listOfOwnedCompanies=signal<ICompanyInfo[]>([])
  #borrowerService:BorrowerService = inject(BorrowerService)
  #translateService:TranslateService=inject(TranslateService)
  companyForm!:FormGroup
  selectedCompany=signal<number>(JSON.parse(localStorage.getItem('selectedCompany')??'0'))
  firstStep=signal<boolean>(JSON.parse(localStorage.getItem('firstStep')??'false'))
  closeStatuts=output<boolean>()
  addAnotherCompanyChange = output<boolean>()
  goBack =output<boolean>()

  CommercialRegisteraddress:any
  CommercialRegister:any
  ngOnInit(): void {

    this.getListOfOwnedCompanies()
    this.initalForm()
   }

   initalForm(){
    this.companyForm = new FormGroup({
      annualTurnover: new FormControl(null,Validators.required),
      ArticlesAssociation: new FormControl(null,Validators.required),
      ShareholdersResolution: new FormControl(null,Validators.required),
      SupportingDocuments: new FormControl(null,Validators.required),
    })
   }
  async getListOfOwnedCompanies(){
    try {

      const res:any = await this.#authService.commercialRegistrationsList()
      this.listOfOwnedCompanies.set(res.data)
     
    } catch (error:any) {
      this.#toast.error(error.message)
    }

   }

    selectCompany(index:number,cr:number,crEntityNumber:number){

    this.selectedCompany.set(index)
   }
   async nextStep(){
    try {
      this.CommercialRegister= await this.#authService.companySubmitRegister(this.listOfOwnedCompanies()[this.selectedCompany()].crNumber,
      this.listOfOwnedCompanies()[this.selectedCompany()].crEntityNumber)
      this.CommercialRegisteraddress=this.CommercialRegister.data
      localStorage.setItem('selectedCompany',this.selectedCompany().toString())
      localStorage.setItem('firstStep',this.firstStep().toString())
      this.firstStep.update(v=>!v)

    } catch (error:any) {
      this.#toast.error(error.message)
    }

   }
   async onSubmit(){
    if(this.companyForm.valid){
      try{
        const data = new FormData();
        data.append('ArticlesAssociation',this.companyForm.get('ArticlesAssociation')?.value)
        data.append('ShareholdersResolution',this.companyForm.get('ShareholdersResolution')?.value)
        data.append('SupportingDocuments',this.companyForm.get('SupportingDocuments')?.value)
        const res:any = await this.#authService.companySubmitDocuments(+this.companyForm.get('annualTurnover')?.value,data)
        // const currentAccountStatus =JSON.parse(localStorage.getItem('currentSubInfo')??'null')
        // currentAccountStatus.userAccountStatusName = 'PendingApproval'
        // currentAccountStatus.userAccountStatus = 1
        // localStorage.setItem('currentSubInfo',JSON.stringify(currentAccountStatus))
        this.closeStatuts.emit(true)
        this.#toast.success(this.#translateService.currentLang == 'ar' ? res.result['message	'] : res.result['message'])
          // this.#toast.error(this.#translateService.currentLang == 'ar' ? res.result['arabicMessage'] : res.result['englishMessage'])
       
      }catch(e){
        this.closeStatuts.emit(false)
        console.log(e);

      }
    }
   }

   addAnotherCompany(){
    this.addAnotherCompanyChange.emit(true)
   }

   previousStep(){
    this.firstStep.update(v=>!v)
   }

   reslut:any
   async handleBorrowerCheck(seleced:number){
    if(seleced){
      try{
        // const res = await this.#borrowerService.apiGetCommercialRegistration(seleced)
        // console.log(res);
        // this.reslut = res

      }catch(e:any){
        this.#toast.error(
          this.#translateService.currentLang == 'ar' ? e.error.result['arabicMessage'] : e.error.result['englishMessage'])
      }
    }
  }
  goBackStep(){

    this.goBack.emit(false);

  }
}
