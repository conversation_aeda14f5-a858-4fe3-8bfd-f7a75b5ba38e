import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  AnnualIncomeRange,
  InvestmentExperience,
  JobInformation,
  RiskTolerance,
  YesOrNo,
  UserStatus,
  UserType,
} from '../../../shared/constants/enums';

import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EnumSelectPipe } from '../../../token-mask.pipe';
import { financialInformationModel } from '../models/financial-data.model';
import { InputComponent } from '@component/form/input/input.component';
import { Router } from '@angular/router';
import { FinancialService } from '../services/financial.service';
import { ButtonComponent } from '../../../shared/components/form/button/button.component';
import { EncryptStorageService } from '../services/encrypt-storage.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-financial-info',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    InputComponent,
    EnumSelectPipe,
    TranslateModule,
    ButtonComponent,
  ],
  standalone: true,
  templateUrl: './financial-info.component.html',
  styleUrls: ['./financial-info.component.css'],
})
export class FinancialInfoComponent implements OnInit {
  model: financialInformationModel = {};

  isUpdating: boolean = false;
  financailInfoForm!: FormGroup;

  constructor(
    private financialService: FinancialService,
    private router: Router,
    private translate: TranslateService,
    private fb: FormBuilder,
    private encryptStorageService: EncryptStorageService,
    private toaster: ToastrService
  ) {}

  ngOnInit() {
    this.initFinancailInfoForm();
  }

  disableSubmit(): boolean {
    return (
      this.model?.annualIncomeRange === null ||
      this.model?.investmentExperience === null ||
      this.model?.riskTolerance === null ||
      this.model?.jobInformation === null ||
      this.model?.isBoardMember === null ||
      this.model?.isPoliticallyExposedPerson === null ||
      this.model?.isAssociatedWithPEP === null ||
      this.model?.isRealBeneficiary === null ||
      this.model?.jobTitle === null ||
      this.model?.companyName === null ||
      this.model?.companyAddress === null
    );
  }

  initFinancailInfoForm(): void {
    this.financailInfoForm = this.fb.group({
      jobTitle: [null],
      companyName: [null],
      companyAddress: [null],
      annualIncome: [null, Validators.required],
      investmentExperience: [null, Validators.required],
      riskTolerance: [null, Validators.required],
      jobInformation: [null, Validators.required],
      isBoardMember: [null, Validators.required],
      isPoliticallyExposedPerson: [null, Validators.required],
      isAssociatedWithPEP: [null, Validators.required],
      isRealBeneficiary: [null, Validators.required],
    });
  }

  get jobTitle() {
    return this.financailInfoForm.get('jobTitle');
  }

  get companyName() {
    return this.financailInfoForm.get('companyAddress');
  }

  get companyAddress() {
    return this.financailInfoForm.get('companyName');
  }

  create() {
    this.isUpdating = true;
    this.financialService.update(this.model).subscribe({
      next: (res) => {
        this.isUpdating = false;
        this.toaster.success(
          this.translate.currentLang === 'en'
            ? res.englishMessage
            : res.arabicMessage
        );
      },
      error: (error) => {
        this.isUpdating = false;
      },
    });
  }

  $checkUser() {
    let user = this.encryptStorageService.getCurrentUser();
    if (user.status == UserStatus.Active) {
      if (user.userType == UserType.Investor) {
        this.router.navigate(['/investor/my-dashboard']);
      } else {
        this.router.navigate(['/admin/dashboard']);
      }
    }
  }

  getLabel(key: string): string {
    return this.translate.instant(key);
  }

  public get AnnualIncomeRange(): typeof AnnualIncomeRange {
    return AnnualIncomeRange;
  }
  public get InvestmentExperience(): typeof InvestmentExperience {
    return InvestmentExperience;
  }
  public get RiskTolerance(): typeof RiskTolerance {
    return RiskTolerance;
  }

  public get JobInformation(): typeof JobInformation {
    return JobInformation;
  }

  public get YesOrNo(): typeof YesOrNo {
    return YesOrNo;
  }
}
