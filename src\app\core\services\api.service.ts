import {HttpClient, HttpStatusCode} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {environment} from '../../../environments/environment';
import {TranslateService} from '@ngx-translate/core';
import {Router} from '@angular/router';
import {firstValueFrom} from 'rxjs';
import {SweetAlertService} from "./sweet-alert.service";
import {ToastService} from "./toast.service";
import {IPagination} from "../models/pagination.model";
import {BaseResponse} from "../models/api-response.model";
import {IResult} from "../models/response.model";

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  isLPostLoading = false;

  constructor(
    private _http: HttpClient,
    private toast: ToastService,
    private translate: TranslateService,
    private router: Router,
    private sweetAlertService: SweetAlertService
  ) {
  }

  initPagination(pagination: IPagination) {
    return {
      pageSize: pagination.pageSize,
      pageSizeOptions: pagination.pageSizeOptions,
      page: pagination.page ?? 1,
      loading: true,
    } as IPagination;
  }

  async post<T>(url: string, body: any, headers: any = null) {
    let result;
    this.isLPostLoading = true;
    result = await firstValueFrom(
      this._http.post<T>(environment.baseUrl + url, body, {headers}),
    ).catch((err) => {
      result = this.handleError(err);
      throw result;
    }).finally(() => this.isLPostLoading = false);
    if (url.includes('admin'))
      this.toast.success(this.translate.instant(url.replace('/', '.')));
    return result;
  }

  async delete(url: string): Promise<void> {
    await this.sweetAlertService.delete().then(async (response: any) => {
      if (response.isConfirmed) {
        const response = <IResult>await firstValueFrom(
          this._http.delete(environment.baseUrl + url),
        ).catch((err) => this.handleError(err));
        if (response != null && response.status == 1) {
          this.sweetAlertService.deleted()
        }
      }
    });
  }

  async get<T>(url: string, query?: any) {
    query = Object.fromEntries(Object.entries(query || {}).filter(([, value]) => value));
    // if (query.limit === undefined) query.limit = 10;
    let response = {} as BaseResponse<T>;
    response = <BaseResponse<T>>await firstValueFrom(
      this._http.get<BaseResponse<T>>(environment.baseUrl + url, {
        params: query,
      }),
    ).catch((err) => (response.result = this.handleError(err)));
    return response ?? ({} as BaseResponse<T>);
  }

  async put<T>(url: string, body: any) {
    let result;
    this.isLPostLoading = true;
    result = <T>await firstValueFrom(
      this._http.put<T>(environment.baseUrl + url, body),
    ).catch((err) => {
      result = this.handleError(err);
      throw result;
    }).finally(() => this.isLPostLoading = false);
    return result;
  }

  async patch( url: string, body?: any,) {
    let response = {} as IResult;
    response = <IResult>await firstValueFrom(
      this._http.patch<IResult>(environment.baseUrl + url, body),
    ).catch((err) => (response = this.handleError(err)));
    return response;
  }

  private handleErrorResponse(response: any, message?: string) {
 
    this.toast.error(
      message ?? this.translate.instant('errorOccure'),
    );
  }

  private handleError(error: any) {
    console.log(error)
    if (error?.status === 0) {
      this.toast.error(this.translate.instant('toast_error_0'));
      error.status = 3;
    } else if (error?.status === HttpStatusCode.BadRequest) {
      this.toast.error(this.translate.currentLang == 'ar'
        ? error.error.arabicMessage ?? error.error.result.arabicMessage :   error.error.englishMessage ?? error.error.result.englishMessage);
    } else if (error?.status === HttpStatusCode.NotFound) {
      this.toast.error(
        this.translate.currentLang == 'ar'
          ? error.error.result.arabicMessage : error.error.result.englishMessage ?? this.translate.instant('not_found'),
      );
    } else if (error?.status === HttpStatusCode.Unauthorized) {
      this.router.navigate(['/auth']);
    } else if (error?.status === HttpStatusCode.Forbidden) {
      this.toast.error(this.translate.instant('forbidden'));
    } else {
      console.info(error.error.arabicMessage)
        this.toast.error(this.translate.currentLang == 'ar'
        ? error.error.arabicMessage ?? error.error.result.arabicMessage :   error.error.englishMessage ?? error.error.result.englishMessage);
    }
    return {status: 3, ...error.error || {message: error.toString()}};
  }
}
