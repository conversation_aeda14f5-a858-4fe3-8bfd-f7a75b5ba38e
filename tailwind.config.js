/** @type {import('tailwindcss').Config} */
import colors from "tailwindcss/colors"

const {addDynamicIconSelectors} = require('@iconify/tailwind');

module.exports = {
  content: ["./src/**/*.{html,ts}"], darkMode: 'class', theme: {
    extend: {
      colors: {
        transparent: "transparent",
        primary: {
          '50': '#eff9ff',
          '100': '#dff2ff',
          '200': '#b7e7ff',
          '300': '#77d4ff',
          '400': '#2fc0ff',
          '500': '#04a8f3',
          '600': '#0086d0',
          '700': '#006aa9',
          '800': '#015c8e',
          '900': '#074a73',
          '950': '#05304c',
          DEFAULT: '#015C8E',
        },
        zink: {
          50: "#E2EAF3",
          100: "#C8D7E9",
          200: "#92AFD3",
          300: "#5885BC",
          400: "#395F8E",
          500: "#233A57",
          600: "#1C2E45",
          700: "#132337",
          800: "#0F1824",
          900: "#070C12",
          950: "#030507"
        },
        secondary1: "#B9BBBD",
        secondary2: "#00A19A",
        secondary3: "#BD7B00",
        n0: "#FFFFFF",
        n10: "#FAFAFB",
        n20: "#F5F6F7",
        n30: "#EBECEF",
        n40: "#DFE0E4",
        n50: "#C1C4CC",
        n60: "#B2B6BF",
        n70: "#A6AAB5",
        n80: "#979CA8",
        n90: "#888E9C",
        n100: "#798090",
        n200: "#6A7283",
        n300: "#5B6477",
        n400: "#4F586D",
        n500: "#404A60",
        n600: "#343E56",
        n700: "#222E48",
        n800: "#13203B",
        n900: "#0B1323",
        bg3: "#23262B",
        bg4: "#1D1E24",
        lightbg1: "#F4FBF7",
        lightbg2: "#F6F8FE",
        success: {
          50: colors.green[50],
          100: colors.green[100],
          200: colors.green[200],
          300: colors.green[300],
          400: colors.green[400],
          500: colors.green[500], // Using Tailwind's color palette
          600: colors.green[600],
          700: colors.green[700],
          800: colors.green[800],
          900: colors.green[900],
          950: colors.green[950],
          DEFAULT: '#00ab55',
          light: '#ddf5f0',
          dark: 'rgba(0,171,85,.15)',
        },
        danger: {
          50: colors.red[50],
          100: colors.red[100],
          200: colors.red[200],
          300: colors.red[300],
          400: colors.red[400],
          500: colors.red[500],
          600: colors.red[600],
          700: colors.red[700],
          800: colors.red[800],
          900: colors.red[900],
          950: colors.red[950],
          DEFAULT: '#e7515a',
          light: '#fff5f5',
          dark: 'rgba(231,81,90,.15)',
        },
        warning: {
          50: colors.amber[50],
          100: colors.amber[100],
          200: colors.amber[200],
          300: colors.amber[300],
          400: colors.amber[400],
          500: colors.amber[500],
          600: colors.amber[600],
          700: colors.amber[700],
          800: colors.amber[800],
          900: colors.amber[900],
          950: colors.amber[950],
          DEFAULT: '#e2a03f',
          light: '#fff9ed',
          dark: 'rgba(226,160,63,.15)',
        },
        info: {
          50: colors.sky[50],
          100: colors.sky[100],
          200: colors.sky[200],
          300: colors.sky[300],
          400: colors.sky[400],
          500: colors.sky[500],
          600: colors.sky[600],
          700: colors.sky[700],
          800: colors.sky[800],
          900: colors.sky[900],
          950: colors.sky[950],
          DEFAULT: '#2196f3',
          light: '#e7f7ff',
          dark: 'rgba(33,150,243,.15)',
        },
        dark: {
          light: '#eaeaec',
          DEFAULT: '#233A57',
          dark: 'rgba(59,63,92,.15)',
        },
        black: {
          DEFAULT: '#0e1726',
          light: '#e3e4eb',
          dark: 'rgba(14,23,38,.15)',
        },
        white: {
          DEFAULT: '#ffffff',
          light: '#e0e6ed',
          dark: '#e3e4eb',
        },
      }, fontFamily: {
        'cairo': ['Cairo', 'sans-serif'] // Make sure 'Cairo' is matched with the font name from Google Fonts
      }, screens: {
        sm: "576px", md: "768px", lg: "992px", xl: "1200px", xxl: "1400px", xxxl: "1600px", "4xl": "1800px",
      }, animation: {
        "spin-slow": "spin 2s linear infinite",
      },
    },
  }, plugins: [

    require('./src/assets/js/plugins/forms.js'),
    require('./src/assets/js/plugins/drawer.js'),

    addDynamicIconSelectors(),

  ],
};
