import {IQuery} from "../../../core/models/pagination.model";

export interface ILoanRequest {
  loanRequestID: string
  loanRequestNo: string
  userID: string
  userName: string
  companyID: string
  requestBy: string
  clientCRNumber: string
  clientName: string
  invoiceNumber: string
  invoiceAmount: number
  invoiceVAT: number
  profitRate: number
  downPayment: number
  requestDate: string
  requestType: number
  requestTypeName: string
  requestStatus: number
  requestStatusName: string
  dueDate: string
  periodDays: number
  studyFees?: any
  evaluation?: any
  

}

//client, loan request, amount, and due date ranges
export interface ILoanRequestSearch  extends IQuery  {
  "find": string,
  "status": number,
  "amount": number,
  "dueDays": number,
  "sort": string,
  "page": number,
  "dueDate": string,
  "userName": string,
}
