
  @if (label) {
    <label [for]="control?.name ?? placeholder" class="inline-block mb-2 text-base font-medium">
      {{ label | translate }}
      @if (required) {
        <span class="text-red-500"> *</span>
      }
    </label>
  }
  <input
         [formControl]="control"
         [placeholder]="placeholder | translate"
         [readonly]="readonly"
         data-provider="flatpickr"
         mwlFlatpickr
         type="date"
         class="form-input px-3 rounded-3xl
  border-slate-200
  placeholder:text-slate-400
  focus:outline-none
  focus:border-primary-500
  disabled:bg-slate-100
  disabled:border-slate-300
  disabled:text-slate-500
  dark:border-zink-500
  dark:text-zink-100
  dark:bg-zink-700
  dark:focus:border-primary-500
  dark:placeholder:text-zink-200
  dark:disabled:bg-zink-600
  dark:disabled:border-zink-500
  dark:disabled:text-zink-200 focus:shadow-custom"
  >

