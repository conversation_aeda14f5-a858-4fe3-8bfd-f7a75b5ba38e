<div class="relative min-h-screen bg-secondary1/5 dark:bg-bg3 overflow-hidden flex items-center justify-center px-4">
  <img [src]="colorMode == 'light' ? 'assets/images/logo.png' : 'assets/images/logo.png'"
    class="p-6 hidden md:flex shrink-0 lg:p-8 absolute top-0 right-0 z-[2]" alt="logo" />
  <!-- Background SVG Top -->
  <svg width="300" height="300" class="shap_top" preserveAspectRatio="none">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color: #015C8E;"></stop>
        <stop offset="100%" style="stop-color: #015C8E;"></stop>
      </linearGradient>
    </defs>
    <path d="M100,100 C70,80 80,50 100,30 L100,100 Z" fill="url(#gradient)">
      <animate attributeName="d" dur="5s" repeatCount="indefinite"
        values="
          M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z;

          M453.5,289Q413,328,399.5,374Q386,420,331.5,393.5Q277,367,239,415.5Q201,464,152.5,449Q104,434,129,365Q154,296,141,273Q128,250,108,211Q88,172,137,171Q186,170,198.5,124.5Q211,79,244,104.5Q277,130,310,131.5Q343,133,388.5,147.5Q434,162,464,206Q494,250,453.5,289Z;

          M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z" />
    </path>
  </svg>

  <!-- Background SVG Bottom -->
  <svg width="500" height="500" class="shap_bottom" preserveAspectRatio="none">
    <defs>
      <linearGradient id="gradient2" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color: #015C8E;"></stop>
        <stop offset="100%" style="stop-color: #015C8E;"></stop>
      </linearGradient>
    </defs>
    <path d="M100,100 C70,80 80,50 100,30 L100,100 Z" fill="url(#gradient2)">
      <animate attributeName="d" dur="5s" repeatCount="indefinite"
        values="
          M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z;

          M453.5,289Q413,328,399.5,374Q386,420,331.5,393.5Q277,367,239,415.5Q201,464,152.5,449Q104,434,129,365Q154,296,141,273Q128,250,108,211Q88,172,137,171Q186,170,198.5,124.5Q211,79,244,104.5Q277,130,310,131.5Q343,133,388.5,147.5Q434,162,464,206Q494,250,453.5,289Z;

          M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z" />
    </path>
  </svg>

  <!-- Centered Form Card -->
  <div
    class="relative z-[2] w-full max-w-[65rem] mt-4 bg-white dark:bg-bg4 rounded-2xl grid grid-cols-12 gap-6 shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">

    <!-- Left: Form -->
    <div class="col-span-12 lg:col-span-7 w-full">
      <div class="bg-primary/5 dark:bg-bg3 lg:p-6 xl:p-8 border border-n30 dark:border-n500 rounded-xl">
        <img [src]="'assets/images/logo-with-text.png'" alt="logo" class="md:hidden p-6 block mx-auto" />
        <h3 class="h3 mb-4">{{ 'resetPassword' | translate }}</h3>
        <p class="md:mb-6 pb-4 mb-4 md:pb-6 bb-dashed text-sm md:text-base">{{ 'setNewPasswordMessage' | translate }}
        </p>

        <form [formGroup]="changePasswordForm" class="flex flex-col space-y-4 md:space-y-4"
          (ngSubmit)="verifyChangePassword()">
          <!-- Current Password -->
          <div class="col-span-12">
            <app-input (endIconClick)="togglePasswordVisibility('currentPassword')"
              [control]="changePasswordForm.get('currentPassword')"
              [endIcon]="passwordVisibility.currentPassword ? 'icon-[solar--eye-line-duotone]' : 'icon-[solar--eye-closed-line-duotone]'"
              [label]="'currentPassword' | translate"
              [type]="passwordVisibility.currentPassword ? 'password' : 'text'" />
            <div
              *ngIf="changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched"
              class="text-red-500 text-sm">
              {{ 'required' | translate }}
            </div>
          </div>

          <!-- New Password -->
          <div class="col-span-12">
            <app-input (endIconClick)="togglePasswordVisibility('newPassword')"
              [control]="changePasswordForm.get('newPassword')"
              [endIcon]="passwordVisibility.newPassword ? 'icon-[solar--eye-line-duotone]' : 'icon-[solar--eye-closed-line-duotone]'"
              [label]="'newPassword' | translate" [type]="passwordVisibility.newPassword ? 'password' : 'text'" />
            <div
              *ngIf="changePasswordForm.get('newPassword')?.touched && changePasswordForm.get('newPassword')?.invalid"
              class="text-red-500 text-sm">
              <div *ngIf="changePasswordForm.get('newPassword')?.errors?.['required']">
                {{ 'required' | translate }}
              </div>
              <div *ngIf="changePasswordForm.get('newPassword')?.errors?.['invalidPassword']">
                {{ 'passwordMustContain' | translate }}
              </div>
            </div>
          </div>

          <!-- Confirm Password -->
          <div class="col-span-12">
            <app-input (endIconClick)="togglePasswordVisibility('confirmPassword')"
              [control]="changePasswordForm.get('confirmPassword')"
              [endIcon]="passwordVisibility.confirmPassword ? 'icon-[solar--eye-line-duotone]' : 'icon-[solar--eye-closed-line-duotone]'"
              [label]="'confirmPassword' | translate"
              [type]="passwordVisibility.confirmPassword ? 'password' : 'text'" />
            <div
              *ngIf="changePasswordForm.get('confirmPassword')?.touched && changePasswordForm.get('confirmPassword')?.invalid"
              class="text-red-500 text-sm">
              <div *ngIf="changePasswordForm.get('confirmPassword')?.errors?.['required']">
                {{ 'required' | translate }}
              </div>
              <div *ngIf="changePasswordForm.get('confirmPassword')?.errors?.['confirmPasswordValidator']">
                {{ 'passwordsDoNotMatch' | translate }}
              </div>
            </div>
          </div>

          <!-- Submit -->
          <div class="col-span-12 flex justify-end mt-4">
            <button class="btn px-6 py-3 text-sm md:text-base md:px-8 md:py-3" type="submit">
              {{ 'save' | translate }}
            </button>
          </div>
        </form>

      </div>
    </div>

    <!-- Right: Image -->
    <!-- Right Column: Image with back button -->
    <div class="hidden md:flex col-span-12 lg:col-span-5 justify-center items-start relative">
      <!-- Back Button inside the image area -->
      <a (click)="goBack()" class="btn ac-modal-btn absolute top-4 left-4 z-10">
        <i class="las la-arrow-alt-circle-left text-base md:text-lg"></i>
        {{ 'back' | translate }}
      </a>

      <!-- Illustration Image -->
      <img src="assets/images/reset-password.png" width="533" height="560" class="my-20" alt="Reset Password" />
    </div>
  </div>
</div>


@if (modalService.isOpen('otp-change-password-modal')) {
<app-otp-modal [id]="'otp-change-password-modal'" (submitOtp)="changePassword($event)" />
}