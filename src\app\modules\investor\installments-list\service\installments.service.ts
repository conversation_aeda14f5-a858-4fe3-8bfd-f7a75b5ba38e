import { Injectable } from '@angular/core';
import { PAGINATION, IQuery } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';

@Injectable({
  providedIn: 'root',
})
export class installmentsService {
  pagination = PAGINATION;
  private path = '/client/loan-requests';

  constructor(private apiService: ApiService) { }

  initPagination = () =>
    (this.pagination = this.apiService.initPagination(this.pagination));

  async getList(params: any, query: IQuery) {
    query = { ...query, limit: this.pagination.pageSize } as IQuery;
    console.log(params)
     const response:any = await this.apiService.post(
      `${this.path}/installment/search`,
      query
    );

    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response

  }
  async payInstallment(id: any) {
    return await this.apiService.post(
      `${this.path}/pay-installment/${id}`,
      {}
    );
  }



}
