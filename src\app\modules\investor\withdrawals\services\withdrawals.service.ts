import {Injectable} from '@angular/core';

import {DepositRequestSearch, PaymentRequestSearch} from "../../deposits/models/deposit.model";
import { IQuery, PAGINATION } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';
import { BaseResponse } from '../../../../core/models/api-response.model';
import { IKeyValue } from '../../../../core/models/key-value.model';
import { IBankAccount, Icreditwallet } from '../../../auth/models/bank-account.model';


@Injectable({
  providedIn: 'root'
})
export class WithdrawalsService {
  pagination = PAGINATION;

  private path = '/client';

  constructor(
    private apiService: ApiService,
  ) {
  }

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);


  async getList(query?: IQuery) {
    query = {...query, limit: this.pagination.pageSize} as IQuery;
    const response:any = await this.apiService.post<BaseResponse<PaymentRequestSearch>>(`${this.path}/withdraw/search`, query);
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response.data;
  }
async getbankaccount(){
      return await this.apiService.get<any>(`${this.path}/profile/bank-account`); 
 }

 
 async getCollectioncount(){
   return this.apiService.get<IBankAccount>(`${this.path}/profile/collection-account`)
 }
  async create(body: any) {
    
    return await this.apiService.post(`${this.path}/withdraw`, body);
  }
  async CancelWithdrawalsbyId(id: any) {
   
    return await this.apiService.put(`${this.path}/withdraw/cancel/${id}`, {});
  }


}


