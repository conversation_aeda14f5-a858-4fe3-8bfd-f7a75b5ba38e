<!-- <app-top-banner /> -->
<div class="mb-6 flex flex-wrap items-center justify-between sm:mt-11 gap-4 lg:mb-8">
  <h2 class="text-3xl">{{ "Dashboard" | translate }}</h2>
 
</div>

<div class="grid grid-cols-12 gap-4 xxl:gap-6">
 
  <div class="col-span-12 flex flex-col gap-4 md:col-span-7 lg:col-span-8 xxl:gap-6">
    <!-- Statistics -->
    <div
      class="xxl:box grid grid-cols-6 p-0 max-xxl:gap-4 xxl:divide-x-2 xxl:divide-dashed xxl:p-4 xxl:dark:divide-n500 4xl:p-8 xxl:ltr:divide-n30 xxl:rtl:divide-x-reverse">
      @for (item of statisticsData; track $index) {
      <div
        class="max-xxl:box col-span-6 ltr:border-r-6 rounded-none rtl:border-l-blue-900 ltr:border-r-blue-900 rtl:border-l-6 flex items-center justify-between gap-3 overflow-x-hidden sm:col-span-3 md:col-span-6 lg:col-span-3 xxl:col-span-2 xxl:px-4 xxl:ltr:first:pl-0 xxl:last:ltr:pr-0">
        <div>
          <p class="mb-4 font-medium">{{ item.title }}</p>
          <div class="flex items-center gap-2">
            <h4 class="h4">{{ item.amount }}</h4>
             <!-- <p class="font-[3px]">{{item.avarage}}</p> -->
            <span class="block items-center gap-1 text-sm text-primary">
             <p class="text-sm text-nowrap">{{item.avarage}}</p>
              <i class="las la-arrow-up text-base"></i> {{ item.growth ||0 }}
            </span>
          </div>
        </div>
        <div>
          <apx-chart [chart]="statchartOptions.chart" [grid]="statchartOptions.grid!"
            [dataLabels]="statchartOptions.dataLabels!" [stroke]="statchartOptions.stroke!"
            [series]="statchartOptions.series" [tooltip]="statchartOptions.tooltip!" [colors]="statchartOptions.colors!"
            [fill]="statchartOptions.fill!" [xaxis]="statchartOptions.xaxis!" [yaxis]="statchartOptions.yaxis!" />
        </div>
      </div>
      }
    </div>

    <!-- Income and expences -->
    <div class="box overflow-x-hidden">

      <div class="border-b border-primary-500 dark:border-gray-700 bb-dashed">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400">
          <li class="me-2" *ngFor="let tab of tabs">
            <a (click)="changeTab(tab)" [id]="tab" [ngClass]="{ 'border-b-primary': activeTab.field === tab.field }"
              class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group transition ease-in-out duration-300">
              {{ tab.title! | translate }}
            </a>
          </li>
        </ul>
      </div>

      @switch (activeTab.field) { @case("Investments") {
      <apx-chart *ngIf="incomeChartOptions.series?.length" [chart]="incomeChartOptions.chart"
        [stroke]="incomeChartOptions.stroke!" [dataLabels]="incomeChartOptions.dataLabels!"
        [markers]="incomeChartOptions.markers!" [colors]="incomeChartOptions.colors!"
        [xaxis]="incomeChartOptions.xaxis!" [yaxis]="incomeChartOptions.yaxis!" [tooltip]="incomeChartOptions.tooltip!"
        [fill]="incomeChartOptions.fill!" [responsive]="incomeChartOptions.responsive"
        [series]="incomeChartOptions.series" [legend]="incomeChartOptions.legend!" />
      } @case("DepositsList") {
    <apx-chart 
  *ngIf="depositsChartOptions.series?.length" 
  [chart]="depositsChartOptions.chart"
  [stroke]="depositsChartOptions.stroke!" 
  [dataLabels]="depositsChartOptions.dataLabels!"
  [markers]="depositsChartOptions.markers!" 
  [colors]="depositsChartOptions.colors!"
  [xaxis]="depositsChartOptions.xaxis!" 
  [yaxis]="depositsChartOptions.yaxis!"
  [tooltip]="depositsChartOptions.tooltip!" 
  [fill]="depositsChartOptions.fill!"
  [responsive]="depositsChartOptions.responsive" 
  [series]="depositsChartOptions.series"
  [legend]="depositsChartOptions.legend!">
</apx-chart>
      } @case("WithdrowsList") {
      <apx-chart *ngIf="withdrawlChartOptions.series?.length" [chart]="withdrawlChartOptions.chart"
        [stroke]="withdrawlChartOptions.stroke!" [dataLabels]="withdrawlChartOptions.dataLabels!"
        [markers]="withdrawlChartOptions.markers!" [colors]="withdrawlChartOptions.colors!"
        [xaxis]="withdrawlChartOptions.xaxis!" [yaxis]="withdrawlChartOptions.yaxis!"
        [tooltip]="withdrawlChartOptions.tooltip!" [fill]="withdrawlChartOptions.fill!"
        [responsive]="withdrawlChartOptions.responsive" [series]="withdrawlChartOptions.series"
        [legend]="withdrawlChartOptions.legend!" />
      } }
      <!-- -->
    </div>
  </div>

  <div class="col-span-12 md:col-span-5 lg:col-span-4 ">
    <div class="box bg-slate-50 shadow-lg">
      <h4 class="h5 mb-4 pb-2 lg:mb-3 lg:pb-3 text-center mx-auto">
        {{ "myWallet" | translate }}
      </h4>
       <div class="h4 bb-dashed mb-2 pb-2 lg:mb-2 lg:pb-2 text-center">
        {{authService.userBalance() || 0}}
        <span class="text-gray-600">
          <img src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg" alt="SAR" width="16"
            height="16" class="inline ml-1" />
        </span>
          <div class="flex items-center justify-center gap-3  xxl:gap-4 ">
        <div class="flex">
          <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4 px-4 py-2">
            <!-- Bank Partner Row -->
            <div
              class="col-span-2 text-black dark:text-inherit text-base sm:text-lg font-medium text-start my-2 sm:my-6">
              {{ "bankpartner" | translate }}
            </div>
            <div
              class="text-[#015C8E] dark:text-inherit text-sm sm:text-base justify-start text-end -mx-4 font-semibold text-nowrap my-auto">
              {{ CollectioncountData?.bank }}
            </div>
          </div>
        </div>
          </div>
      
        </div>
    
      <div class="mb-6 flex items-center justify-center gap-3 lg:mb-8 xxl:gap-4 bb-dashed">
        <div class="flex">
          <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4 px-4 py-2">
            <!-- Bank Partner Row -->
            <div
              class="col-span-2 text-black dark:text-inherit text-base sm:text-lg font-medium text-start my-2 sm:my-6">
              {{ "the Name" | translate }}
            </div>
            <div
              class="text-[#015C8E] dark:text-inherit text-sm sm:text-base justify-start text-end -mx-4 font-semibold text-nowrap my-auto">
              {{ CollectioncountData?.name ||("notfound" |translate)}}
            </div>

            <!-- Account Number Row -->
            <div class="text-black dark:text-inherit text-xs sm:text-sm text-start my-auto">
              {{ "AccountNo" | translate }}
            </div>
            <div
              class="col-span-2 text-[#015C8E] dark:text-inherit justify-end text-sm sm:text-base font-semibold text-end my-auto mx-6">
              {{ CollectioncountData?.accountNumber }}
            </div>

            <!-- IBAN Row -->
            <div class="text-black dark:text-inherit text-xs sm:text-sm text-start my-auto">
              {{ "Iban" | translate }}
            </div>
            <div
              class="col-span-2 text-[#015C8E] dark:text-inherit text-xs justify-end px-4 sm:text-sm font-semibold text-end my-auto break-all rtl:-ml-10 rtl:-mr-2 ltr:-mr-10 ltr:-ml-12">
              {{ CollectioncountData?.iban }}
            </div>

            <!-- Empty Div for spacing -->
            <div class="col-span-3 h-4"></div>
          </div>

        </div>
      </div>
      <div class="text-[#B1B1B2] mx-8 py-4">{{"candepositinvestmentamount"|translate}}</div>
    </div>
  </div>

  <!-- Latest Transactions -->
  <div class="col-span-12">
    <div class="box col-span-12 lg:col-span-6">
      <!-- Title Section - Full width above table -->
      <div class="mb-4 w-full border-b border-dashed border-gray-200 pb-4 dark:border-gray-700">
        <h4 class="text-lg font-bold text-gray-900 dark:text-white">
          {{ "latestInvestments" | translate }}
        </h4>
      </div>

      <!-- Table Section - Full width below title -->
      <div class="mt-5 w-full overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="datatable min-w-full">
          <ng-datatable (changeServer)="handePageChange($event.current_page)" [columns]="columns"
            [isServerMode]="search.isServerMode" [loading]="investmentService.pagination.loading"
            [page]="investmentService.pagination.page" [rows]="investments" [showPageSize]="false"
            [totalRows]="investmentService.pagination.totalRows" class="w-full whitespace-nowrap"
            firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
            nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            paginationInfo=" {{
              'pagination_info'
                | translate : { start: '{0}', end: '{1}', total: '{2}' }
            }}"
            previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'>
            <ng-template let-value="data" slot="investDate">
              {{ value.investDate | dateFormat }}
            </ng-template>

            <ng-template let-value="data" slot="dueDate">
              {{ value.dueDate | dateFormat }}
            </ng-template>

            <ng-template let-value="data" slot="actualDueDate">
              {{ value.actualDueDate | dateFormat }}
            </ng-template>

            <ng-template let-value="data" slot="statusName">
              <span [ngClass]="value.statusName"
                class="block w-28 rounded-[30px] border border-n30 bg-primary/10 py-2 text-center text-xs text-primary dark:border-n500 dark:bg-bg3 xxl:w-36">
                {{ value.statusName | translate }}
              </span>
            </ng-template>
          </ng-datatable>
        </div>
      </div>
    </div>
  </div>

  @if (modalService.isOpen('acceptLegalDocument')) {
  <app-accept-legal-document />
  }

  <!----------------------------------------------------------------------------->
</div>