export interface IStatement {
  accountId: string;
  journalLines: IJournalLine[];
}

export interface IJournalLine {
  title: string;
  titleEn: string;
  journalEntryId: string;
  ledgerAccountId: string;
  ledgerAccountName: string | null;
  ledgerAccountCode: string | null;
  journalLineType: number;
  debit: number;
  credit: number;
  runningBalance: number;
  account: any | null; // Type can be specified if account structure is known
  journalEntry: IJournalEntry;
  id: string;
  notes: string | null;
  lastUpdateBy: string | null;
  lastUpdateById: string | null;
  lastUpdateDate: string;
  deletedAt: string | null;
  isDeleted: boolean;
  
}

export interface IJournalEntry {
  code: string;
  description: string;
  descriptionEn: string;
  entryDate: string;
  referenceId: string;
  isAutomatedEntry: boolean;
  fiscalYearId: string | null;
  fiscalYear: any | null; // Type can be specified if fiscal year structure is known
  journalLines: any | null; // This will be IJournalLine[] but marked as null in this context
  id: string;
  notes: string;
  lastUpdateBy: string;
  lastUpdateById: string;
  lastUpdateDate: string;
  deletedAt: string | null;
  isDeleted: boolean;
}