import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CalendarModule } from 'primeng/calendar';
import { ToastService } from '../../../../../core/services/toast.service';
import { LoanRequestService } from '../../Service/loan-request.service';
import { ModalService } from '@component/form/modals/modal.service';
import { ChangeStatusComponent } from '../../../change-status/change-status.component';
import { Transaction } from '../../interface/financing.model';
import { DateFormatPipe } from '../../../../../shared/pipe/date-format.pipe';
import { PaymentRequestSearch } from '../../../deposits/models/deposit.model';
import { OtpService } from '../../../../../shared/otp/services/otp.service';
import { OtpModalComponent } from '../../../../../shared/otp/otp-modal/otp-modal.component';
import { OperationType } from '../../../../../core/enums/operation-type.enum';

@Component({
  selector: 'app-loan-request-scores',
  standalone: true,
   imports: [
     DataTableModule,
       CalendarModule,
       FormsModule,
       CommonModule,
       TranslateModule,
     DateFormatPipe,
    OtpModalComponent

     ],
     providers: [
       DatePipe
     ],
  templateUrl: './loan-request-scores.component.html',
  styleUrl: './loan-request-scores.component.css'
})
export class LoanRequestScoresComponent implements OnInit{
  @Input() loanRequest!: any;
  @Input() id: string = '';
  response: any;
  accepted: any;
  isLoading: boolean = true;
  currentUserId:any;
  requestStatus:any;
  submitData: any;
  cards = [
    { title: 'GrantedFinancingAmount', body: '' },
    { title: 'CreditRating', body: '' },
    { title: 'AnnualYield', body: '' },
    { title: 'ReturnonFinancing', body: '' },
    { title: this.translate.instant('FinancingDuration'), body: '' },
    { title: this.translate.instant('AdministrativeFees'), body: '' },

  ];
  tableData = [
    { col1: 'Row 1 Col 1', col2: 'Row 1 Col 2', col3: 'Row 1 Col 3' },
    { col1: 'Row 2 Col 1', col2: 'Row 2 Col 2', col3: 'Row 2 Col 3' }
  ];
  agreementList: [] = [];
  columns: colDef[] = [];
  approverColumns: colDef[] = [];
  apiData: Transaction[] = [
    { principalAmount: '1000', profit: '150', date: new Date('2025-05-01') },
    { principalAmount: '2000', profit: '250', date: new Date('2025-05-10') }
  ];
  transactionsForm: FormGroup;
  approvelList: any
  status: any
  totalRecords: number = 0;

   search: PaymentRequestSearch = {isServerMode: true} as PaymentRequestSearch;
  constructor(private fb: FormBuilder,
     protected LoanRequestService: LoanRequestService,
     private translate: TranslateService,
     protected modalService: ModalService,
           private otpService: OtpService,
            private router: Router,) {
    this.transactionsForm = this.fb.group({
      transactions: this.fb.array([])
    });
    this.LoanRequestService.initPagination();

  }
  ngOnInit(): void {

    this.getLoanRequestScores();
    this.setupColumns();
     this.isLoading = false;
  this.totalRecords = this.apiData.length;
  }






async getLoanRequestScores() {
    await this.LoanRequestService.getLoanRequestScore(this.loanRequest.id).then((response: any) => {

      this.cards[0].body = response?.data?.grantedAmount || 0
      this.cards[1].body = response?.data?.score || 0
      this.cards[2].body = response?.data?.apr || 0
      this.cards[3].body = response?.data?.companySharePer || 0
      this.cards[4].body = response?.data?.duration || 0
      this.cards[5].body = response?.data?.adminFees
      this.status = response?.data?.status
      this.apiData = response?.data.loanRequestInstallmentDto

      this.accepted = true
    })
    if (this.status == 2) {
      this.accepted = true

    }

  }

  setupColumns() {
    this.columns = [
      { title: this.translate.instant('PrincipalAmount'), field: 'amount' },
      { title: this.translate.instant('Profits'), field: 'profit' },
          { title: this.translate.instant('DueDate'), field: 'dueDate' },

    ];

  }
    async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getLoanRequestScores();
  }

  currentAction: 'accept' | 'reject' | null = null;

async openOtpModal(action: 'accept' | 'reject') {
    this.currentAction = action;
    try {
        await this.otpService.send({operationType: OperationType.Client_Approve_Loan_Request})
        this.modalService.open('otp-change-password-modal')
    } catch (e: any) {
        // Handle error
    }
}

ApprovalscoreloanRequest(action: 'accept' | 'reject') {
    this.openOtpModal(action);
}





async submitAccptance(otp: string) {
    if (!this.currentAction) return;

    const status = this.currentAction === 'accept' ? 26 : 10;
    const formData = new FormData();
    formData.append('status', status.toString());
    formData.append('otp', otp); // Use the OTP from the modal
    formData.append('LoanRequestId', this.loanRequest.id);

    try {
        this.submitData = await this.LoanRequestService.submitLoanRequest(formData);

        if (this.submitData.statusCode == 200) {
            this.accepted = (status === 26);
            this.closeOtpModal();
            this.router.navigate(['/loanRequest']);
        } else {
            console.error('Submission failed:', this.submitData.message);
            this.closeOtpModal();
        }
    } catch (error) {
        console.error('API Error:', error);
        this.closeOtpModal();
        // this.toastService.error('Request failed');
    } finally {
        this.currentAction = null; // Reset the action
    }
}
  mapValue(value: any) {
    if (value === true) {
      return 'Yes'
    } else if (value === false) {
      return 'No';
    }
    return value;
  }

  closeOtpModal() {
    this.modalService.close('otp-change-password-modal');
  }

}
