import {Component, Input} from '@angular/core';
import { JsonPipe } from "@angular/common";
import {NgxPaginationModule, PaginationInstance} from "ngx-pagination";
import {DataTableModule} from "@bhplugin/ng-datatable";
import {TooltipModule} from "ng2-tooltip-directive";

@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [
    NgxPaginationModule,
    DataTableModule,
    JsonPipe,
    TooltipModule
],
  templateUrl: './pagination.component.html',
  styleUrl: './pagination.component.scss'
})
export class PaginationComponent {
  @Input() config: PaginationInstance = {
    id: 'custom',
    itemsPerPage: 15,
    currentPage: 1
  }
}
