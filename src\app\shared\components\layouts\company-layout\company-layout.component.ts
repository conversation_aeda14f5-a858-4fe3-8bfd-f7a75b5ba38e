import { Component, inject, input, OnChanges, output, SimpleChanges } from '@angular/core';
import { IUser } from '../../../../modules/auth/models/user.model';
import { FormGroup, FormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ModalService } from '@component/form/modals/modal.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../core/services/toast.service';
import { ICommercialRegistrations } from '../../../../modules/auth/models/commercial-registrations.model';
import { ISendNafathResponse } from '../../../../modules/auth/models/login.model';
import { AuthService } from '../../../../modules/auth/services/auth.service';
import { nationalIDValidator } from '../../../validators/national-id.validator';
import { passwordValidator } from '../../../validators/password.validator';
import { phoneNumberValidator } from '../../../validators/phone.validator';
import { CommonModule, DatePipe } from '@angular/common';
import { RegistedCompanyComponent } from '../companies/Registed-company/Registed-company.component';
import { BorrowerComponent } from '@component/Borrower/Borrower.component';
import { BorrowerService } from '@component/Borrower/Borrower.service';

enum RegisterTabs {
  mobile = 0,
  nationalId = 1,
}
@Component({
  selector: 'app-company-layout',
  standalone: true,
  imports: [
     ReactiveFormsModule,
 CommonModule,
     TranslateModule,
     RegistedCompanyComponent,
     BorrowerComponent
   ],
   providers: [
     DatePipe
   ],
  templateUrl: './company-layout.component.html',
  styleUrl: './company-layout.component.css'
})
export class CompanyLayoutComponent  implements OnChanges{
 mobileForm!: FormGroup
  nationalIdForm!: FormGroup
  commercialRegistrationForm!: FormGroup


  isPasswordHidden = true;
  selectedRegisterTab = RegisterTabs.mobile
  response: ISendNafathResponse = {} as ISendNafathResponse;
  commercialRegistrations: ICommercialRegistrations[] = [] as ICommercialRegistrations[];
  #authService:AuthService = inject(AuthService)
  protected readonly RegisterTabs = RegisterTabs;
  update =input<boolean>()
  isSelectCompanyShow = JSON.parse(localStorage.getItem('selectedCompanyIndex')??'false')
  isSelectedBorrower = JSON.parse(localStorage.getItem('selectedCompanyIndex')??'false')
  changeStatus = output<boolean>()
  images = [
    { src: 'assets/images/Buildings-Varied.svg', alt: 'Investor', text:this.translate.instant ('Registered Company on your ID') },
    { src: 'assets/images/Online-Learning.svg', alt: 'Brower', text:this.translate.instant ('Enter the companys commercial register')  },

  ];

  constructor(
    private fb: FormBuilder, private router: Router,
    protected modalService: ModalService,
    private authService: AuthService,
    public translate: TranslateService,
    private toast: ToastService
  ) {
  }

  ngOnInit() {
    localStorage.removeItem('firstStep')
    if(this.isSelectCompanyShow){
      this.selectImage(0)
    }
  }

  selectedImageIndex: number | null = null;

  selectImage(index: number): void {

    if(index ===0){
      this.isSelectCompanyShow = true
      this.isSelectedBorrower=false
    }else{
      this.isSelectCompanyShow = true
      this.isSelectedBorrower=true
    }
    localStorage.setItem('selectedCompanyIndex',this.isSelectCompanyShow.toString())
    this.selectedImageIndex = index;
  }


  addAnotherCompany(event?:boolean){
    this.isSelectCompanyShow = true
    this.isSelectedBorrower=true
  }
  closeStatuts(e:boolean){
   this.changeStatus.emit(true)

  }
  closeStatuts2(e:number){
    localStorage.setItem('firstStep','true')
    this.isSelectedBorrower=false;
    this.isSelectCompanyShow=true
  }

  nextStep(){

  }

ngOnChanges(changes: SimpleChanges): void {

}
goBackStep(){
  this.authService.selectedSubAccount.set(undefined)
  }
  handleGoBack() {
    this.isSelectCompanyShow = !this.isSelectCompanyShow;
  }
}
