import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CalendarModule } from 'primeng/calendar';
import { ToastService } from '../../../../../core/services/toast.service';
import { OpportunitiesService } from '../../services/opportunities.service';

@Component({
  selector: 'app-opportunity-financial-information',
  standalone: true,
 imports: [
      DataTableModule,
        CalendarModule,
        FormsModule,
        CommonModule,
        TranslateModule,
      ],
      providers: [
        DatePipe
      ],
  templateUrl: './opportunity-financial-information.component.html',
  styleUrl: './opportunity-financial-information.component.css'
})
export class OpportunityFinancialInformationComponent {
@Input() OpportunityId!: string;
     loanrequestData: any;
   OpportunityData:any;
   loanRequestDetailData:any;
     listOfSelectedItems:any
companyInfoData:any;
  activeTab!: colDef;
     tabs!: Array<colDef>;
     constructor(
       private toastr: ToastService,
       public translate: TranslateService,
       protected opportunitiesService: OpportunitiesService,
       private route: ActivatedRoute,
        private router: Router,
     ) {}
    
   
     async ngOnInit() {
     
         await this.getcompanyFinancialdata();
        this.route.params.subscribe((params:any) => {
          this.OpportunityId=params['id'];
          ;})

      this.tabs = [
   
      {
        title:this.translate.instant("finianciallist"),
        field:"finianciallist"
      },
      {
        title:this.translate.instant("incomelist"),
        field:"incomelist"
      },
      {
        title:this.translate.instant("Cashflows"),
        field:"Cashflows"
      },

    ];
        this.activeTab= this.tabs[0]
     }
  
     

async getcompanyFinancialdata() {
  try {
    this.route.params.subscribe(async (params) => {
      this.OpportunityId = params['id'];
      
   
      const res = await this.opportunitiesService.getOpportunityById(this.OpportunityId);
      // console.log('Opportunity Data:', res);
      
     
      if (!res) {
        this.toastr.error('No data received from API');
        return;
      }
      
      // Access data based on your actual API structure
      this.OpportunityData = res.opportunity || res;
      this.loanRequestDetailData = res.loanRequestDetail ;
      this.companyInfoData	 = res.companyFinancialInfoDto ;
      console.log('Company Info Data:', this.companyInfoData);
      
  
    });
  } catch (e: any) {
    console.error('Error:', e);
    this.toastr.error(e.message || 'Failed to load opportunity data');
  }
}
    
async previewFile(filePath: string) {
  try {
    const file = this.listOfSelectedItems.find((item: any) => item.id === filePath);
    if (file && file.filePath) {
      window.open(file.filePath, '_blank'); // This will open the file in a new tab
    }
  } catch (e: any) {
    this.toastr.error(e);  
  }
}

 changeTab(selectedTab : colDef)
  {
    this.activeTab= selectedTab;
  }

       goBack() {
    this.router.navigate(['/loanRequest']);
  }
   
     get currentLang() {
      return this.translate.currentLang;
    }
      getLabel(key: string): string {
    return this.translate.instant(key);
  }
   }
   