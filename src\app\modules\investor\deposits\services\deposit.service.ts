import { Injectable } from '@angular/core';
import {DepositRequestModel, DepositRequestSearch, PaymentRequestModel, PaymentRequestSearch} from "../models/deposit.model";
import { PAGINATION } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';
import { BaseResponse } from '../../../../core/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class DepositService {

   pagination = PAGINATION;
   private path='/client/deposits'
 

  constructor(
    private apiService: ApiService,
  ) {
  }

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);



  async getList(query?: DepositRequestSearch) {
    query = {...query, limit: this.pagination.pageSize} as DepositRequestSearch;
    const response:any  = await this.apiService.post<BaseResponse<DepositRequestModel>>(`${this.path}`, query);
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response.data;
  }
}
