import { AuthService } from './../../../../modules/auth/services/auth.service';
import {Component, effect, forwardRef, HostListener, inject, OnInit, signal} from '@angular/core'
import {RouterLink, RouterLinkActive, RouterOutlet} from '@angular/router'
import { CommonModule, Location, NgSwitch, NgSwitchCase } from '@angular/common';
import {Observable} from 'rxjs'
import {Store} from '@ngrx/store'
import {ThemeCustomizerComponent} from '@component/shared/theme-customizer/theme-customizer.component'
import {NotificationDropdownComponent} from '@component/topbar/notification/notification.component'
import {ProfileDropdownComponent} from '@component/topbar/profile-dropdown/profile-dropdown.component'
import {LanguageComponent} from '@component/topbar/language/language.component'
import {LayoutState} from '@store/reducer'
import {toggleDarkMode} from '@store/actions'
import {DialogModule} from 'primeng/dialog'
import {CalendarModule} from 'primeng/calendar'
import {FormControl, FormGroup, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators} from '@angular/forms'
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import { sidebarData } from '../../../../core/data/sidebar';
import { NpxDropdownComponent } from '@component/shared/npx_dropdown/npx_dropdown.component';
import { countries } from '../../../country/list.country';
import { ProfileService } from '../../../service/profile.service';
import { InputComponent } from '@component/form/input/input.component';
import { CompanyLayoutComponent } from '../company-layout/company-layout.component';
import { EncryptStorage } from 'encrypt-storage';
import { UserAccountStatus } from '../../../../modules/auth/models/user.model';
import { SelectAccountComponent } from '../../../../modules/auth/select-account/select-account.component';
import { CreateSubAccountsComponent } from '@component/topbar/create-sub-accounts/create-sub-accounts.component';
import { IncomeRange, InvestmentExperience, JobInformation, LookupTypeKYC, RiskTolerance } from '../enums/layout.enum';
import { DepositCreateComponent } from '../../../../modules/investor/deposits/deposit-create/deposit-create.component';
import { ModalService } from '@component/form/modals/modal.service';

@Component({
  selector: 'app-default-layout',
  standalone: true,
  imports: [
    RouterOutlet,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    SelectAccountComponent,
    RouterLink,
    InputComponent,
    RouterLinkActive,
    CompanyLayoutComponent,
    CreateSubAccountsComponent,
    TranslateModule,
    ThemeCustomizerComponent,

    DepositCreateComponent,
    NotificationDropdownComponent, LanguageComponent, ProfileDropdownComponent, DialogModule, CalendarModule, FormsModule, TranslateModule,NpxDropdownComponent],
  templateUrl: './default-layout.component.html',
  styles:[`:host ::ng-deep{
    app-npx-dropdown{
      width: 100% !important;
      height: 48px !important;
    }
    app-input{
      width:32rem !important;
    }

  } `],
    providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NpxDropdownComponent),
      multi: true,
    },
  ],
})
export class DefaultLayoutComponent implements OnInit {
  RegisterForm!: FormGroup
  encryptStorage = new  EncryptStorage('User_info_login');
  encryptStorageStep = new  EncryptStorage('User_info_step');
  Collectioncount:any
  CollectioncountData:any
  BalanceCount:any
  isDepositCreateVisible: boolean = false;
  LookupTypeKYC!:LookupTypeKYC
  KYCData:any
  educationLevel:any
  annualIncomeRange:any
  sourceOfIncome:any
  totalAssetsRange:any
  nationalities:any
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')

  listOcCountries=countries
  quickForm!:FormGroup
  notCompleteUser= signal<boolean>(false)
  userMainAccount= signal<number>(1)
  userType= signal<number>(1)
  layout$: Observable<LayoutState>
  visible = false
  isSidebarOpen = false
  date: Date | undefined

  status = ['active', 'inactive']
  colorMode = ''
  sidebarData = sidebarData
  activeMenu = sidebarData[0].name
  filteredSidebarData: typeof sidebarData = [];
  private store = inject(Store)
  #profileApi:ProfileService = inject(ProfileService)
  createSubAccount=signal<boolean>(false)
  isComplete=signal<boolean>(false)
  isKYCPending=signal<boolean>(false)
  accountstatuspending:any
  constructor(private location: Location,
  public authService:AuthService,
  private translate: TranslateService,

   private modalService: ModalService,
  ) {

    this.layout$ = this.store.select('layout')
    effect(() => {
      if(this.authService.addSubAccount()){
        this.notCompleteUser.set(true)
        this.createSubAccount.set(true)
      }

      if(this.authService.selectedSubAccount()){
        this.notCompleteUser.set(true)
        this.createSubAccount.set(false)
        localStorage.removeItem('selectedCompanyIndex')
        this.userType.set(this.authService.selectedSubAccount() as number)
      }
      this.accountstatuspending =  this.#encryptTokenStorage.getItem('currentSubInfo')?.userAccountStatus == UserAccountStatus.KYCPending
      this.isKYCPending.set(this.authService._currentUserSubject?.userAccountType == UserAccountStatus.KYCPending || this.accountstatuspending)
    },{allowSignalWrites: true})
 this.initalForm();
  }


  setActiveMenu(name: string) {
    const user = this.encryptStorage.getItem('userInfo');
    if(user.userAccountStatus != UserAccountStatus.KYCPending){
      this.notCompleteUser.set(false)
    }
    this.activeMenu == name ? (this.activeMenu = '') : (this.activeMenu = name)
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen
  }

  toggleDarkMode() {
    this.store.dispatch(toggleDarkMode({newTheme: this.colorMode == 'light' ? 'dark' : 'light'}))
  }
  changeStatus(event:boolean) {
    if(event){
      if(this.checkIfUserIsPendingKYC()){
        if(this.userType() != 1){
          this.notCompleteUser.set(true)
          this.createSubAccount.set(false)
          this.userType.set(1)
        }else{
          this.notCompleteUser.set(false)
        }
      }else{
      this.notCompleteUser.set(false)
      }
    }
  }
profile:any
profileData:any
profilebalance:any
async getProfile() {
  try {
    // Check if profile exists in local storage
    const encryptedProfile = await this.#encryptTokenStorage.getItem('profile');
    
    if (encryptedProfile) {
      // Decrypt and parse the profile data
      this.profileData = encryptedProfile;
      this.authService.userBalance.set(this.profileData?.balance);
      return this.profileData; // Return the stored data
    }

    // If not in local storage, fetch from API
    this.profile = await this.authService.getprofileAccount();
    
    if (this.profile?.data) {
      // Store the fresh data in encrypted local storage
      await this.#encryptTokenStorage.setItem('profile', this.profile.data);
      
      this.profileData = this.profile.data;
      this.authService.userBalance.set(this.profile.data?.balance);
    }

    return this.profileData;
  } catch (error) {
    console.error('Error in getProfile:', error);
    throw error; // Consider re-throwing or handling the error appropriately
  }
}
  // async getProfile(){
  //   // if(this.#encryptTokenStorage.getItem('profile')){
  //   //   this.profileData= this.#encryptTokenStorage.getItem('profile')
  //   //   return
  //   // }
  //   try{
  //     this.profile= await  this.authService.getprofileAccount();
  //     this.#encryptTokenStorage.setItem('profile',this.profile?.data)
 

  //     this.profileData= this.profile?.data
  // this.profilebalance=    this.authService.userBalance.set(this.profile?.data?.balance)
  //   }catch{
  //     console.log('error')
  //   }

  // }

async getKYCFORM() {
  try {
    this.KYCData = await this.authService.getlookupsKYC();
 this.nationalities = this.KYCData.data
  .filter((item: any) => item.type === LookupTypeKYC.Nationalities)
  .map((item: any) => ({
    value: item.id,  
    key: this.translate.currentLang === 'ar' ? item.nameAr : item.nameEn  
  }));
 this.educationLevel = this.KYCData.data
  .filter((item: any) => item.type === LookupTypeKYC.EducationLevel)
  .map((item: any) => ({
    value: item.id,  
    key: this.translate.currentLang === 'ar' ? item.nameAr : item.nameEn  
  }));
 this.annualIncomeRange = this.KYCData.data
  .filter((item: any) => item.type === LookupTypeKYC.AnnualIncomeRange)
  .map((item: any) => ({
    value: item.id,  
    key: this.translate.currentLang === 'ar' ? item.nameAr : item.nameEn  
  }));
 this.sourceOfIncome = this.KYCData.data
  .filter((item: any) => item.type === LookupTypeKYC.SourceOfIncome)
  .map((item: any) => ({
    value: item.id,  
    key: this.translate.currentLang === 'ar' ? item.nameAr : item.nameEn  
  }));
 this.totalAssetsRange = this.KYCData.data
  .filter((item: any) => item.type === LookupTypeKYC.TotalAssetsRange)
  .map((item: any) => ({
    value: item.id,  
    key: this.translate.currentLang === 'ar' ? item.nameAr : item.nameEn  
  }));

 
      
  } catch (error) {
    console.error('Error loading nationalities:', error);
  }
}
  async getBalance() {
    try {
      this.BalanceCount = await this.authService.getBalance();
      this.authService.userBalance.set(this.BalanceCount);
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  }
  onModalClick(event: MouseEvent) {
    event.stopPropagation()
  }
  handl1(event:any){

    this.quickForm.get('educationLevel')?.setValue(event)
  }
  getCurrentYear() {
    return new Date().getFullYear()
  }
  useraccountType:any
  usbInfoUser:any
  async ngOnInit(): Promise<void> {
    // const userAccounts = await this.authService.getAccounts()
    const x = this.#encryptTokenStorage.getItem('currentSubInfo')?.userAccountStatus == UserAccountStatus.KYCPending
    this.isKYCPending.set(this.authService._currentUserSubject?.userAccountType == UserAccountStatus.KYCPending || x)
    this.useraccountType = this.authService._currentUserSubject? this.authService._currentUserSubject.userAccountType:
     !!this.#encryptTokenStorage.getItem('currentUser') ? this.#encryptTokenStorage.getItem('currentUser')?.userAccountType :this.encryptStorageStep.getItem('selected_type') !== null ?
     this.encryptStorageStep.getItem('selected_type') :  1
    this.userMainAccount.set(this.useraccountType)
    // console.log(this.userMainAccount())

    const user = this.#encryptTokenStorage.getItem('currentUser');
    this.usbInfoUser = this.#encryptTokenStorage.getItem('currentSubInfo')
    this.userType.set(user?.userAccountType|| this.usbInfoUser?.userAccountType)

    this.notCompleteUser.set(user?.userAccountStatus == UserAccountStatus.KYCPending || this.usbInfoUser?.userAccountStatus == UserAccountStatus.KYCPending)
      this.authService.selectedSubAccount.set(undefined)
      this.authService.addSubAccount.set(false)
    // Initial check when the component is initialized
    this.layout$.subscribe((theme) => {
      this.colorMode = theme.theme
    })
    this.handleWindowResize()

   // sidebarData.map(({ name, url }) => (url == this.location.path() ? (this.activeMenu = name) : ''))
    this.filteredSidebarData = this.getFilteredSidebarData(sidebarData);

   this.getBalance();
    await this.getProfile();
    await this.getKYCFORM();
  }

  getFilteredSidebarData(sidebarData: any[]): any[] {
    const userDataString =  this.#encryptTokenStorage.getItem('currentUser')?? this.#encryptTokenStorage.getItem('currentSubInfo')??null;
    const currentPath = this.location.path();
    if (!userDataString) return sidebarData;
    const userData = userDataString;
    const userType = userData.userAccountType;
    const filteredData = sidebarData.filter(item => {
      if (item.url === currentPath) {
        this.activeMenu = item.name;
      }
      if (userType === 1 || userType === 2 || userType === 0) {
        return item.id !== 4 && item.id !== 9 && item.id !== 8 && item.id !== 10 ;
      } else if (userType === 3) {
        return item.id == 4|| item.id === 1 ||item.id === 10 ||item.id === 9 || item.id== 11 ||item.id==2;
      }
        return true;
    });
    return filteredData;
  }

  initalForm(){
    this.quickForm = new FormGroup({
      haveExperience: new FormControl(false,Validators.required),
      educationLevel: new FormControl(null,Validators.required),
      annualIncome: new FormControl(null,Validators.required),
      sourceOfIncome: new FormControl(null,Validators.required),
      pureOwnership: new FormControl(null,Validators.required),
      nationality: new FormControl(null,Validators.required),
      isPoliticallyExposedPerson:new FormControl(false,Validators.required),
      isRealBeneficiary:new FormControl(false,Validators.required),
      isAssociatedWithPEP:new FormControl(false,Validators.required),
      isBoardMember:new FormControl(true,Validators.required),
      jobTitle:new FormControl(null,Validators.required),
      companyName:new FormControl(null,Validators.required),
      companyAddress:new FormControl(null,Validators.required),
    })
  }

  ngOnDestroy(): void {
    // Clean up by removing event listener when the component is destroyed
    window.removeEventListener('resize', this.handleWindowResize)
  }
  openCreateDeposit() {

    this.modalService.open('deposit-create')
  }
  @HostListener('window:resize', ['$event'])
  handleWindowResize(event?: Event) {
    // Check window width and set isSidebarOpen accordingly
    this.isSidebarOpen = window.innerWidth > 1200
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if the click event target is not within the sidebar
    if (window.innerWidth < 1200) {
      if (!document.querySelector('.sidebar')!.contains(event.target as Node) && !document.querySelector('#sidebar-toggle-btn')!.contains(event.target as Node)) {
        this.isSidebarOpen = false // Close the sidebar
      }
    }
  }

  clickRoute() {
    if (window.innerWidth < 1200) {
      this.isSidebarOpen = false
    }
  }
  response:any
  // async sendRequest() {

  //   try{
  //     const res = await this.#profileApi.postProfileIndividual(this.quickForm.getRawValue())
  //     this.isComplete.set(true)
  //    this.response= await this.authService.getAccounts();
  //    console.log( this.response,' this.response');
  //     if(res){


  //       this.notCompleteUser.set(false)
  //     }
  //   const currentAccountStatus =JSON.parse(localStorage.getItem('currentSubInfo')??'null')
  //         currentAccountStatus.userAccountStatusName = 'PendingApproval'
  //         currentAccountStatus.userAccountStatus = 1
  //         localStorage.setItem('currentSubInfo',JSON.stringify(currentAccountStatus))
  //   }catch(e){}
  // }


  async sendRequest() {
    try {
        const res = await this.#profileApi.postProfileIndividual(this.quickForm.getRawValue());
        this.isComplete.set(true);
        this.response = await this.authService.getAccounts();
      

        if (res) {
            this.notCompleteUser.set(false);
        }
        // Retrieve current account status from local storage
        const currentAccountStatus = this.#encryptTokenStorage.getItem('currentSubInfo');

        if (currentAccountStatus) {
            currentAccountStatus.userAccountStatusName = 'Approved'; // Set status name to Approved
            currentAccountStatus.userAccountStatus = 2;
            currentAccountStatus.userAccountType = 1;

            this.#encryptTokenStorage.setItem('currentSubInfo',currentAccountStatus);
           this.filteredSidebarData =  this.getFilteredSidebarData(sidebarData);

           this.getProfile()
        }
    } catch (e) {
        console.error('Error in sendRequest:', e);
    }
}

checkIfUserIsPendingKYC(){
  const x =  this.#encryptTokenStorage.getItem('currentSubInfo')?.userAccountStatus == UserAccountStatus.KYCPending
  return (this.authService._currentUserSubject?.userAccountType == UserAccountStatus.KYCPending || x)
}
  complateBorrower(){
    if(this.checkIfUserIsPendingKYC()){
      if(this.userType() != 1){
        this.notCompleteUser.set(true)
        this.createSubAccount.set(false)
        this.userType.set(1)
      }else{
        this.notCompleteUser.set(false)
      }
    }else{
      this.notCompleteUser.set(false)
    }
  }
}
