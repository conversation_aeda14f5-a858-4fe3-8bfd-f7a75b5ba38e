export interface IUser {
  userID: string
  fullName: string
  nationalId: number
  mobileNo: string
  idNumber: string
  birthDate: string
  birthDateCalnder: string
  email: string
  password: string
  status: number
  statusName: string
  userType: number
  userTypeName: string
  userAccountTypeName:any
  agreeOnTerms: boolean
  ipAddress: string
  agreeOnTermsDate: string
  token: string
  tokenExpiryDate: string
  isDeleted: boolean
  lastUpdateBy: string
  lastUpdateByName: string
  lastUpdateOn: string
  actionName: string
  mfaCode: string
  walletID: string
  walletName: string
  investorType: number
  investorTypeName: string
  iban: string
  account: string
  bankName: string
  roles: Role[]
  permissionCodes: string[]
  company: Company
  failedLoginAttempts: number
  lockoutEnd: string
  focalResponse: string
  boardingIndividualResponse: BoardingIndividualResponse,
  userAccountType:number
}
export enum UserAccountType
{
  IndivdualInvstor = 1,
  InvestmentCompany = 2,
  Borrower = 3,
}
export enum UserAccountStatus
{
    PendingApproval = 1,
    Approved = 2,
    Blocked = 3,
    KYCPending = 4,
    AMLPending = 5,
    PoliticallyExposed = 6,
}
export interface Role {
  roleID: string
  name: string
  description: string
  lastUpdatedBy: string
  lastUpdatedOn: string
  actionName: string
  permissions: Permission[]
}

export interface Permission {
  permissionID: string
  code: string
  nameAr: string
  nameEn: string
  moduleName: string
  isDeleted: boolean
  deletedAt: string
}

export interface Company {
  companyID: string
  name: string
  crNumber: string
  crEntityNumber: string
  issueDate: string
  expiryDate: string
  issueDateG: string
  expiryDateG: string
  crMainNumber: string
  businessTypeID: number
  businessTypeName: string
  fiscalYear: string
  fiscalMonth: string
  fiscalDay: string
  calendarTypeID: number
  calendarTypeName: string
  crStatus: string
  statusNameAr: string
  statusNameEn: string
  cancellation: string
  locationID: number
  locationName: string
  companyPeriod: string
  companyPeriodStart: string
  companyPeriodEnd: string
  status: number
  isRegistered: boolean
  capitalPaidAmount: number
  capitalSubscribedAmount: number
  capitalAnnouncedAmount: number
  capitalSharePrice: number
  capitalSharesCount: number
  website: string
  address: string
  email: string
  fax1: string
  fax2: string
  telephone1: string
  telephone2: string
  postalBox1: string
  postalBox2: string
  buildingNumber: string
  additionalNumber: string
  streetName: string
  city: string
  zipCode: string
  unitNumber: string
  district: string
  isEcommerce: boolean
  wathiqResponse: string
  lastUpdateBy: string
  lastUpdateOn: string
  actionName: string
  activities: Activity[]
  userID: string
  userName: string
  companyOnBoarding: CompanyOnBoarding[]
}

export interface Activity {
  companyActivityID: string
  companyID: string
  id: number
  name: string
  nameEn: string
}

export interface CompanyOnBoarding {
  query_uuid: string
  query_id: string
  fname: string
  mname: string
  lname: string
  orgname: string
  company_registration: string
  commercial_registration: string
  unified_number: string
  iden: string
  iden_type: IdenType
  nat: string
  pob: string
  date: string
  query_type: string
  threshold: number
  customer_reference_id: string
  education_level: EducationLevel
  expected_number_of_deposits: number
  expected_number_of_withdrawals: number
  income_amount: number
  is_pep: boolean
  is_rca: boolean
  hijri_date: string
  query_time: string
  query_status: string
  result_time: string
  is_batch_query: boolean
  batch_name: string
  batch_description: string
  batch_filename: string
  source: string
  decisions_history: string[]
  alert_decisions: string[]
  warnings: string
  highest_match_score: number
  identity_info: string
  personal_info: string
  profession: string
  product_name: string
  channel: string
  country_of_residence: string
  source_of_income: string
  high_risk_transaction: string
  net_worth_category: string
  country_of_incorporation: string
  country_of_operation: string
  owner_nationality: string
  legal_structured: string
  industry: string
  purpose_of_account: string
  product_delivery_method: string
  length_of_relationship: string
  length_of_incorporation: string
  customer_segment: string
  ownership_structure: string
  extra_attributes: ExtraAttributes
  screen_result: string[]
  risk_assessment: RiskAssessment
}

export interface IdenType {
  type: string
}

export interface EducationLevel {
  level: string
}

export interface ExtraAttributes {
  property1: number
  property2: number
}

export interface RiskAssessment {
  query_uuid: string
  customer_reference_id: string
  total_risk: number
  risk_result: string
  alert_risk_values: AlertRiskValues
  outdated_scores: OutdatedScore[]
  result_is_overridden: boolean
  extra_attributes_risk_values: ExtraAttributesRiskValues
  type: string
  nationality_risk_values: NationalityRiskValues
  place_of_birth_risk_values: PlaceOfBirthRiskValues
  age_risk_values: AgeRiskValues
  profession_risk_values: ProfessionRiskValues
  product_name_risk_values: ProductNameRiskValues
  channel_risk_values: ChannelRiskValues
  country_of_residence_risk_values: CountryOfResidenceRiskValues
  source_of_income_risk_values: SourceOfIncomeRiskValues
  high_risk_transaction_risk_values: HighRiskTransactionRiskValues
  net_worth_category_risk_values: NetWorthCategoryRiskValues
  product_delivery_method_risk_values: ProductDeliveryMethodRiskValues
}

export interface AlertRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface OutdatedScore {
  risk_score_table: string
  record_id: number
}

export interface ExtraAttributesRiskValues {
  property1: Property1
  property2: Property2
}

export interface Property1 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface Property2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface NationalityRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface PlaceOfBirthRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface AgeRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProfessionRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProductNameRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ChannelRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface CountryOfResidenceRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface SourceOfIncomeRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface HighRiskTransactionRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface NetWorthCategoryRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProductDeliveryMethodRiskValues {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface BoardingIndividualResponse {
  query_uuid: string
  query_id: string
  fname: string
  mname: string
  lname: string
  orgname: string
  company_registration: string
  commercial_registration: string
  unified_number: string
  iden: string
  iden_type: IdenType2
  nat: string
  pob: string
  date: string
  query_type: string
  threshold: number
  customer_reference_id: string
  education_level: EducationLevel2
  expected_number_of_deposits: number
  expected_number_of_withdrawals: number
  income_amount: number
  is_pep: boolean
  is_rca: boolean
  hijri_date: string
  query_time: string
  query_status: string
  result_time: string
  is_batch_query: boolean
  batch_name: string
  batch_description: string
  batch_filename: string
  source: string
  decisions_history: string[]
  alert_decisions: string[]
  warnings: string
  highest_match_score: number
  identity_info: string
  personal_info: string
  profession: string
  product_name: string
  channel: string
  country_of_residence: string
  source_of_income: string
  high_risk_transaction: string
  net_worth_category: string
  country_of_incorporation: string
  country_of_operation: string
  owner_nationality: string
  legal_structured: string
  industry: string
  purpose_of_account: string
  product_delivery_method: string
  length_of_relationship: string
  length_of_incorporation: string
  customer_segment: string
  ownership_structure: string
  extra_attributes: ExtraAttributes2
  screen_result: string[]
  risk_assessment: RiskAssessment2
}

export interface IdenType2 {
  type: string
}

export interface EducationLevel2 {
  level: string
}

export interface ExtraAttributes2 {
  property1: number
  property2: number
}

export interface RiskAssessment2 {
  query_uuid: string
  customer_reference_id: string
  total_risk: number
  risk_result: string
  alert_risk_values: AlertRiskValues2
  outdated_scores: OutdatedScore2[]
  result_is_overridden: boolean
  extra_attributes_risk_values: ExtraAttributesRiskValues2
  type: string
  nationality_risk_values: NationalityRiskValues2
  place_of_birth_risk_values: PlaceOfBirthRiskValues2
  age_risk_values: AgeRiskValues2
  profession_risk_values: ProfessionRiskValues2
  product_name_risk_values: ProductNameRiskValues2
  channel_risk_values: ChannelRiskValues2
  country_of_residence_risk_values: CountryOfResidenceRiskValues2
  source_of_income_risk_values: SourceOfIncomeRiskValues2
  high_risk_transaction_risk_values: HighRiskTransactionRiskValues2
  net_worth_category_risk_values: NetWorthCategoryRiskValues2
  product_delivery_method_risk_values: ProductDeliveryMethodRiskValues2
}

export interface AlertRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface OutdatedScore2 {
  risk_score_table: string
  record_id: number
}

export interface ExtraAttributesRiskValues2 {
  property1: Property12
  property2: Property22
}

export interface Property12 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface Property22 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface NationalityRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface PlaceOfBirthRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface AgeRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProfessionRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProductNameRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ChannelRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface CountryOfResidenceRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface SourceOfIncomeRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface HighRiskTransactionRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface NetWorthCategoryRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}

export interface ProductDeliveryMethodRiskValues2 {
  value: number
  risk_value: number
  risk_name: string
  risk_weight: number
}
