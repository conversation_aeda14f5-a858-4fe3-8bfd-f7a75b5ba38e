<div class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header">
      <h2>{{modalTitle|translate}}</h2>
      <button (click)="closeModal()" class="close-button">×</button>
    </div>
    <form [formGroup]="form" (ngSubmit)="submitForm()" class="modal-body">
      <p class="modal-text">{{modalDescription|translate}}</p>
      <textarea formControlName="notes" class="textarea" placeholder="إضافة ملاحظات ..."></textarea>
      <p *ngIf="form.controls['notes'].invalid && form.controls['notes'].touched" class="error-text">
        الملاحظات مطلوبة (يجب أن تحتوي على 5 أحرف على الأقل)
      </p>
      <div *ngIf="!isOtpVisible" class="modal-buttons">
        <button
          class="btn-reject"
          (click)="rejectRequest()">
          {{ "Reject ❌" | translate }}
        </button>

        <button
          class="btn-accept "
          (click)="acceptRequest()">
          {{ "Accept ✅" | translate }}
        </button>
      </div>
    </form>
    <div *ngIf="isOtpVisible">
      <p class="modal-text">{{ modalDescription |translate}}</p>
      <form [formGroup]="otpForm" class="w-full" id="otp-form">
        <div class="flex w-full items-center justify-center gap-3" formArrayName="otp">
          <input
          #otpInput
            (input)="handleInput($event, i)"
            (keydown)="handleKeyDown($event, i)"
            (paste)="handlePaste($event)"
            *ngFor="let control of otpArray.controls; let i = index"
            [formControlName]="i"
            [id]="'otp-input-' + i"
            class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
            maxlength="1"
            type="text">
        </div>
        <button
          #submitButton
          class="w-full mt-4 inline-flex justify-center whitespace-nowrap rounded-lg bg-indigo-500 px-3.5 py-2.5 text-sm font-medium text-white shadow-sm shadow-indigo-950/10 hover:bg-indigo-600 focus:outline-none focus:ring focus:ring-indigo-300 focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 transition-colors duration-150"
          (click)="submitForm()">
          {{ 'Submit' | translate }}
        </button>
      </form>
      <div class="text-sm text-slate-500 mt-4">
        {{ 'didNotReceiveCode' | translate }}
        <span *ngIf="countdown > 0">
          <span class="font-medium text-indigo-500 hover:text-indigo-600">{{ countdown }}</span>
        </span>
        <span *ngIf="countdown <= 0">
          <button (click)="sendOtp()" class="font-medium text-indigo-500 hover:text-indigo-600">{{ 'resend' | translate }}</button>
        </span>
      </div>
    </div>
  </div>
</div>
