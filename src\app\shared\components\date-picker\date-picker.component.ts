import {Component, Input, OnInit, ViewContainerRef} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {NgSelectModule} from "@ng-select/ng-select";
import {ReactiveFormsModule} from "@angular/forms";
import {DatePipe} from "@angular/common";
import {distinctUntilChanged} from "rxjs";
import {InputHelperService} from "../../../core/services/input-helper.service";
import { FlatpickrModule } from './flatpickr.module';

@Component({
  selector: 'app-date-picker',
  standalone: true,
  imports: [
    FlatpickrModule,
    TranslateModule,
    NgSelectModule,
    ReactiveFormsModule,
  ],
  templateUrl: './date-picker.component.html',
  styleUrl: './date-picker.component.scss'
})
export class DatePickerComponent implements OnInit {
  @Input() label?: string;
  @Input() placeholder!: string;
  @Input() readonly?: boolean;
  required: boolean = true;
  @Input() control: any;
  name: string = 'text';

  constructor(
    public datePipe: DatePipe,
    protected inputHelperService: InputHelperService,
    private viewContainerRef: ViewContainerRef
  ) {
  }

  ngOnInit() {
    this.required = this.inputHelperService.isRequired(this.control);
    this.name = this.inputHelperService.getFormControlName(this.control) ?? this.label ?? this.placeholder ?? this.name;
    this.control?.valueChanges?.pipe(
      distinctUntilChanged() // This ensures the value has actually changed before processing.
    ).subscribe((result?: string) => {
      // this.control?.setValue(this.datePipe.transform(result ?? Date.now(), 'yyyy-MM-dd'));
    })
  }
}
