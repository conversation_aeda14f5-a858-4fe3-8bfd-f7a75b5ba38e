import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {SettingsComponent} from "./settings/settings.component";
import { QalificationRequestComponent } from './components/qalification-request/qalification-request.component';
import { AccountSettingComponent } from './components/account-setting/account-setting.component';


const routes: Routes = [
  {
    path: '',
    component: AccountSettingComponent,
  },
  {path:"qualification-Request",component:QalificationRequestComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule { }
