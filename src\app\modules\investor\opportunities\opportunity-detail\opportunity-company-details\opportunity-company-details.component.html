
<!-- first-two-------------->
<div class="grid grid-flow-row-dense grid-cols dark:bg-inherit dark:text-inherit">
  <p class="text-base  font-bold border-b-2 bb-dashed mt-8 mb-2 py-3">
    {{"companyInfo" | translate }}
  </p>

  <div class="grid grid-cols-1 gap-8 mt-3">


    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-semibold"> {{"commercialRegistration" | translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{OpportunityData?.crNumber}}</span>
    </div>
    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-semibold">{{"incomeCOmpany" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold"> {{ OpportunityData?.investableAmount}} <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg" class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /> </span>
    </div>



    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-semibold">{{"Hyjrydate" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{OpportunityData?.issueDateHijri }}</span>
    </div>
  </div>
</div>
<!-- second-two-------------->
 <div class="grid grid-flow-row-dense grid-cols">
  <p class="text-base  font-bold border-b-2 bb-dashed mt-4 mb-2 py-3">
    {{"companyActivity" | translate }}
  </p>
 <div class="grid grid-cols-3 gap-8 mt-3">
 @for( activity of OpportunityData?.activities; track activity) {
   
      <div class="flex justify-between items-center text-center bg-[#015C8E0D] rounded-lg p-4">
        <span class="text-black dark:text-inherit font-semibold m-auto">{{ currentLang === 'ar' ? (activity?.name):(activity?.nameEn)}}</span>
      </div>
      
 }
 </div>
</div>
<!------- section-three------------>
<div class="grid grid-flow-row-dense grid-cols">
  <p class="text-base  font-bold border-b-2 bb-dashed mt-8 mb-2 py-3">
    {{"companyhasinvoices" | translate }}
  </p>

  <div class="grid grid-cols-1 gap-8 mt-3">


    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-semibold">{{"totalinvoices" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold"> {{OpportunityData?.amount}}<img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /> </span>
    </div>
    
    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-semibold"> {{"Privatecompany" | translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{OpportunityData?.companyName}}</span>
    </div>

  </div>
</div>