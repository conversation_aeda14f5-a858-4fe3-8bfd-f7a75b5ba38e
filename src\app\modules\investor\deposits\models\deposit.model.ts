// import {PaymentStatus, PaymentType} from "../../../shared/enums/payment.enum";
// import {IQuery} from "../../../core/models/pagination.model";

import { IQuery } from "../../../../core/models/pagination.model";
import { PaymentStatus, PaymentType } from "../../../../shared/enums/payment.enum";

export interface PaymentRequestModel {
  paymentRequestID?: string;
  walletID?: string;
  walletName?: string;
  paymentDate?: Date;
  userID?: string;
  userBankAccountID?: string;
  fullName?: string;
  amount?: number;
  withdrawStatus:number
  status?: PaymentStatus;
  readonly statusName?: string;
  requestType?: PaymentType;
  readonly requestTypeName?: string;
  paymentType?: PaymentType;
  readonly paymentTypeName?: string;
  transactionNo?: string;
  transactionDate: Date;
  bankID: string;
  bankName?: string;
  approvdOn?: Date;
  approvdBy?: string;
  approvdByName?: string;
  accountDetails?: string;
  notes?: string;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  lastUpdateBy?: string;
  lastUpdateByName?: string;
  lastUpdateOn?: Date;
  actionName?: string;
  mfaCode: string;
}
export interface DepositRequestModel {
  paymentRequestID?: string;
  walletID?: string;
  walletName?: string;
  paymentDate?: Date;
  userID?: string;
  find: string,
  userAccountId: string,
  userId?: string;
  userBankAccountID?: string;
  fullName?: string;
  amount?: number;
  status?: PaymentStatus;
  readonly statusName?: string;
  requestType?: PaymentType;
  readonly requestTypeName?: string;
  paymentType?: PaymentType;
  readonly paymentTypeName?: string;
  transactionNo?: string;
  transactionDate: Date;
  bankID: string;
  bankName?: string;
  approvdOn?: Date;
  approvdBy?: string;
  approvdByName?: string;
  accountDetails?: string;
  notes?: string;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  lastUpdateBy?: string;
  lastUpdateByName?: string;
  lastUpdateOn?: Date;
  actionName?: string;
  mfaCode: string;
}

export interface PaymentRequestSearch  extends IQuery  {
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  userAccountId?: string;
  sort?: number;
  itemsCount?: number;
  limit?: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
  requestType?: PaymentType;
  withdrawStatus?: PaymentStatus;
  excludeStatus?: PaymentStatus;
  paymentType?: PaymentType;
  transactionDateFrom?: Date;
  transactionDateTo?: Date;
}
export interface DepositRequestSearch  extends IQuery  {
  
  find: string,
  userAccountId: string,
  userId?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: number;
  itemsCount?: number;
  limit?: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
  requestType?: PaymentType;
  paymentStatus?: PaymentStatus;
  excludeStatus?: PaymentStatus;
  paymentType?: PaymentType;
  transactionDateFrom?: Date;
  transactionDateTo?: Date;
}

export interface PaymentRequestCommand {
  paymentRequestID: string;
}

export interface WithdrawStatusesModel {
  waitingCount: number;
  approvedCount: number;
  paidCount: number;
  waitingTotal: number;
  approvedTotal: number;
  paidTotal: number;
}

export interface DepositStatusesModel {
  waitingCount: number;
  approvedCount: number;
  waitingTotal: number;
  approvedTotal: number;
}

export interface WithdrawExcelModel {
  investorName: string;
  transactionNo?: string;
  transactionDate?: string;
  accountDetails?: string;
  amount: number;
  bankName: string;
  walletName: string;
  statusName: string;
}

export interface DepositExcelModel {
  investorName: string;
  transactionNo: string;
  transactionDate: string;
  amount: number;
  bankName: string;
  walletName: string;
  statusName: string;
}

export interface ApprovePaymentRequestCommand {
  paymentRequestID: string;
  notes?: string;
  mfaCode: string;
}

export interface PayWithdrawCommand {
  paymentRequestID: string;
  transactionNo?: string;
  transactionDate?: Date;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  mfaCode: string;
}

export interface WithdrawCreateCommand {
  walletID: string;
  userBankAccountID: string;
  amount: number;
  notes?: string;
  mfaCode: string;
}

export interface DepositCreateCommand {
  amount: number;
  transactionNo?: string;
  transactionDate?: Date;
  bankID: string;
  notes?: string;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  mfaCode: string;
}

export interface ApprovedWithdrawCreateCommand {
  walletID: string;
  userID: string;
  amount: number;
  notes?: string;
  mfaCode: string;
}

export interface PaidWithdrawCreateCommand {
  walletID: string;
  userID: string;
  amount: number;
  bankID: string;
  transactionNo?: string;
  transactionDate?: Date;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  notes?: string;
  mfaCode: string;
}

export interface RejectPaymentRequestCommand {
  paymentRequestID: string;
  notes?: string;
}
