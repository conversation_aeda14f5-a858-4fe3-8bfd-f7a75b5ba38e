.bh-datatable {
  @apply text-black dark:text-white-dark #{!important};
}

.bh-datatable .bh-table-responsive {
  @apply rounded-none #{!important};
}

.bh-datatable .bh-table-responsive table thead tr th {
  @apply font-bold #{!important};
}

.bh-datatable .bh-pagination {
  @apply font-medium #{!important};
}

.datatable .bh-datatable .bh-pagination .bh-page-item {
  @apply h-9 w-9 border-white-light bg-white-light text-dark hover:bg-primary hover:text-white dark:border-[#191e3a] dark:bg-[#191e3a] dark:text-white-light dark:hover:bg-primary dark:hover:text-white #{!important};
}

.datatable .bh-datatable .bh-pagination .bh-page-item.bh-active {
  @apply bg-primary text-white #{!important};
}

.bh-datatable .bh-pagination .bh-page-item.bh-active {
  @apply dark:bg-primary dark:text-white #{!important};
}

.bh-datatable .bh-pagination select {
  @apply rounded-md border border-[#e0e6ed] bg-white py-1.5 pl-2 pr-4 text-sm font-semibold text-black focus:border-primary focus:ring-transparent dark:border-[#17263c] dark:bg-[#121e32] dark:text-white-dark dark:focus:border-primary #{!important};
}

.bh-datatable .bh-pagination .bh-pagination-number {
  @apply rtl:ml-0 rtl:space-x-reverse rtl:sm:mr-auto #{!important};
}

.bh-datatable .bh-pagination .bh-pagination-info > span {
  @apply rtl:mr-0 rtl:ml-2 #{!important};
}

.datatable.invoice-table .bh-datatable .bh-pagination {
  @apply px-5 #{!important};
}

.bh-datatable .bh-filter div button {
  @apply block #{!important};
}

.bh-datatable .bh-sort svg polygon {
  @apply dark:text-dark #{!important};
}

.bh-datatable .bh-filter .bh-form-control {
  @apply dark:border-[#17263c] dark:bg-[#121e32] dark:text-white-dark dark:focus:ring-transparent #{!important};
}

.bh-datatable .bh-filter > button {
  @apply dark:border-dark dark:bg-dark dark:text-white-dark dark:hover:text-white-light #{!important};
}

.bh-datatable .bh-filter-menu button {
  @apply dark:bg-[#1b2e4b] dark:hover:bg-[#181f32] dark:hover:text-white-dark #{!important};
}

.bh-datatable .bh-filter-menu button.active {
  @apply dark:bg-[#181f32] #{!important};
}

.bh-datatable .bh-table-responsive input[type='checkbox'] + div {
  @apply rounded border-2 border-[#e0e6ed] bg-transparent text-primary dark:border-[#253b5c] #{!important};
}

.bh-datatable .bh-table-responsive input[type='checkbox']:checked + div,
.bh-datatable .bh-table-responsive input[type='checkbox']:indeterminate + div {
  @apply border-primary bg-primary #{!important};
}

.bh-datatable .bh-table-responsive table.bh-table-bordered thead tr th,
.bh-datatable .bh-table-responsive table.bh-table-bordered tbody tr td {
  @apply dark:border-[#191e3a] #{!important};
}

.bh-datatable .bh-table-responsive table th.bh-sticky,
.bh-datatable .bh-table-responsive table td.bh-sticky {
  @apply bg-[#f6f8fa]  dark:bg-bg3 #{!important};
}

.bh-datatable .bh-filter-menu {
  @apply min-w-max #{!important};
}

.next-prev-pagination .bh-datatable .bh-pagination > div {
  @apply flex-col justify-center #{!important};
}

.next-prev-pagination .bh-datatable .bh-pagination .bh-pagination-number {
  @apply ltr:ml-0 rtl:mr-0 #{!important};
}

.next-prev-pagination .bh-datatable .bh-pagination .bh-page-item {
  @apply w-max rounded-md border-primary bg-transparent px-5 py-2 text-primary dark:border-primary dark:bg-transparent dark:text-primary #{!important};
}

.bh-datatable .bh-table-responsive table.bh-table-hover tbody tr {
  @apply hover:bg-white-light/40 dark:hover:bg-[#1a2941]/40  bg-white-light/20 dark:bg-bg3 #{!important};
}

.bh-datatable .bh-table-responsive table.bh-table-striped tbody tr:nth-child(odd) {
  @apply bg-white dark:bg-bg3/40 hover:bg-white-light/40 #{!important};
}

.ang-json2excel-btn {
  @apply relative z-10 h-[34px] w-[87px] px-2.5 py-1.5 opacity-0 #{!important};
}

.bh-datatable .bh-table-responsive table tbody tr td,
.bh-datatable .bh-table-responsive table tfoot tr th,
.bh-datatable .bh-table-responsive table thead tr th {
  @apply text-start dark:text-white-dark #{!important};
}

.bh-datatable .bh-skeleton-box {
  @apply bg-white dark:bg-bg3 h-10 my-1 #{!important};
}
.datatable .bh-datatable .bh-table-responsive {
  @apply lg:overflow-visible #{!important}
}
/* tables */
.table-responsive {
  @apply overflow-auto;
}

table {
  @apply w-full border-collapse #{!important};
}

table thead tr,
table tfoot tr {
  @apply border-b-0 bg-[#f6f8fa]  dark:bg-bg3 #{!important};
}

table thead tr th,
table tfoot tr th,
table tbody tr td {
  @apply py-3 px-4 ltr:text-left rtl:text-right;
}

table thead tr th,
table tfoot tr th {
  @apply font-semibold;
}

table tbody tr {
  @apply border-b border-white-light/40 dark:border-[#191e3a] #{!important};
}

table.table-hover tbody tr {
  @apply hover:bg-white-light/20 dark:hover:bg-[#1a2941]/40 #{!important};
}

table.table-striped tbody tr:nth-child(even) {
  @apply bg-white-light/20 dark:bg-bg3/40 #{!important};
}

table.dataTable-table tbody tr th,
table.dataTable-table tbody tr td {
  @apply border-b border-[#e5e7eb] py-3 px-4 ltr:text-left rtl:text-right dark:border-[#191e3a];
}

table.dataTable-table tbody tr:last-child td {
  @apply border-b-0;
}
