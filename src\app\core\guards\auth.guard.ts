// Import statements
import {inject, Injectable} from '@angular/core';
import {CanActivateFn, Router} from '@angular/router';
import {AuthService} from "../../modules/auth/services/auth.service";
import { EncryptStorageService } from '../../modules/auth/services/encrypt-storage.service';
import { EncryptStorage } from 'encrypt-storage';

// Services injection
@Injectable({providedIn: 'root'})
export class AuthGuardService {
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(private router: Router, private authService: AuthService,private encryptStorageService: EncryptStorageService) {
  }


  checkAuthentication(): boolean {
    const isAuthenticated = !!this.authService.currentUserValue;
    const isTokenValid = !this.authService.isTokenExpired;
    
    // console.log('Auth Status:', {
    //     isAuthenticated,
    //     isTokenValid,
    //     currentTime: new Date(),
    //     expiryTime: this.#encryptTokenStorage.getItem('TokenExpiryDate')
    // });

    if (!isAuthenticated && !isTokenValid) {
        // Optional: Clear stored auth data if invalid
        this.#encryptTokenStorage.removeItem('TokenExpiryDate');
        // Other cleanup as needed
        // window.location.reload();
        this.router.navigate(['/auth/']);
        return false;
    }
    return true;
}

//   checkAuthentication = (): boolean => {
//     const isAuthenticated = !!this.authService.currentUserValue;
//     const isTokenValid = !this.encryptStorageService.isTokenExpired;
    
//     console.log('Auth Status:', {
//         isAuthenticated,
//         isTokenValid,
//         currentTime: new Date(),
//         expiryTime: localStorage.getItem('TokenExpiryDate')
//     });

//     if (!isAuthenticated || !isTokenValid) {
//         this.router.navigate(['/auth']);
//         return false;
//     }
//     return true;
// };

//   checkAuthentication = (): boolean => {
//     const isAuthenticated = !!this.authService.currentUserValue;
//     const isTokenValid = !this.encryptStorageService.isTokenExpired;
// console.log(isTokenValid,'isTokenValid');

//     if (!isAuthenticated || !isTokenValid) {
      
//       this.router.navigate(['/auth']);
  
//       return false  && window.location.reload();
//     }
   
//     return true;
//   };
}
  
  // checkAuthentication = (): boolean => {
  //   if (!this.authService.currentUserValue && this.authService.tokenUser.length==0 ) {
  //     // User is not authenticated, redirect to login
  //     this.router.navigate(['/auth']);
  //     return false;
  //   }
  //   return true;
  // }
// }

// CanActivateFn definition
export const authenticatedGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthGuardService); // Using Angular's inject function to get the AuthGuardService instance
  return authService.checkAuthentication();
};

@Injectable({providedIn: 'root'})
export class RedirectIfAuthenticatedService {
  constructor(private router: Router, private authService: AuthService,
    private encryptStorageService: EncryptStorageService
  ) {
  }

  redirectToHomeIfAuthenticated = (): boolean => {
    if (this.authService.currentUserValue ) {
      this.router.navigate(['/']); // Redirect authenticated users to home
      return false;
    }
    return true;
  }
}

// CanActivateFn for redirect
export const redirectIfAuthenticatedGuard: CanActivateFn = () => {
  const authService = inject(RedirectIfAuthenticatedService);
  return authService.redirectToHomeIfAuthenticated();
};
