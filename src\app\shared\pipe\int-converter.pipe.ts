import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
  name: 'intConverter',
  standalone: true
})
export class IntConverterPipe implements PipeTransform {
  transform(value: number | null | undefined, method: 'ceil' | 'round' | 'floor' = 'floor'): number {
    value = value || 0;
    return {
      'ceil': Math.ceil(value),
      'round': Math.round(value),
      'floor': Math.floor(value),
    }[method];
  }
}
