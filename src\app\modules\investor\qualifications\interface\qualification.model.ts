import {
  QualificationReason,
  QualificationStatus,
} from '../../../shared/enums/qualification.enum';

export interface QualificationModel {
  qualificationID?: string;
  userID?: string;
  userName?: string;
  qualificationReason?: QualificationReason | undefined;
  status?: QualificationStatus;
  createdOn?: Date;
  attachments?: string[];
}

export interface QualificationSearch {
  find?: string;
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: string;
  page?: number;
  itemsCount?: number;
  limit: number;
  includeStatus?: QualificationStatus;
  excludeStatus?: QualificationStatus;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
}

export interface ChangeQualificationStatusCommand {
  status?: QualificationStatus;
  description?: string;
  otp?: string;
}

export interface CreateQualificationCommand {
  qualificationReason: QualificationReason;
  otp: string;
  files: string[];
}

export interface UpdateQualificationCommand {
  qualificationID: string;
  qualificationReason: QualificationReason;
  otp: string;
  attachments: string[];
}
