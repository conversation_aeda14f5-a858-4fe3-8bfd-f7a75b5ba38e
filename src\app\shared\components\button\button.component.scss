
.base {
  @apply btn hover:text-white focus:text-white focus:ring active:text-white active:ring dark:hover:text-white dark:active:text-white dark:focus:text-white
}

.green {
  @apply text-green-500 bg-green-100   hover:bg-green-600  focus:bg-green-600  focus:ring-green-100  active:bg-green-600  active:ring-green-100 dark:bg-green-500/20 dark:text-green-400 dark:hover:bg-green-500  dark:focus:bg-green-500  dark:active:bg-green-500  dark:ring-green-400/20;
}

.primary {
  @apply flex text-white btn hover:text-white focus:text-white focus:ring active:text-white active:ring bg-primary-500 border-primary-500 hover:bg-primary-600 hover:border-primary-600 focus:bg-primary-600 focus:border-primary-600 focus:ring-primary-100 active:bg-primary-600 active:border-primary-600 active:ring-primary-100 dark:ring-primary-400/20;
}

//.secondary {
//  @apply flex text-white btn hover:text-white focus:text-white focus:ring active:text-white active:ring bg-secondary-500 border-secondary-500 hover:bg-secondary-600 hover:border-secondary-600 focus:bg-secondary-600 focus:border-secondary-600 focus:ring-secondary-100 active:bg-secondary-600 active:border-secondary-600 active:ring-secondary-100 dark:ring-secondary-400/20;
//}

.sky {
  @apply text-sky-500  bg-sky-100  hover:bg-sky-600  focus:bg-sky-600  focus:ring-sky-100  active:bg-sky-600  active:ring-sky-100 dark:bg-sky-500/20 dark:text-sky-400 dark:hover:bg-sky-500 dark:focus:bg-sky-500  dark:active:bg-sky-500  dark:ring-sky-400/20;
}

.gray {
  @apply text-gray-500  bg-gray-100  hover:bg-gray-600  focus:bg-gray-600  focus:ring-gray-100  active:bg-gray-600  active:ring-gray-100 dark:bg-gray-500/20 dark:text-gray-400 dark:hover:bg-gray-500 dark:focus:bg-gray-500  dark:active:bg-gray-500  dark:ring-gray-400/20;
}

.btn[disabled] {
  @apply opacity-60 cursor-not-allowed;
}
