import { Injectable } from '@angular/core';


import {PaymentRequestSearch} from "../../deposits/models/deposit.model";
import { IQuery, PAGINATION } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';
import { BaseResponse } from '../../../../core/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class LoanRequestService {
 pagination = PAGINATION;

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);
  private path = '/client';

  constructor(
    private apiService: ApiService,
  ) {
  }

  


  async getList(query?: IQuery) {
    query = {...query, limit: this.pagination.pageSize} as IQuery;
    const response:any = await this.apiService.post<BaseResponse<PaymentRequestSearch>>(`${this.path}/loan-requests/search`, {});
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response.data;
  }


  async create(body: any) {
    const path = '/investor/PaymentRequests/Withdraw';
    return await this.apiService.post(`${path}`, body);
  }

  async createrequest(body:any){

    return await this.apiService.post(`${this.path}/loan-requests`, body);
  }
  async getPaymentRequest(id: string) {
    const path = `/client/loan-requests/${id}/study-fee`;
    return await this.apiService.get(`${path}`);
  }
  async updatePaymentRequest(id: string, body: any) {
    const path = `/client/loan-requests/${id}/pay-study-fee`;
    return await this.apiService.put(`${path}`, body);
  }

  async getLoanRequest(id: string) {

    return await this.apiService.get(`${this.path}/loan-requests/${id}`);
  }
async getLoanRequestScore(id: string) {

    return await this.apiService.get(`${this.path}/loan-requests/${id}/score`);
  }
async getLoanRequstComment(id: string) {

    return await this.apiService.get(`${this.path}/loan-requests/${id}/comments`);
  }
async getLoanRequstAttachments(id: string) {

    return await this.apiService.get(`${this.path}/loan-requests/${id}/attachments`);
   }
  //   async sendAttachmentRequest(requestBody: any) {
      
  //   return await this.apiService.put(`${this.path}/attachment-file`, requestBody)
  // }

  async sendAttachmentRequest(formData: FormData) {
  return await this.apiService.put(`${this.path}/loan-requests/attachment-file`, formData);
}
  async submitLoanRequest(formData: FormData) {
  return await this.apiService.put(`${this.path}/loan-requests/change-status`, formData);
}
    async SendLoanRequstComment(requestBody: any) {
    return await this.apiService.post(`${this.path}/loan-requests/comment`, requestBody)
  }



  async updateLoanRequest(body: any) {
    const path = `/client/loan-requests`;
    return await this.apiService.put(`${path}`, body);
  }
}
