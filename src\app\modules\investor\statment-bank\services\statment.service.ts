import { Injectable, signal } from '@angular/core';

import { ApiService } from '../../../../core/services/api.service';
import { IQuery, PAGINATION } from '../../../../core/models/pagination.model';
import { BaseResponse } from '../../../../core/models/api-response.model';
import { EncryptStorage } from 'encrypt-storage';

 interface StatementQuery extends Partial<IQuery> {
  startDate?: string | null;
  endDate?: string | null;
}@Injectable({
  providedIn: 'root'
})

export class StatmentService {
  private path = '/client/statement';
  statementsPagination: IQuery = { 
    ...PAGINATION,
    isServerMode: true,
    find: '',
    totalRows: 0,
    loading: false
  };

  #encryptTokenStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken');
  userMainAccount = signal<string>("0");

  constructor(private apiService: ApiService) {}



async getList(query?: StatementQuery): Promise<{ data: any; totalRecords: number }> {
  const currentUser = this.#encryptTokenStorage.getItem('currentUser');
  const userId = typeof currentUser === 'object'
    ? currentUser?.userId || currentUser?.userAccountId
    : currentUser;

  const params = {
    find: query?.find || '',
    userAccountId: userId,
    startDate: query?.startDate || null,
    endDate: query?.endDate || null,
    pageNumber: query?.page ?? 0, // 0-based
    pageSize: query?.pageSize ?? 10
  };

  this.statementsPagination.loading = true;

  try {
    const response = await this.apiService.post<BaseResponse<any>>(`${this.path}`, params);

    return {
      data: response.data,
      totalRecords: response.totalRecords || response.data?.totalRecords || 0
    };
  } finally {
    this.statementsPagination.loading = false;
  }
}

  initStatementsPagination = () => {
    this.statementsPagination = { 
      ...PAGINATION,
      isServerMode: true,
      find: '',
      totalRows: 0,
      loading: false
    };
  }
}