import {Component, Input} from '@angular/core';
import {LucideAngularModule} from "lucide-angular";
import {TranslateModule} from "@ngx-translate/core";
import {CountUpModule} from "ngx-countup";
import {NgClass} from "@angular/common";

@Component({
  selector: 'app-statistic-card',
  standalone: true,
  imports: [
    LucideAngularModule,
    TranslateModule,
    CountUpModule,
    NgClass
  ],
  templateUrl: './statistic-card.component.html',
  styleUrl: './statistic-card.component.scss'
})
export class StatisticCardComponent {
  @Input() title: string = '';
  @Input() count: number = 0;
  @Input() icon: string = '';
  @Input() color: string = '';
}
