import {Component, signal} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {Router} from "@angular/router";
import {InputComponent} from "@component/form/input/input.component";
import {TranslateModule} from "@ngx-translate/core";
import {phoneNumberValidator} from "../../../shared/validators/phone.validator";
import {passwordValidator} from "../../../shared/validators/password.validator";
import {ButtonComponent} from "@component/form/button/button.component";

import {ModalService} from "@component/form/modals/modal.service";
import {AuthService} from "../services/auth.service";
import {DatePickerComponent} from "@component/form/date-picker/date-picker.component";
import {DatePipe} from "@angular/common";
import {nationalIDValidator} from "../../../shared/validators/national-id.validator";
import {SelectComponent} from "@component/form/select/select.component";
import {NafathModalComponent} from "@component/nafath-modal/nafath-modal.component";
import {ISendNafathResponse} from "../models/login.model";
import {IUser} from "../models/user.model";
import {ICommercialRegistrations} from "../models/commercial-registrations.model";
import {ToastService} from "../../../core/services/toast.service";
import { EncryptStorage } from 'encrypt-storage';
import { OtpModalComponent } from '../../../shared/otp/otp-modal/otp-modal.component';

enum RegisterTabs {
  mobile = 0,
  nationalId = 1,
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    InputComponent,
    TranslateModule,
    ButtonComponent,
    OtpModalComponent,

    NafathModalComponent
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent {
  mobileForm!: FormGroup
  nationalIdForm!: FormGroup
  commercialRegistrationForm!: FormGroup
  isPasswordHidden = true;
  userType=signal<number>(0)
  selectedRegisterTab = RegisterTabs.mobile
  response: ISendNafathResponse = {} as ISendNafathResponse;
  commercialRegistrations: ICommercialRegistrations[] = [] as ICommercialRegistrations[];
  protected readonly RegisterTabs = RegisterTabs;
  encryptStorage = new EncryptStorage('User_info_register');

  constructor(
    private fb: FormBuilder, private router: Router,
    protected modalService: ModalService,
    private authService: AuthService,
    private toast: ToastService
  ) {
  }

  ngOnInit() {
    this.userType.set(history.state.userSelect['selected'])
    this.encryptStorage.setItem('User_info_register', history.state.userSelect['selected'])
    this.mobileForm = this.fb.group({
      mobile: [null, [Validators.required, phoneNumberValidator()]],
      password: [null, [Validators.required]]
    })
    this.nationalIdForm = this.fb.group({
      nationalId: [null, [Validators.required, nationalIDValidator()]],
    })
    this.commercialRegistrationForm = this.fb.group({
      cr: [null, [Validators.required]],
    })
  }

  async registerMobile() {
    if (this.mobileForm.valid) {
      this.encryptStorage.setItem('mobile', this.mobileForm.value)
      await this.authService.registerMobile2({
        mobile: this.mobileForm.value.mobile
      })
      this.openOtpModal()
    } else {
      this.mobileForm.markAllAsTouched()
    }
  }

  async verifyOtp(otp: string) {
    try {
      const body = {
        mobile: this.mobileForm.value.mobile,
        code: otp,
      }
      this.response = {token: await this.authService.validateMobile2(body)}
      localStorage.setItem('token', (this.response.token  as any)['data'])
      // this.selectedRegisterTab = RegisterTabs.nationalId
      this.modalService.close('otp-modal')
      this.router.navigate(['/auth/nafath'])
    } catch (error: any) {
      this.modalService.close('otp-modal')
    }
  }

  async sendNafathRequst() {
    try {
      if (this.response?.token) {
        const body = {
          nationalId: +this.nationalIdForm.value.nationalId,
          password: this.mobileForm.value.password,
          token: this.response?.token
        }
        this.response = await this.authService.sendNafathReqeust2(body) ?? {} as ISendNafathResponse;
        this.openNafathModal();
      } else {
        this.selectedRegisterTab = RegisterTabs.mobile
      }
    } catch (e: any) {
    }
  }

  async onNafathSuccess(user: IUser) {
    localStorage.setItem('token', user.token);
    this.modalService.close('nafath-modal')
    const userSelectType = JSON.parse(this.encryptStorage.getItem('selected_type')??'1')
    if(userSelectType === 1){
      this.router.navigate(['/']);

    }else if(userSelectType === 2){
      this.router.navigate(['/auth/register_company'])
    }
  }

  togglePasswordVisibility = () => this.isPasswordHidden = !this.isPasswordHidden;
  openOtpModal = () => this.modalService.open('otp-modal')
  openNafathModal = () => this.modalService.open('nafath-modal')
}
