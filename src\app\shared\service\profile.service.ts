import { Injectable } from '@angular/core';
import { ApiService } from '../../core/services/api.service';
import { IProfileIndividual } from '../modals/Profile-individual';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {

   constructor(
    private apiService: ApiService,
  ) {
  }


   async postProfileIndividual(body:IProfileIndividual) {
      const path = '/client/profile/indvidual-kyc';
      // return (await this.apiService.post(path, body)).list?.[0];
      return (await this.apiService.post(`${path}`, body));
    }

}
