<div class="relative shrink-0">
  <div (click)="toggleOpen()" id="profile-btn" class="w-10 cursor-pointer md:w-12">
    <img src="assets/images/user.png" class="rounded-full" width="48" height="48" alt="profile img" />
  </div>
  <div id="profile" [ngClass]="isOpen?'show':'hide'" class="absolute top-full z-20 rounded-md bg-n0 shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)] duration-300 dark:bg-bg4 ltr:right-0 ltr:origin-top-right rtl:left-0 rtl:origin-top-left">
    <div class="flex  items-center border-b p-3 text-center dark:border-n500 lg:p-4">
      <img src="assets/images/user.png" width="40" height="40" class="rounded-full" alt="profile img" />

      <span class="text-sm">{{ "welcome"| translate }} {{profileData?.name}}</span>
    </div>
    <ul class="flex w-[250px] flex-col p-4">
      <li>
        <a routerLink="/settings" class="flex items-center gap-2 rounded-md p-2 duration-300 hover:bg-primary hover:text-n0">
          <span>
            <i class="las la-user mt-1 text-xl"></i>
          </span>
          {{ 'profile' | translate }}
        </a>
      </li>


      <li>
        <a routerLink="/settings/qualification-Request" class="flex items-center gap-2 rounded-md p-2 duration-300 hover:bg-primary hover:text-n0">
          <span>
            <img src="../../../../../assets/images/Personal-Growth.svg">
            <!-- <i class="las la-PersonalGrowth mt-1 text-xl"></i> -->
          </span>
          {{ 'Personal-Growth' | translate }}
        </a>
      </li>
      <li>
        <a  (click)="logout()" class="flex items-center gap-2 rounded-md p-2 duration-300 hover:bg-primary hover:text-n0">
          <span>
            <i class="las la-sign-out-alt mt-1 text-xl"></i>
          </span>
          {{ 'logout' | translate }}
        </a>
      </li>
    </ul>
  </div>
</div>
