import { Injectable } from '@angular/core';
import { BaseResponse } from '../../core/models/api-response.model';
import { PAGINATION, IQuery } from '../../core/models/pagination.model';
import { ApiService } from '../../core/services/api.service';
import { HttpClient, HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class acceptLegalDocument {



  pagination = PAGINATION;
  private path = '/client/legal-documents';
  constructor(
    private apiService: ApiService,
  ) {
  }

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);

  async getLegalDocuments() {
    const response = await this.apiService.get<BaseResponse<any>>(`${this.path}/un-signed`)
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response;
  }

  // async acceptLegalDocuments(requestBody: any, legalDocumentId: string) {
  //   const response = await this.apiService.post(`${this.path}/accept/${legalDocumentId}`,
  //     requestBody
  //   )
  // }

  async acceptLegalDocuments(requestBody: any) {
    return await this.apiService.post<BaseResponse<any>>(`${this.path}/accept`,
      requestBody
    )
  }



}
