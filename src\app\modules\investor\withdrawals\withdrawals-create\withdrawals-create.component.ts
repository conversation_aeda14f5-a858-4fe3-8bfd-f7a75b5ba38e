import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { InputComponent } from "@component/form/input/input.component";
import { TranslateModule, TranslateService } from "@ngx-translate/core";

import { ModalService } from "@component/form/modals/modal.service";
import { WithdrawalsService } from "../services/withdrawals.service";
import { Router, RouterLink } from "@angular/router";
import { IBankAccount } from '../../../auth/models/bank-account.model';
import { OtpModalComponent } from '../../../../shared/otp/otp-modal/otp-modal.component';
import { ToastService } from '../../../../core/services/toast.service';
import { OperationType } from '../../../../core/enums/operation-type.enum';
import { OtpService } from '../../../../shared/otp/services/otp.service';
import { BankAccountStatus } from '../enum/BankAccountStatus.enum';
import { CommonModule, NgClass } from '@angular/common';
import { AuthService } from '../../../auth/services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-withdrawals-create',
  standalone: true,
  imports: [
    FormsModule,
    InputComponent,
    TranslateModule,
    ReactiveFormsModule,
    OtpModalComponent, NgClass, CommonModule,
    RouterLink
  ],
  templateUrl: './withdrawals-create.component.html',
  styleUrl: './withdrawals-create.component.css'
})
export class WithdrawalsCreateComponent implements OnInit {
  form: UntypedFormGroup;
  bankAccount?: IBankAccount;
  acountdetails: any
  accountbank: any
  ibanaccount: any
  BalanceCount: any
  bankAccountStatus?: BankAccountStatus
  private subscriptions = new Subscription();
  constructor(
    private translate: TranslateService,
    private fb: UntypedFormBuilder,
    private toast: ToastService,
    private otpService: OtpService,
    private withdrawalsService: WithdrawalsService,
    protected modalService: ModalService,
    private router: Router,
    public authService: AuthService,
  ) {
    this.form = this.fb.group({
      amount: [1, [Validators.required, Validators.min(1)]],
      notes: "",
      bankName: { value: null, disabled: true },
      iban: [{ value: null, disabled: true }],
    });
  }


  async ngOnInit() {
    await this.getBankAccount();
    await this.getBalance();
    this.setupAmountValidation(); // Make sure to call this after data is loaded
  }

  async getBankAccount() {
    try {
      this.acountdetails = await this.withdrawalsService.getbankaccount();
      this.accountbank = this.acountdetails
      // console.log('Bank account details:', this.acountdetails.data.userAccountId);
      
      this.bankAccount = this.acountdetails?.data?.bankName;
      this.ibanaccount = this.acountdetails?.data?.iban;

      this.form.setValue({
        amount: null,
        notes: null,
        bankName: this.bankAccount || '', 
        iban: this.ibanaccount || '',    // Provide empty string as fallback
      });
    } catch (error) {
      console.error('Error getting bank account:', error);
      // Initialize form with empty values if there's an error
      this.form.setValue({
        amount: null,
        notes: null,
        bankName: '',
        iban: '',
      });
    }
  }

  async getBalance() {
    try {
      this.BalanceCount = await this.authService.getBalance();
    } catch {
      console.log('error getting balance');
      this.BalanceCount = 0; // Provide default value
    }
  }


  checkBalance(event: Event) {
    if (!this.BalanceCount) return;

    const input = event.target as HTMLInputElement;
    const amount = parseFloat(input.value) || 0;

    if (amount > this.BalanceCount) {
      this.form.get('amount')?.setErrors({ insufficientBalance: true });
    } else {
      this.clearBalanceError();
    }
  }
  setupAmountValidation() {
    const amountControl = this.form.get('amount');
    if (!amountControl) return;

    this.subscriptions.add(
      amountControl.valueChanges.subscribe((value) => {
        const amount = parseFloat(value) || 0;
        if (amount > this.BalanceCount) {
          amountControl.setErrors({ insufficientBalance: true });
        } else {
          this.clearBalanceError();
        }
      })
    );
  }
  async createApprovedWithdraw(otp: string) {
    if (this.form.valid) {
      try {
        const body = {
          userBankAccountId:this.acountdetails.data.userAccountId,
          amount: this.form.value.amount,
          notes: this.form.value.notes,
          mfaCode: otp
        }
        await this.withdrawalsService.create(body)
        this.router.navigate(['/withdrawals'],);
      } catch (error: any) {
        this.toast.error(error.arabicMessage)
      } finally {
        this.modalService.close('otp-modal');

      }
    } else {
      this.form.markAllAsTouched()
    }
  }


  getStatusName(status: number): string {
    switch (status) {
      case BankAccountStatus?.Pending:
        return this.translate.instant('Pending');
      case BankAccountStatus?.Approved:
        return this.translate.instant('Approved');
      case BankAccountStatus?.Rejected:
        return this.translate.instant('Rejected');
      default:
        return 'Unknown Status';
    }
  }

  async createWithdraw() {
    if (this.form.valid) {
      try {
        await this.otpService.send({ operationType: OperationType.Request_Withdraw })
        this.modalService.open('otp-modal')
      } catch (error: any) {
      }
    } else {
      this.form.markAllAsTouched()
    }
  }
  clearBalanceError() {
    const amountControl = this.form.get('amount');
    if (amountControl?.errors?.['insufficientBalance']) { // Bracket notation here
      amountControl.setErrors(null);
    }
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe(); // Clean up subscriptions
  }
}
