@if(!isSelectCompanyShow){
  <div
    class="box xl:p-6 dark:bg-bg4 font-cairo grid grid-cols-12 gap-2 xxxl:gap-5 h-[700px] items-center -mt-[25px] shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
    <div class="col-span-11 lg:col-span-11 ">
      <h3 class="mb-3 text-start font-extrabold text-[34px] font-cairo">
         {{ 'letsGetStartedCompanyView' | translate }}
         <p class="text-end cursor-pointer font-normal" (click)="goBackStep()">{{'back'|translate}}</p>
      </h3>
      <h5 class="text-start font-normal text-md  ">
           {{ 'choosewayTofoundCompany' | translate }}
      </h5>

        <div class="selectedcount">
           <div class="col-span-12 lg:col-span-12 my-12  flex justify-center gap-3">
                @for( image of images; track $index){
              <div  class="image-container dark:bg-inherit dark:text-white-light dark:border-[#015c8e]  gap-8 rounded-xl flex flex-col items-center mx-4"

            [ngStyle]="{
                  'background-color': selectedImageIndex === $index ? '#015C8D0D' : 'bg-inherit',
                  'border': selectedImageIndex === $index ? '2px solid #015C8E' : '2px solid #E7E7E7',

                }"
                (click)="selectImage($index)">
              <img

              [src]="image.src"
              [alt]="image.alt"
              class="px-6  mx-6 mix-blend-multiply"
              height="561"
            />
            <p class="image-text my-8 dark:text-slate-500 font-extrabold text-lg leading-6">{{ image.text }}</p>
          </div>
          }
          @if(images.length ==0){
            <div class=" h-[80px] rounded-lg bg-warning-400 text-warning-700 flex justify-center items-center">
              {{ 'noCompanyFound' | translate }}
            </div>
          }
      </div>

      <!-- <p class="text-center font-normal text-md text-primary cursor-pointer" (click)="addAnotherCompany()">
        {{'addAnotherCompany'|translate}}
      </p> -->
      <div class="col-span-12 lg:col-span-12  my-12 mx-[24rem] items-center">
          <button class=" btn-primary px-36 py-3 rounded-[32px] text-center justify-center text-white flex w-96 h-12 bg-[#015C8E]"
          (click)="nextStep()"> {{ 'next' |translate}}
              <img src="assets/images/arrownext.svg" class="w-4" />
          </button>

      </div>



    </div>

  </div>
  </div>
}@else{
  @if(!isSelectedBorrower){
    <app-registed-company (addAnotherCompanyChange)="addAnotherCompany()" (closeStatuts)="closeStatuts($event)" (goBack)="handleGoBack()"/>
  }@else {
    <app-borrower (closeStatuts)="closeStatuts2($event)" (brBackStep)="handleGoBack()" [isInner]="true"/>
  }
}




