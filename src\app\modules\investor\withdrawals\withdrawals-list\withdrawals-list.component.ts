import {Component, OnInit, signal} from '@angular/core';
import {colDef, DataTableModule} from "@bhplugin/ng-datatable";

import {DropdownComponent} from "@component/shared/dropdown/dropdown.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {PaymentRequestSearch} from "../../deposits/models/deposit.model";
import {Router} from "@angular/router";

import {DatePipe, DecimalPipe, NgClass} from "@angular/common";
import {TopBannerComponent} from "@component/shared/top-banner/top-banner.component";
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';
import { PaymentType } from '../../../../shared/enums/payment.enum';
import { WithdrawalsService } from '../services/withdrawals.service';
import { ToastService } from '../../../../core/services/toast.service';
import { SweetAlertService } from '../../../../core/services/sweet-alert.service';
import { EncryptStorage } from 'encrypt-storage';




@Component({
  selector: 'app-withdrawals-list',
  standalone: true,
  imports: [
    DataTableModule,
    DateFormatPipe,
    DropdownComponent,
    TranslateModule,
    DecimalPipe,
    NgClass,
    TopBannerComponent,
   
],
  providers: [
    DatePipe
  ],
  templateUrl: './withdrawals-list.component.html',
  styleUrl: './withdrawals-list.component.css'
})
export class WithdrawalsListComponent implements OnInit {
  columns: Array<colDef> = [];
Currentuser:any
  search: PaymentRequestSearch = {isServerMode: true} as PaymentRequestSearch;
  withdrawals=signal<any[]>([])
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(
    public translate: TranslateService,
     private toaster: ToastService,
    private sweetAlertService: SweetAlertService,
    protected withdrawalsService: WithdrawalsService,
    private router: Router,
   
  ) {
    this.withdrawalsService.initPagination();
  }

  async ngOnInit() {
    this.columns = [
      {title: this.translate.instant('requestNumber'), field: 'transactionNo',},
            {title: this.translate.instant('amount'), field: 'amount',},
      {title: this.translate.instant('orderDate'), field: 'requestDate',},
{title: this.translate.instant('RefNumber'), field: 'bankName',},
    
      
      {title: this.translate.instant('transactionDat'), field: 'transactionDate',},
        {title: this.translate.instant('status'), field: 'statusName',},
      {title: this.translate.instant('Actions'), field: 'Actions',},
   
    ]
    await this.getList();
  }



  async getList() {
    try {
      this.Currentuser=this.#encryptTokenStorage.getItem('currentUser')
      console.log(this.Currentuser.userAccountId);
      
    this.search.userAccountId = this.Currentuser.userAccountId;
      this.withdrawals.set(await this.withdrawalsService.getList(this.search) ?? [])
    } catch (e: any) {
      console.log(e);
    }
  }

  async handleSearch(e: any) {
    this.search = {find: (e.target as HTMLInputElement).value, isServerMode: false} as PaymentRequestSearch;
    this.withdrawals()
    this.withdrawalsService.initPagination();
    await this.getList();
    this.search.isServerMode = true;
  }


  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }

  openCreateWithdraw() {
    this.router.navigate(['/withdrawals/create']); // Not logged in, redirect to login page
  }
  async CancelWithdrawals(withdrawId: any): Promise<void> {
    try {
        const result = await this.sweetAlertService.confirm(
    'AreYouSure', 
    'confirmCancelwithdrawals' // optional - you can create this translation key
  );
   if (result.isConfirmed) {
      const response = await this.withdrawalsService.CancelWithdrawalsbyId(withdrawId);
      this.toaster.success('Withdraw request cancelled successfully');
      // Handle success|
   }
    } catch (err) {
      console.error('Error cancelling withdraw', err);
      // Handle error
    }
  }
}
