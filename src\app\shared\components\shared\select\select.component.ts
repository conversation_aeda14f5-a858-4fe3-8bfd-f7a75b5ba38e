import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import {NgSelectModule} from "@ng-select/ng-select";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";

import {NgClass} from "@angular/common";


import { InputHelperService } from '../../../../core/services/input-helper.service';
import { IKeyValue, Lookup } from '../../../../core/models/key-value.model';
import { MDModalModule } from '@component/form/modals';




@Component({
  selector: 'app-select',
  standalone: true,
  imports: [
    NgSelectModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    MDModalModule,
    NgClass
  ],
  templateUrl: './select.component.html',
  styleUrl: './select.component.scss'
})
export class SelectComponent implements OnInit, OnChanges {
  @Input() control: any;
  @Input() errorMessage?: any;
  @Input() placeholder!: string;
  @Input() label?: string;
  @Input() iconSrc?: string;
  @Input() bindLabel!: string;
  @Input() items: any;
  @Input() bindValue: any = 'id';
  @Input() disabled: boolean = false;
  @Input() multiple: boolean = false;
  @Input() editableSearchTerm: boolean = false;
  @Input() formControlName: string | number | null = null;
  @Input() lookup: Lookup = new Lookup();
  @Input() selectModel!: any;
  @Input() selectqcModel!: any;
  @Input() lang!: string;
  @Output() selectModelChange = new EventEmitter<string>();
  @Output() onSelect = new EventEmitter<any>();
  @Output() onClick = new EventEmitter<any>();
  required: boolean = true;
  @Output() onClear = new EventEmitter<any>();
  @Input() addTag = false;

  constructor(
    protected inputHelperService: InputHelperService,
    public translate: TranslateService,

  ) {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['items'] &&
      changes['items']['previousValue'] !== changes['items']['currentValue'] &&
      Array.isArray(this.items)
    ) {
      this.lang = this.translate.currentLang || 'ar';

      if(this.selectModel !== "year"){
        this.bindLabel = (this.lang === 'ar') ? "textAr" : "textEn";
      }else{
        
      }
      this.items.forEach(
        (bank) => (bank.textEn = bank.textEn ?? bank.textAr),
      );
    }
  }

  ngOnInit(): void {
    this.required = this.inputHelperService.isRequired(this.control);
  }

  onSearchSelect(item: any) {
    if (typeof item === "string") item = {textAr: item}
    if (this.addTag && item) item.isNewItem = (this.addTag && (!this.items.some((e: IKeyValue) => e.id === item.id)));
    this.onSelect.emit(item);
  }

  onClickSelect() {
    this.onClick.emit();
  }

  clearSearch() {
    this.onClear.emit();
  }

  // Implementing compareWith function
  compareWith(item1: any, item2: any): boolean {
    return item1 && item2 ? item1.id === item2.id : item1 === item2;
  }
}
