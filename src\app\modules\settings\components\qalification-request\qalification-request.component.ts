import { Component, OnInit } from '@angular/core';
import { OtpService } from '../../../../shared/otp/services/otp.service';
import { SettingsService } from '../../services/settings.service';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import { CommonModule, AsyncPipe } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ModalComponent } from '@component/form/modal/modal.component';
import { MDModalModule } from '@component/form/modals';
import { QualificationReason } from '../../models/user-accounts.model';
import { QualificationStatus } from '../../../../shared/constants/enums';
import { EnumSelectPipe } from "../../../../token-mask.pipe";
import { FileUploadComponent } from '@component/form/file-upload/file-upload.component';
import { OtpModalComponent } from '../../../../shared/otp/otp-modal/otp-modal.component';
import { ModalService } from '@component/form/modals/modal.service';
import { IValidateLoginOTPBody } from '../../../auth/models/login.model';
import { Router } from '@angular/router';
import { ToastService } from '../../../../core/services/toast.service';
import { OperationType } from '../../../../core/enums/operation-type.enum';
import { SelectComponent } from '@component/shared/select/select.component';


@Component({
  selector: 'app-qalification-request',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MDModalModule,
    TranslateModule,
    EnumSelectPipe,
    FileUploadComponent,
    OtpModalComponent,
   
],
  templateUrl: './qalification-request.component.html',
  styleUrl: './qalification-request.component.css'
})
export class QalificationRequestComponent implements OnInit{

  qualificationRquest:any
  oldQualificationRquest:any
  qualificationReason=QualificationReason
  selectedReason: number | null = null;
  qualificationRequestForm !:FormGroup
  qualificationReasons = QualificationReason;
  selectedReasons: QualificationReason[] = [];
  isRejected:boolean = false
  statusName:any
    constructor(
      private otpService: OtpService,
      private settingService: SettingsService,
      private translate: TranslateService,
      private router: Router,
      protected modalService: ModalService,
      private translateService: TranslateService,
      private toast: ToastService,

    ){

    }
  async ngOnInit(): Promise<void> {
    this.initalForm()
    await this.getqualificationRquest();

  }

   initalForm(){
    this.qualificationRequestForm = new FormGroup({
      Files: new FormControl(null,Validators.required),

    })
   }

  selectReason(reason: any): void {
    if (this.selectedReason === reason) {
        // If the same reason is clicked again, deselect it
        this.selectedReason = null;
    } else {
        // Otherwise, select the new reason
        this.selectedReason = reason;
    }
}

// Check if a reason is selected
isSelected(reason: any): boolean {
    return this.selectedReason === reason;
}


    public get QualificationStatus(): typeof QualificationStatus {
      return QualificationStatus;
    }

    getLabel(key: string): string {
      return this.translate.instant(key);
    }

    public get QualificationReason(): typeof QualificationReason {
      return QualificationReason;
    }
  async getqualificationRquest(){
    try{
      this.qualificationRquest= await  this.settingService.getQualificationRequest();
 
      this.statusName = this.qualificationRquest.data[0].statusName;
      
      this.isRejected = this.qualificationRquest.data.some((item: any)=> item.status === QualificationStatus.rejected)
      this.selectReason(this.qualificationRquest.data[0].qualificationReason)

    }catch{
      console.log('error')
    }

  }



  updateQualificationReason(reason: QualificationReason, isChecked: boolean): void {
    if (isChecked) {
        // Add the reason if it's checked
        if (!this.selectedReasons.includes(reason)) {
            this.selectedReasons.push(reason);
        }
    } else {
        // Remove the reason if it's unchecked
        this.selectedReasons = this.selectedReasons.filter(r => r !== reason);
    }
}

async onSubmit(){
  this.sendOtp();
}

async sendOtp() {
  try {
    const body = {
      operationType: OperationType.Qualification_Request,
    } ;

    const data=  await this.otpService.send(body);
    this.modalService.open('otp-modal')
  } catch (error: any) {
    this.toast.error(error.error ?
      this.translate.currentLang == 'ar' ? error.error['message'] : error.error['englishMessage'] : error);
  }
}

  async verifyOtp(otp: string) {
    try {

    const body = {
      operationType: OperationType.Qualification_Request,
      propertyValue: otp
    }
      const data=  await this.otpService.verify(body);
      
         this.router.navigate(['/']);
      this.modalService.close('otp-modal')
   

    } catch (error: any) {
      this.toast.error(error.error ?
        this.translate.currentLang == 'ar' ? error.error['arabicMessage'] : error.error['englishMessage'] : error);
    }
  }

  async postQualificationRequest(otp: string){
    try{
      const formData = new FormData();
      formData.append('QualificationReason', JSON.stringify(this.selectedReason));
      formData.append('OTP',otp)
      // for(let i=0;i<this.qualificationRequestForm.value.attachments.length;i++){
        formData.append('Files', this.qualificationRequestForm.value.Files);
      // }
     const res:any = await this.settingService.PostQualificationRequest(formData)
           this.toast.success(this.translateService.instant('Qualification_Request_Success'));
                 this.modalService.close('otp-modal')
       this.router.navigate(['/'])
   
    }catch{
      console.log('error')
        this.toast.error(this.translateService.instant('Qualification_Request_Failed'));
    }
  }

  createNewRequest(){
    this.isRejected = false
    this.selectReason(null)
    this.qualificationRquest.data = []
  }

}
