import {Component, Input, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, NgForOf} from "@angular/common";
import {MDModalModule} from "@component/form/modals";
import {PaginatorModule} from "primeng/paginator";
import {ReactiveFormsModule} from "@angular/forms";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {ModalService} from "@component/form/modals/modal.service";
import {ClipboardModule} from '@angular/cdk/clipboard'; // Ensure this import is correct
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {MatTooltip} from "@angular/material/tooltip"; // For snack bar notifications
import { AuthService } from '../../../auth/services/auth.service';
@Component({
  selector: 'app-deposit-create',
  standalone: true,
  imports: [
   
    MDModalModule,
   
    PaginatorModule,
    ReactiveFormsModule,
    TranslateModule,
    ClipboardModule,
    MatTooltip
  ],
  templateUrl: './deposit-create.component.html',
  styleUrl: './deposit-create.component.css'
})
export class DepositCreateComponent implements OnInit{
  @Input() id = 'deposit-create';
  CollectioncountData:any
  Collectioncount:any
  constructor(
    private modalService: ModalService,
   
     private translate: TranslateService,
         private authService:AuthService,
  ) {
 
  }
  async ngOnInit(): Promise<void> {
  await this.getCollectioncount()
}

  async getCollectioncount(){
    try{
      this.Collectioncount= await  this.authService.getCollectioncount()

      this.CollectioncountData=this.Collectioncount.data

      // const currencySymbol = this.translate.currentLang === 'ar' ? 'ر.س' : 'SR';

      // // Check if the iban starts with "SA" and format it
      // if (this.CollectioncountData.iban.startsWith("SA")) {
      //     const numberPart = this.CollectioncountData.iban.slice(2);
      //     this.CollectioncountData.iban = `${currencySymbol} ${numberPart}`;
      // }
    }catch{
      console.log('error')
    }

  }
  closeModal() {
    this.modalService.close(this.id);
  }
}
