import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import {FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {AsyncPipe, NgClass, NgForOf} from "@angular/common";
import {ModalService} from "@component/form/modals/modal.service";
import {ModalComponent} from "@component/form/modal/modal.component";
import {ButtonComponent} from "@component/form/button/button.component";
import {MDModalModule} from "@component/form/modals";
import {TranslateModule} from "@ngx-translate/core";
import {LoadingBarService} from "@ngx-loading-bar/core";
import {interval, Subscription} from "rxjs";
import { phoneNumberValidator } from '../../../../shared/validators/phone.validator';


@Component({
  selector: 'app-forget-modal',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MDModalModule,
    TranslateModule,
    AsyncPipe,
  ],
  templateUrl: './forget-modal.component.html',
  styleUrl: './forget-modal.component.css'
})
export class forgetModalComponent implements AfterViewInit {
  userForm!: FormGroup;
  @Input() id = 'forget-modal';
  @ViewChild('nextButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() submitEnteredValue = new EventEmitter<string>();
  @Input() title: string = 'forgotPassword';
  @Input() subTitle: string = 'forgotPassword_short';
  @Input() placeholder: string = 'enterVerificationCode';
  @Input() isPhone: boolean = false;

  countdown: number = 0;
  countdownSubscription: Subscription | null = null;
  readonly countdownStart = 60; // Countdown start time in seconds
  constructor(
    private fb: FormBuilder,
    protected modalService: ModalService,
    private cdr: ChangeDetectorRef,
    protected loadingBar: LoadingBarService,
  ) {
  }



  ngOnInit(): void {
    this.userForm = this.fb.group({
      userValue: this.fb.control("",this.isPhone?[Validators.required, phoneNumberValidator()]:[Validators.required, Validators.pattern("^(1|2){1}[0-9]{9}$")])
    });
  }

  ngAfterViewInit(): void {
   
  }

  handleKeyDown(event: KeyboardEvent): void {
  
  }

  handleInput(event: Event): void {
    
  }

  handleFocus(event: Event): void {

  }

  handlePaste(event: ClipboardEvent): void {
   
  }
closeModal(): void {
  this.modalService.close(this.id);
}
  verify(): void {
    if (this.userForm.valid) {
      this.submitEnteredValue.emit(this.userForm.value);
    } else {
      this.userForm.markAllAsTouched();
    }
  }


}
