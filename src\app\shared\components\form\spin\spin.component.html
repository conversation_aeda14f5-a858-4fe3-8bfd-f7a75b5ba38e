<div class="flex justify-center text-center">
  <button
    (click)="plus()"
    class="px-2 transition-all duration-200 ease-linear bg-white border dark:bg-zink-700 dark:border-zink-500 rounded-s  w-9 h-9 border-slate-200 btn-plus text-slate-500 dark:text-zink-200 hover:bg-primary-500 dark:hover:bg-primary-500 hover:text-primary-50 dark:hover:text-primary-50 hover:border-primary-500 dark:hover:border-primary-500 focus:bg-primary-500 dark:focus:bg-primary-500 focus:border-primary-500 dark:focus:border-primary-500 focus:text-primary-50 dark:focus:text-primary-50"
    type="button">
    <lucide-angular [class]="'inline-block w-4 h-4'" name="plus"></lucide-angular>
  </button>

  @if (control) {
    <input
      (change)="handleOnChange($event)"
      [formControl]="control "
      class=""
      max="100"
      min="0" type="number" value="0">
  } @else {
    <input
      (change)="handleOnChange($event)"
      [ngModel]="value"
      class=""
      max="100"
      min="0" type="number" value="0">
  }

  <button
    (click)="minus()"
    class="px-2 border w-9 h-9 leading-[15px] btn-minus bg-white dark:bg-zink-700 dark:border-zink-500 rounded-e transition-all duration-200 ease-linear border-slate-200 text-slate-500 dark:text-zink-200 hover:bg-primary-500 dark:hover:bg-primary-500 hover:text-primary-50 dark:hover:text-primary-50 hover:border-primary-500 dark:hover:border-primary-500 focus:bg-primary-500 dark:focus:bg-primary-500 focus:border-primary-500 dark:focus:border-primary-500 focus:text-primary-50 dark:focus:text-primary-50"
    type="button">
    <lucide-angular [class]="'inline-block w-4 h-4'" name="minus"></lucide-angular>
  </button>
</div>
