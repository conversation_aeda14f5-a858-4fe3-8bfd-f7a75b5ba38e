const TransactionStatus = {
  active: 'active',
  cancelled: 'cancelled',
  paused: 'paused'
}
export const exchangeData = [
  {
    id: 1,
    from: 'USD',
    to: 'EUR',
    medium: 'Paypal',
    date: '11/05/2028',
    status: TransactionStatus.active,
    time: '05:12 AM',
    money: 7000
  },
  {
    id: 2,
    from: 'EUR',
    to: 'USD',
    medium: 'Bank Transfer',
    date: '12/15/2028',
    status: TransactionStatus.paused,
    time: '02:45 PM',
    money: 10000
  },

  {
    id: 7,
    from: 'USD',
    to: 'EUR',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 8,
    from: 'USD',
    to: 'EUR',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 14,
    from: 'USD',
    to: 'EUR',
    medium: 'Venmo',
    date: '03/10/2029',
    status: TransactionStatus.active,
    time: '08:00 PM',
    money: 12000
  },
  {
    id: 15,
    from: 'EUR',
    to: 'USD',
    medium: 'Cash Deposit',
    date: '06/25/2029',
    status: TransactionStatus.paused,
    time: '01:15 PM',
    money: 8000
  },
  {
    id: 9,
    from: 'USD',
    to: 'EUR',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 10,
    from: 'USD',
    to: 'EUR',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 11,
    from: 'USD',
    to: 'EUR',
    medium: 'Paypal',
    date: '11/05/2028',
    status: TransactionStatus.active,
    time: '05:12 AM',
    money: 7000
  },
  {
    id: 12,
    from: 'EUR',
    to: 'USD',
    medium: 'Bank Transfer',
    date: '12/15/2028',
    status: TransactionStatus.paused,
    time: '02:45 PM',
    money: 10000
  },
  {
    id: 3,
    from: 'USD',
    to: 'EUR',
    medium: 'Credit Card',
    date: '08/20/2028',
    status: TransactionStatus.active,
    time: '10:30 AM',
    money: 5000
  },
  {
    id: 4,
    from: 'USD',
    to: 'EUR',
    medium: 'Venmo',
    date: '03/10/2029',
    status: TransactionStatus.active,
    time: '08:00 PM',
    money: 12000
  },
  {
    id: 5,
    from: 'EUR',
    to: 'USD',
    medium: 'Cash Deposit',
    date: '06/25/2029',
    status: TransactionStatus.paused,
    time: '01:15 PM',
    money: 8000
  },
  {
    id: 6,
    from: 'USD',
    to: 'EUR',
    medium: 'Wire Transfer',
    date: '02/18/2029',
    status: TransactionStatus.cancelled,
    time: '11:45 AM',
    money: 15000
  },
  {
    id: 13,
    from: 'USD',
    to: 'EUR',
    medium: 'Credit Card',
    date: '08/20/2028',
    status: TransactionStatus.active,
    time: '10:30 AM',
    money: 5000
  },

  {
    id: 16,
    from: 'USD',
    to: 'EUR',
    medium: 'Wire Transfer',
    date: '02/18/2029',
    status: TransactionStatus.cancelled,
    time: '11:45 AM',
    money: 15000
  },
  {
    id: 17,
    from: 'USD',
    to: 'EUR',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 18,
    from: 'USD',
    to: 'EUR',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  },
  {
    id: 19,
    from: 'USD',
    to: 'EUR',
    medium: 'Google Pay',
    date: '09/30/2028',
    status: TransactionStatus.active,
    time: '04:30 AM',
    money: 9500
  },
  {
    id: 20,
    from: 'USD',
    to: 'EUR',
    medium: 'Direct Deposit',
    date: '07/12/2029',
    status: TransactionStatus.paused,
    time: '03:20 PM',
    money: 18000
  }
]
