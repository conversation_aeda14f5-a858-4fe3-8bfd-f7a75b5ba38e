export interface BaseResponse<T> {
  list?: T[];
  totalRecords: number;
  result: ResultResponse;
}

export interface ResultResponse {
  statusCode?: string;
  status?: boolean;
  arabicMessage?: string;
  englishMessage?: string;
  technicalErrorMessage?: string;
}

export interface Search {
  find?: string;
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: string;
  page?: number;
  itemsCount: number;
  limit: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
}
