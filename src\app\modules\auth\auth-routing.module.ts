import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {LoginComponent} from "./login/login.component";
import {RegisterComponent} from "./register/register.component";
import {VerifyOtpComponent} from "./verify-otp/verify-otp.component";
import {ForgotPasswordComponent} from "./forgot-password/forgot-password.component";
import {ResetPasswordComponent} from "./reset-password/reset-password.component";
import { SelectAccountComponent } from './select-account/select-account.component';
import { NafathComponent } from './nafath/nafath.component';
import { CompanyLayoutComponent } from '@component/layouts/company-layout/company-layout.component';
import { ChangePasswordComponent } from './change-password/change-password.component';



const routes: Routes = [
  {path: '', component: LoginComponent},
  {path: 'accountType', component: SelectAccountComponent},
  {path: 'register', component: RegisterComponent},
  {path:'nafath',component:NafathComponent},
  {path: 'verify-otp', component: VerifyOtpComponent},
  {path: 'forgot-password', component: ForgotPasswordComponent},
  {path: 'reset-password', component: ResetPasswordComponent},
  {path:"register_company",component:CompanyLayoutComponent},
  // {path:"change-password",component:ChangePasswordComponent},

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {
}
