import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { provideStore } from '@ngrx/store';
import { metaReducers, reducers } from '@store/store';
import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  BrowserAnimationsModule,
  provideAnimations,
} from '@angular/platform-browser/animations';
import { provideEffects } from '@ngrx/effects';
import { LayoutEffects } from '@store/effects';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { LoadingBarModule } from '@ngx-loading-bar/core';
import { LoadingBarHttpClientModule } from '@ngx-loading-bar/http-client';
import { authInterceptor, errorInterceptorHandler } from './core/interceptors/auth.interceptor';
import { provideToastr } from 'ngx-toastr';
import { ErrorInterceptor } from './core/services/error.interceptor';
import {DatePipe} from "@angular/common";

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, '../assets/i18n/', '.json');
}

export const appConfig: ApplicationConfig = {
  providers: [
    DatePipe,
    provideRouter(routes),
    provideStore(reducers, { metaReducers }),
    provideEffects([LayoutEffects]),
    provideToastr(),

    provideAnimations(),
    TranslateService,
    provideHttpClient(withInterceptors([authInterceptor,errorInterceptorHandler])),
    importProvidersFrom(
      BrowserAnimationsModule,
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
      }),

      LoadingBarModule,
      LoadingBarHttpClientModule
    ),
  ],
};
