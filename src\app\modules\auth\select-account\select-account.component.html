<div
  class="box xl:p-6 dark:bg-bg4 font-cairo grid grid-cols-12 gap-2 xxxl:gap-5 h-[700px] items-center -mt-[25px] shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
  <div class="col-span-11 lg:col-span-11 ">
    <h3 class="mb-3 text-center font-extrabold text-[34px] font-cairo">{{ 'letsGetStarted' | translate }}</h3>
  
    @if(!userMainAccount){

      <h5 class="text-center font-normal text-sm  ">  <p>
        {{'haveaccount'|translate}}
        <a class="text-primary" href="#"> {{ 'Login' | translate }} </a>

      </p></h5>
    }

      <p class="text-3xl font-bold text-start mt-4 gap-60">{{ 'selectaccounttype' | translate }}</p>
      <div class="selectedcount">
         <div class="col-span-12 lg:col-span-12 my-12 flex justify-between">
              @for( image of images; track $index){
            <div  class="image-container dark:bg-inherit gap-8 rounded-xl flex flex-col items-center mx-4"
            [ngStyle]="{
                'background-color': selectedImageIndex === image.userType ? '#015C8D0D' : 'bg-inherit',
                'border': selectedImageIndex === image.userType ? '2px solid #015C8E' : '2px solid #E7E7E7'
              }"
              (click)="selectImage(image.userType!)">
            <img

            [src]="image.src"
            [alt]="image.alt"
            class="px-6  mx-6 mix-blend-multiply dark:bg-black-light"
            height="561"
          />
          <p class="image-text my-8 font-extrabold dark:text-inherit text-lg leading-6">{{ image.text | translate }}</p>
        </div>
        }
    </div>
    <div class="col-span-12 lg:col-span-12  my-12 mx-[24rem] items-center">
        <button class=" btn-primary px-36 py-3 rounded-[32px] text-center justify-center text-white flex w-96 h-12 bg-[#015C8E]"
        (click)="nextStep()"> {{ 'next' |translate}}
            <img src="assets/images/arrownext.svg" class="w-8 mx-2" />
        </button>

    </div>



  </div>

</div>




