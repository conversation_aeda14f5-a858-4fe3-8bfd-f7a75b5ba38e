.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999; /* 👈 make sure this is high */
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* optional dim background */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .modal-container {
    max-height: 80vh;
    background: white;
    background: white;
    width: 60% !important ;
    overflow-y: auto; 
    max-width:600px !important;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
  }
  
  .modal-text {
    margin: 10px 0;
    color: #666;
  }
  
  .textarea {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  
  .error-text {
    color: red;
    font-size: 12px;
  }
  
  .modal-buttons {
    display: flex;
      padding: 20px;
  }
  
  .btn-reject, .btn-accept {
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    border: none;
  }
  
  .btn-reject {
    margin: auto;
    color: red;
    border: 1px solid red;
    background: transparent;
  }
  
  .btn-reject:hover {
    background: red;
    color: white;
  }
  
  .btn-accept {
    margin: auto;
    color: green;
    border: 1px solid green;
    background: transparent;
    
  }
  
  .btn-accept:hover {
    background: green;
    color: white;
  }
  button {
    border-radius: 0; /* Completely square buttons */
  }
  
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    text-align: center;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
  }
  
  .otp-input {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: 1.5rem;
    border: 2px solid #ccc;
    border-radius: 8px;
  }
  
  .submit-button {
    width: 100%;
    margin-top: 10px;
    padding: 10px;
    background: #4f46e5;
    color: white;
    font-size: 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
  }
  
  .submit-button:hover {
    background: #4338ca;
  }
  
  .btn-reject, .btn-accept {
    padding: 10px 20px;
    border-radius: 999px; /* Fully rounded */
    font-weight: bold;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: background 0.3s, color 0.3s;
  }
  
  .btn-reject {
    color: red;
    border-color: red;
  }
  
  .btn-reject:hover {
    background: red;
    color: white;
  }
  
  .btn-accept {
    color: green;
    border-color: green;
  }
  
  .btn-accept:hover {
    background: green;
    color: white;
  }
  
  .custom-select .ngx-select .ngx-select__control {
    background-color: white !important;  /* Remove the background */
    border: 1px solid transparent !important;  /* Make the border transparent */
    box-shadow: none !important;  /* Remove any shadow */
  }
  
  /* Optional: You can add focus styles */
  .custom-select .ngx-select .ngx-select__control:focus {
    border-color: #ccc !important; /* Change border color when focused */
    background-color: transparent !important; /* Ensure focus background is transparent */
  }

  .popup-content {
    max-height: 400px; /* You can adjust this value to your desired height */
    overflow-y: auto;  /* This will make the content scrollable */
    padding: 20px;     /* Optional: add padding for spacing */
  }
  
  /* Make the accordion content scrollable */
  .accordion-content {
    max-height: 300px;  /* You can adjust this value based on how tall you want the accordion section */
    overflow-y: auto;   /* Enable vertical scrolling */
    padding-right: 1rem; /* Optional: ensure padding on the right for scroll bar */
    text-align: justify; /* Optional: better alignment for long text */
  }