import {Injectable} from '@angular/core';
import {IUserAccounts} from "../models/user-accounts.model";
import {ApiService} from "../../../core/services/api.service";
import {IKeyValue} from "../../../core/models/key-value.model";
import { BaseResponse } from '../../../core/models/api-response.model';
import { IQuery, PAGINATION } from '../../../core/models/pagination.model';
import { EncryptStorage } from 'encrypt-storage';

@Injectable({
  providedIn: 'root'
})

export class SettingsService {
  private path = '/Investor';
  private lookupsPath = '/shared/Lookups';
  private clientpath = '/client';
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
   pagination = PAGINATION;
  constructor(
    private apiService: ApiService,
  ) {
  }

  async getUserAccounts() {
    return await this.apiService.get<IUserAccounts>(`${this.path}/UserAccounts`);
  }

  async getQualificationRequest(query?: IQuery){
      query = {...query, limit: this.pagination.pageSize,userAccountId: this.#encryptTokenStorage.getItem('currentUser')?.id} as IQuery;
    return (await this.apiService.post(`${this.clientpath}/get/qualification-requests`,query));
  }
  async PostQualificationRequest(data: any){
  return (await this.apiService.post(`${this.clientpath}/Create/qualification-requests`,data));
}

 
  async updateUserAccounts(body: any) {
    return await this.apiService.put(`${this.clientpath}/profile/bank-account`, body);
  }


   async getprofileAccount() { 
    return await this.apiService.get<IUserAccounts>(`${this.clientpath}/profile`);
  }
  //  async createfinte() { 
  //   return await this.apiService.get<IUserAccounts>(`${this.clientpath}/profile`);
  // }
   async getBanks() { 
    return await this.apiService.get<IKeyValue>(`${this.lookupsPath}/Banks`);
  }
   async getIBanByBanks() { 
    return await this.apiService.get<IKeyValue>(`${this.clientpath}/profile/get-bank-account`);
  }
   async getIBans(iban: string) { 
    return await this.apiService.get<IKeyValue>(`${this.clientpath}/profile/bank-info/${iban}`);
  }

  async resetEmail(body: any) {
    return await this.apiService.post(`${this.clientpath}/authentication/ResetEmail`, body);
  }

  async confirmResetEmail(body: any) {
    return await this.apiService.post(`${this.clientpath}/authentication/ConfirmResetEmail`, body);
  }

  async resetMobileNumber(body: any) {
    return await this.apiService.post(`${this.clientpath}/authentication/ResetMobileNumber`, body);
  }

  async confirmResetMobileNumber(body: any) {
    return await this.apiService.post(`${this.clientpath}/authentication/ConfirmResetMobileNumber`, body);
  }
}
