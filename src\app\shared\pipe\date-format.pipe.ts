import { Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from "@angular/common";

@Pipe({
  name: 'dateFormat',
   standalone: true,
  // pure: false, // 🔥 Required to detect runtime changes
})
export class DateFormatPipe implements PipeTransform {
  constructor(private datePipe: DatePipe) {}

  transform(value: any, method: 'date' | 'date-time' = 'date'): unknown {
    if (!value) return value;

    // ✅ Always get the latest lang (no caching)
    const currentLang = localStorage.getItem('lang') || 'en';

    try {
      if (method === 'date') {
        return currentLang === 'ar' 
          ? this.datePipe.transform(value, 'yyyy-M-d') // Arabic: 2025-7-29
          : this.datePipe.transform(value, 'd-M-yyyy'); // English: 29-7-2025
      } else {
        return new Date(value).toLocaleString(currentLang, {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          hour12: currentLang === 'ar',
        });
      }
    } catch (e) {
      return value;
    }
  }
}