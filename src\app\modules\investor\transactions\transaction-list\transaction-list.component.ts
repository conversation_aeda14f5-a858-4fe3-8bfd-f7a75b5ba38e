import {Component} from '@angular/core';
import {colDef, DataTableModule} from "@bhplugin/ng-datatable";

import {DropdownComponent} from "@component/shared/dropdown/dropdown.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {DatePipe} from "@angular/common";
import {TransactionModel, TransactionSearch} from "../models/transaction.model";
import {TransactionService} from "../services/transaction.service";
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';

@Component({
  selector: 'app-transaction-list',
  standalone: true,
  imports: [
    DataTableModule,
    DateFormatPipe,
    DropdownComponent,
    TranslateModule
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './transaction-list.component.html',
  styleUrl: './transaction-list.component.css'
})
export class TransactionListComponent {
  columns: Array<colDef> = [];
  search:  TransactionSearch = {isServerMode: true} as TransactionSearch;
  transactions: TransactionModel[] = [];

  constructor(
    public translate: TranslateService,
    protected transactionService: TransactionService,
    private router: Router
  ) {
    this.transactionService.initPagination();
  }

 async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }

  onInputChange(e: any) {
  }

  async ngOnInit() {
    this.columns = [
      {title: this.translate.instant('TransactionNo'), field: 'transactionNo',},
      {title: this.translate.instant('name'), field: 'walletName',},
      {title: this.translate.instant('Amount'), field: 'amount',},
      {title: this.translate.instant('CreatedOn'), field: 'createdOn',},
      {title: this.translate.instant('DueDate'), field: 'actualDueDate',},
    ]
    await this.getList();
  }

  async getList() {
    try {
      this.transactions = await this.transactionService.getList(this.search) ?? []
    } catch (e: any) {
      console.log(e);
    }
  }
}
