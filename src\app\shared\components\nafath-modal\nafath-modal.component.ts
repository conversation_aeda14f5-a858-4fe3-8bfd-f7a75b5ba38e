import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {MDModalModule} from "@component/form/modals";
import {NgForOf} from "@angular/common";
import {PaginatorModule} from "primeng/paginator";
import {ReactiveFormsModule} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {ModalService} from "@component/form/modals/modal.service";
import {AuthService} from "../../../modules/auth/services/auth.service";
import {ISendNafathResponse} from "../../../modules/auth/models/login.model";
import {IUser} from "../../../modules/auth/models/user.model";

@Component({
  selector: 'app-nafath-modal',
  standalone: true,
  imports: [
    MDModalModule,
    NgForOf,
    PaginatorModule,
    ReactiveFormsModule,
    TranslateModule
  ],
  templateUrl: './nafath-modal.component.html',
  styleUrls: ['./nafath-modal.component.css']
})
export class NafathModalComponent implements OnInit {
  @ViewChild('submitButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() onSuccess = new EventEmitter<IUser>();
  @Output() reSend = new EventEmitter<void>();
  @Input() response: ISendNafathResponse = {} as ISendNafathResponse;

  constructor(
    protected modalService: ModalService,
    private authService: AuthService
  ) {
  }

  get truncatedRandom(): string {
    return (this.response?.random ?? ':(').toString().substring(0, 2);
  }

  ngOnInit(): void {
    this.scheduleCheckNafathStatus();
  }

  async checkNafathStatus() {
    if (this.response?.token) {
      try {
        return await this.authService.checkNafathStatus({token: this.response?.token});
      } catch (e: any) {
        throw e; // Rethrow error to handle in the caller
      }
    }
    return;
  }

  private scheduleCheckNafathStatus(): void {
    setTimeout(() => {
      this.checkNafathStatus()
        .catch((error: any) => {
          setTimeout(() => {
            this.checkNafathStatus()
              .catch((error: any) => {
                // console.error('Error checking Nafath status after 50 seconds', error);
              }).then((e: any) => this.onSuccess.emit(e));
          }, 15000); // 20 seconds after the first check
        }).then((e: any) => this.onSuccess.emit(e));
    }, 20000); // 30 seconds
  }
}
