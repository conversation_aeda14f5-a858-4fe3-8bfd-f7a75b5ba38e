import {Injectable} from '@angular/core';
import {<PERSON>ttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpStatusCode} from '@angular/common/http';
import {Observable, throwError} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {AuthService} from "../../modules/auth/services/auth.service";
import {ToastService} from "./toast.service";

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(private authenticationService: AuthService,
              private toast: ToastService
  ) {
  }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(catchError(err => {
      if (err.status === HttpStatusCode.Unauthorized) {
        // auto logout if 401 response returned from api
        this.authenticationService.logout();
      } else if (err.status === HttpStatusCode.BadRequest) {
        this.toast.error(err.error.message);
      }
      const error = err.error.message || err.statusText;
      return throwError(error);
    }))
  }
}
