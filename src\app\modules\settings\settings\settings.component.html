<div class="flex gap-4">
  
  <div class="box mb-4 flex-1 xxxl:mb-6">
    <div class="mb-6 pb-6 bb-dashed flex justify-between items-center">
      <h4 class="h4">{{ 'financialData' | translate }}</h4>
    </div>
    <form [formGroup]="userAccountsForm" class="flex flex-col space-y-4">
      <div class="col-span-2 md:col-span-1">
        <app-input [control]="userAccountsForm.get('iban')" [label]="'ibanNumber'"
          (onKeyup)="getIBanByBanks($event.target.value)" (onClear)="resetBankFields()" />
      </div>

      <div class="col-span-2 md:col-span-1">
        <app-input [control]="userAccountsForm.get('accountNo')" [label]="'accountNumber'" [type]="'number'" />
      </div>

      <div class="col-span-2 md:col-span-1">
        <app-select [control]="userAccountsForm.get('bankId')" [items]="IBanList?.data ? bankslist : allbankslist"
          [label]="'bankName'" [bindValue]="'id'" [bindLabel]="'name'" [placeholder]="'selectBank'"
          [disabled]="IBanList?.data !== null" />
      </div>
      <div class="col-span-2 flex justify-end w-full gap-2 md:gap-6 mt-2">
        <button (click)="updateUserAccounts()" class="btn" type="submit">{{ 'save' | translate }}</button>
      </div>
    </form>
  </div>
</div>

@if (modalService.isOpen('otp-modal')) {
<app-otp-modal (submitOtp)="updateUserAccountsVerified($event)" />
}
@if (modalService.isOpen('otp-reset-email-modal')) {
<app-otp-modal [id]="'otp-reset-email-modal'" (submitOtp)="resetEmail($event)" />
}
@if (modalService.isOpen('otp-confirm-reset-email-modal')) {
<app-otp-modal [id]="'otp-confirm-reset-email-modal'" [title]="'emailVerification'"
  [subTitle]="'enterEmailVerificationCode'" (submitOtp)="confirmResetEmail($event)" />
}
@if (modalService.isOpen('otp-reset-mobile-modal')) {
<app-otp-modal [id]="'otp-reset-mobile-modal'" [title]="'verifyCurrentPhoneNumber'"
  [subTitle]="'enterCurrentPhoneVerificationCode'" (submitOtp)="resetMobileNumber($event)" />
}


@if (modalService.isOpen('otp-confirm-reset-mobile-modal')) {
<app-otp-modal [id]="'otp-confirm-reset-mobile-modal'" [title]="'verifyNewPhoneNumber'"
  [subTitle]="'enterNewPhoneVerificationCode'" (submitOtp)="confirmResetMobileNumber($event)" />
}