<div class="col-span-12">
  <div class="box col-span-12 lg:col-span-6">
    <div class="bb-dashed mb-0 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-1 lg:pb-1">
      <h4 class="h4">{{ 'OpenOpportunities' | translate }}</h4>
      <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
         <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
        <form class="bg-primary/5 dark:bg-bg3 border border-n30 dark:border-n500 flex gap-3 rounded-[30px] 
          focus-within:border-primary p-1 items-center justify-between min-w-[200px] xxl:max-w-[319px] w-full">
          <input (input)="onInputChange($event)"
            class="bg-transparent border-none text-sm ltr:pl-4 rtl:pr-4 py-1 w-full"
            [placeholder]="'Search' | translate" type="text" />
          <button title="search" type="button"
            class="bg-primary shrink-0 rounded-full w-7 h-7 lg:w-8 lg:h-8 flex justify-center items-center text-n0">
            <i class="las la-search text-lg"></i>
          </button>
        </form>
     <!-- Filter Button -->
<button (click)="openModal()" class="relative">
  <i class="la la-filter text-xl text-gray-800"></i>
  @if (isFiltered) {
    <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
  }
</button>
      </div>
     
      </div>
    </div>


   

<div class="overflow-x-auto px-4">
  @if(opprtunityData.length > 0) {
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      @for (item of Opprtunitylist(); track item.id) {
        <div class="relative">
          <!-- Character circle -->
          <div
            class="absolute rtl:left-12 rtl:top-0 ltr:-right-4 ltr:top-0 dark:bg-slate-800  w-[4.5rem] h-[4.5rem] rounded-full border-2 flex items-center justify-center bg-white text-2xl font-bold  -translate-x-1/2"
            [class]="getStatusClass(item.status)">
            {{ item.scoreName }}
          </div>

          <!-- Card container -->
          <div
            class="mt-10 w-full max-w-sm mx-auto bg-[#015C8E0D] rounded-3xl shadow-md border border-gray-200 font-sans text-right"
            (click)="openDetails(item.id)">
            
            <!-- Title -->
            <div class="text-black px-6 pt-12 text-xl font-extrabold text-center">
              {{ item.title }}
            </div>

            <!-- Buttons row -->
            <div class="flex flex-col sm:flex-row justify-center items-center gap-2 px-4 mt-4">
              <button class="bg-[#015C8E80] text-white font-bold py-2 px-4 rounded-lg w-full sm:w-auto">
                @if(item.requestType == 1) {
                  <i class="las la-file-invoice"></i>
                } @else if(item.requestType == 2) {
                  <i class="las la-cubes"></i>
                }
                {{ item.requestTypeName | translate }}
              </button>

              <button class="bg-white border-2 dark:bg-inherit datk: border-inherit font-bold py-2 px-4 rounded-lg w-full sm:w-auto"
                [ngClass]="getStatusClass(item.status)">
                <i [class]="getStatusIcon(item.status)"></i>
                {{ item.statusName | translate }}
              </button>
            </div>

            <!-- Card content -->
            <div class="space-y-3 p-6">
              <div class="flex justify-between items-center ">
                <div class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{"startinvestment" | translate}}</div>
                <span class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{ item.amount }}</span>
              </div>

              <div class="w-full bg-gray-200 mt-2 rounded-full h-2.5 dark:bg-gray-700 mb-6">
                <div class="bg-black dark:text-danger-50 h-2.5 rounded-full" [style.width.%]="calculateProgressWidth(item.amount)"></div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{ "Returnoninvestment" | translate }}</span>
                <span class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{ item.profitsPer }}%</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{ "Peroid" | translate }}</span>
                <span class="text-black dark:text-danger-50 font-bold text-sm sm:text-lg">{{ item.durationInMonths }} {{ "Monthes" | translate }}</span>
              </div>
            </div>
          </div>
        </div>
      }
    </div>

    <!-- Pagination (unchanged except for padding) -->
    <div class="flex flex-col sm:flex-row justify-between items-center mt-8 px-4 gap-4">
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600">{{ 'Rows per page:' | translate }}</span>
        <select 
          class="border rounded p-1 text-sm"
          (change)="handlePageSizeChange($any($event.target).value)">
          @for (size of opportunitiesService.pagination.pageSizeOptions; track size) {
            <option [value]="size" [selected]="size === opportunitiesService.pagination.pageSize">
              {{ size }}
            </option>
          }
        </select>
      </div>

      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600">
          {{ opportunitiesService.pagination.page * opportunitiesService.pagination.pageSize - opportunitiesService.pagination.pageSize + 1 }} - 
          {{ minValue(opportunitiesService.pagination.page * opportunitiesService.pagination.pageSize, opportunitiesService.pagination.totalRows) }} 
          {{ 'of' | translate }} 
          {{ opportunitiesService.pagination.totalRows }}
        </span>

        <button 
          class="p-1 rounded border disabled:opacity-50"
          [disabled]="opportunitiesService.pagination.page === 1"
          (click)="handlePageChange(opportunitiesService.pagination.page - 1)">
          <svg class="w-4 h-4 rtl:rotate-180" viewBox="0 0 24 24"><path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
        </button>

        <button 
          class="p-1 rounded border disabled:opacity-50"
          [disabled]="opportunitiesService.pagination.page * opportunitiesService.pagination.pageSize >= opportunitiesService.pagination.totalRows"
          (click)="handlePageChange(opportunitiesService.pagination.page + 1)">
          <svg class="w-4 h-4 rtl:rotate-180" viewBox="0 0 24 24"><path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
        </button>
      </div>
    </div>
  } @else if (opprtunityData.length == 0) {
    <div class="text-center py-8">
      @if(opportunitiesService.pagination.loading) {
        <div class="text-gray-500">{{ 'Loading...' | translate }}</div>
      } @else {
        <div>{{ 'NoDataAvailable' | translate }}</div>
      }
    </div>
  }
</div>

  </div>
</div>


<!-- Modal -->
@if (modalService.isOpen('customFilterModal')) {
  <app-custom-filter 
    id="customFilterModal"
    [fields]="fields"
    [isFiltered]="isFiltered"
    [result]="filterResult"
    (filterApplied)="handleFilterResult($event)"
    (closed)="onModalClosed()">
  </app-custom-filter>
}