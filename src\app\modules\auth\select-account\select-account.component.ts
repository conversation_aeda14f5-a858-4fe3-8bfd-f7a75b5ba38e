import { Component, Input, input, OnChang<PERSON>, SimpleChanges } from '@angular/core';

import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {Router} from "@angular/router";
import {InputComponent} from "@component/form/input/input.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {phoneNumberValidator} from "../../../shared/validators/phone.validator";
import {passwordValidator} from "../../../shared/validators/password.validator";
import {ButtonComponent} from "@component/form/button/button.component";
// import {OtpModalComponent} from "../../otp/otp-modal/otp-modal.component";
import {ModalService} from "@component/form/modals/modal.service";
import {AuthService} from "../services/auth.service";
import {DatePickerComponent} from "@component/form/date-picker/date-picker.component";
import {CommonModule, DatePipe, NgClass, NgFor} from "@angular/common";
import {nationalIDValidator} from "../../../shared/validators/national-id.validator";
import {SelectComponent} from "@component/form/select/select.component";
import {NafathModalComponent} from "@component/nafath-modal/nafath-modal.component";
import {ISendNafathResponse} from "../models/login.model";
import {IUser, UserAccountType} from "../models/user.model";
import {ICommercialRegistrations} from "../models/commercial-registrations.model";
import {ToastService} from "../../../core/services/toast.service";
import { EncryptStorage } from 'encrypt-storage';

enum RegisterTabs {
  mobile = 0,
  nationalId = 1,
}




@Component({
  selector: 'app-select-account',
  standalone: true,
  imports: [
    ReactiveFormsModule,

CommonModule,
    TranslateModule,

  ],
  providers: [
    DatePipe
  ],
  templateUrl: './select-account.component.html',
  styleUrl: './select-account.component.css'
})
export class SelectAccountComponent implements OnChanges{
  @Input()userMainAccount!: number
  encryptStorageStep = new  EncryptStorage('User_info_step');
  
 mobileForm!: FormGroup
  nationalIdForm!: FormGroup
  commercialRegistrationForm!: FormGroup
  isPasswordHidden = true;
  selectedRegisterTab = RegisterTabs.mobile
  response: ISendNafathResponse = {} as ISendNafathResponse;
  commercialRegistrations: ICommercialRegistrations[] = [] as ICommercialRegistrations[];
  protected readonly RegisterTabs = RegisterTabs;
  images:{src:string,alt:string,text:string,userType:UserAccountType}[] = [
    { src: 'assets/images/investor.svg', alt: 'Investor',
      text: this.translate.instant('IndivdualInvstors'),userType:UserAccountType.IndivdualInvstor },
    { src: 'assets/images/brower.svg', alt: 'Brower', 
      text: this.translate.instant('asRequired'),userType:UserAccountType.InvestmentCompany },
    { src: 'assets/images/companyBrower.svg', alt: 'Company Brower',
       text: this.translate.instant('asCoraporate'),userType:UserAccountType.Borrower }
  ];

// images: {src: string, alt: string, text: string, userType: UserAccountType}[] = [
//   { src: 'assets/images/investor.svg', alt: 'Investor', text: '', userType: UserAccountType.IndivdualInvstor },
//   { src: 'assets/images/brower.svg', alt: 'Brower', text: '', userType: UserAccountType.InvestmentCompany },
//   { src: 'assets/images/companyBrower.svg', alt: 'Company Brower', text: '', userType: UserAccountType.Borrower }
// ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    protected modalService: ModalService,
    private authService: AuthService,
    public translate: TranslateService,

  ) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.userMainAccount ) { // in case user login in try to create valid sub account
      // this.images =this.images.filter(image=>image.userType !==(this.userMainAccount()))
      this.images =this.images.filter(image=>image.userType !==UserAccountType.IndivdualInvstor)
    }else{
      if(localStorage.getItem('token')){ // if user type 0
        this.authService.logout()
        this.router.navigate(['/auth'])
      }else{
        localStorage.clear()
      }
    }
  }
  ngOnInit() {
  //    this.translate.get([
  //   'individualInvestor',
  //   'asRequired',
  //   'asCoraporate'
  // ]).subscribe(translations => {
  //   this.images[0].text = translations['individualInvestor'];
  //   this.images[1].text = translations['asRequired'];
  //   this.images[2].text = translations['asCoraporate'];
  // });

    this.mobileForm = this.fb.group({
      mobile: [null, [Validators.required, phoneNumberValidator()]],
      password: [null, [Validators.required, passwordValidator()]]
    })
    this.nationalIdForm = this.fb.group({
      nationalId: [null, [Validators.required, nationalIDValidator()]],
    })
    this.commercialRegistrationForm = this.fb.group({
      cr: [null, [Validators.required]],

    })
  }

  selectedImageIndex: number | null = null;

  selectImage(index: number): void {
    this.encryptStorageStep.setItem('selected_type',index)
    this.selectedImageIndex = index;
  }
  async registerMobile() {
    if (this.mobileForm.valid) {
      await this.authService.registerMobile({
        mobile: this.mobileForm.value.mobile
      })
      this.openOtpModal()
    } else {
      this.mobileForm.markAllAsTouched()
    }
  }

  async verifyOtp(otp: string) {
    try {
      const body = {
        mobile: this.mobileForm.value.mobile,
        code: otp,
      }
      this.response = {token: await this.authService.validateMobile(body)}
      this.selectedRegisterTab = RegisterTabs.nationalId
      this.modalService.close('otp-modal')
    } catch (error: any) {
    }
  }

  async sendNafathRequst() {
    try {
      if (this.response?.token) {
        const body = {
          nationalId: +this.nationalIdForm.value.nationalId,
          password: this.mobileForm.value.password,
          token: this.response?.token
        }
        this.response = await this.authService.sendNafathReqeust(body) ?? {} as ISendNafathResponse;
        this.openNafathModal();
      } else {
        this.selectedRegisterTab = RegisterTabs.mobile
      }
    } catch (e: any) {
    }
  }
  nextStep(){
    if(this.userMainAccount){ 

      this.authService.selectedSubAccount.set(this.selectedImageIndex ?? 1)
      // console.log(this.authService.selectedSubAccount())
    }else{

      if(this.selectedImageIndex ==null) return
      this.router.navigate(['/auth/register'],{ state: { userSelect: { selected:(this.selectedImageIndex??0)
      } } } );

    }
  }
  async onNafathSuccess(user: IUser) {
    localStorage.setItem('token', user.token);
    this.modalService.close('nafath-modal')
    this.router.navigate(['/']);
  }

  togglePasswordVisibility = () => this.isPasswordHidden = !this.isPasswordHidden;
  openOtpModal = () => this.modalService.open('otp-modal')
  openNafathModal = () => this.modalService.open('nafath-modal')
}
