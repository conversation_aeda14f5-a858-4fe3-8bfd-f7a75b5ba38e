



input:focus {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
  0 0 0 3px rgba(0, 126, 255, 0.1);
}

input.error {
  @apply border-danger bg-danger/[0.08]  placeholder-danger/70 focus:border-danger text-danger #{!important};
}


input {
  @apply w-full mb-1 text-sm bg-n0 dark:bg-bg4 bg-primary/5 border border-n30 dark:border-n500 rounded-3xl px-3 md:px-6 py-2 md:py-3 #{!important};
}
input:active {
  @apply w-full mb-1 text-sm bg-n0 dark:bg-bg4 bg-primary/5 border border-n30 dark:border-n500 rounded-3xl px-3 md:px-6 py-2 md:py-3 #{!important};
}

