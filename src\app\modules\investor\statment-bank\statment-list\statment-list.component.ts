import { Component, inject, NgModule, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {colDef, DataTableModule} from "@bhplugin/ng-datatable";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {ActivatedRoute, Router} from "@angular/router";
import {CommonModule, DatePipe} from "@angular/common";
import {StatmentService} from "../services/statment.service";
import { DatePickerComponent } from '@component/date-picker/date-picker.component';
import { ButtonComponent } from '@component/button/button.component';
import { FormGroup, FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';

import { IJournalLine } from '../interfaces/statement.interface';
import moment from 'moment';
import { ToastService } from '../../../../core/services/toast.service';
import { PAGINATION } from '../../opportunities/services/opportunities.service';
import { IQuery } from '../../../../core/models/pagination.model';
import { TooltipModule } from 'primeng/tooltip';
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';

@Component({
  selector: 'app-statment-list',
  standalone: true,
  imports: [
    DataTableModule,
DatePickerComponent,
ButtonComponent,
    TranslateModule,
    CommonModule,
TooltipModule,
DateFormatPipe
  ],
    providers: [
      DatePipe
    ],
  templateUrl: './statment-list.component.html',
  styleUrl: './statment-list.component.css'
})

export class StatmentListComponent implements OnInit, OnDestroy {
search: IQuery = {
  pageSize: 10, // Fixed to 10 items per page
  page: 1,
  isServerMode: true,
  find: '',
  totalRows: 0,
  loading: false,
  pageSizeOptions: [10, 25, 50, 100] // Add default options or use from PAGINATION if available
};

  statmentList: any;
  journalLines: IJournalLine[] = [];
  subs$ = new Subscription();

  hoveredTitle: string | null = null;
  tooltipX: number = 0;
  tooltipY: number = 0;

  filtersForm = new FormGroup({
    startDate: new FormControl(),
    endDate: new FormControl(),
    find: new FormControl('')
  });

  get columns(): Array<colDef> {
    return [
        { title: this.translate.instant('lastUpdateDate'), field: 'entryDate' },
      { title: this.translate.instant('Title'), field: this.translate.currentLang === 'ar' ? 'title' : 'titleEn' },
      { title: this.translate.instant('credit'), field: 'credit' },
      { title: this.translate.instant('debit'), field: 'debit' },
      { title: this.translate.instant('balance'), field: 'runningBalance' },
    
    ];
  };

  get tableRows(): any[] {
    if (!this.statmentList || !this.journalLines?.length) return [];

    const rows = [...this.journalLines];
    if (this.statmentList) {
      rows.push({
        isFooter: true,
        title: '',
        titleEn: '',
        debit: this.statmentList.totalDebit || 0,
        credit: this.statmentList.totalCredit || 0,
        runningBalance: this.statmentList.finalBalance || 0,
      } as any);
    }
    return rows;
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService,
    public accountsService: StatmentService,
    private toast: ToastService
  ) {}

  ngOnInit(): void {
    this.accountsService.initStatementsPagination();
    this.loadData();
  }
async loadData(): Promise<void> {
  if (!this.validateDates()) return;

  this.search.loading = true;

  try {
    const apiParams = {
      ...this.filtersForm.value,
      find: this.search.find,
      page: this.search.page,
      pageSize: this.search.pageSize
    };
   
    const { data, totalRecords } = await this.accountsService.getList(apiParams);

    this.statmentList = data;
    this.journalLines = data?.journalLines || [];
    this.search.totalRows = totalRecords;
  } catch (error) {
    this.toast.error(this.translate.instant('Failed to load data'));
    this.statmentList = null;
    this.journalLines = [];
    this.search.totalRows = 0;
  } finally {
    this.search.loading = false;
  }
}

async handlePageChange(newPage: number): Promise<void> {
  console.log('Page changed:', newPage);

  if (this.search.page !== newPage) {
    this.search.page = newPage;
    await this.loadData();
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
async handlePageSizeChange(newSize: number): Promise<void> {
  if (!this.search.pageSizeOptions.includes(newSize)) return;

  if (this.search.pageSize !== newSize) {
    this.search.pageSize = newSize;
    this.search.page = 1;
    await this.loadData();
  }
}

showTooltip(event: MouseEvent, title: string): void {
  this.hoveredTitle = title;
  this.tooltipX = event.clientX + 10;
  this.tooltipY = event.clientY + 10;
}

hideTooltip(): void {
  this.hoveredTitle = null;
}


  validateDates(): boolean {
    const start = this.filtersForm.get('startDate')?.value;
    const end = this.filtersForm.get('endDate')?.value;

    if (start && end && new Date(start) > new Date(end)) {
      this.toast.error('End date cannot be before start date');
      return false;
    }
    return true;
  }
 formatDate(date: string): string {
      const currentLang = localStorage.getItem('lang') || 'en';
   return currentLang === 'ar' 
          ?moment(date).format('YYYY-MM-DD | ss:mm:HH')
          :moment(date).format('DD-MM-YYYY | HH:mm:ss'); // English: 29-7-2025

}
  ngOnDestroy(): void {
    this.subs$.unsubscribe();
  }
}
