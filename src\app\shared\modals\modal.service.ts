import {Injectable} from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modals: { [key: string]: boolean } = {};
  private modalResolvers: { [key: string]: (value?: any) => void } = {};
  constructor() {
  }

  open(id: any) {
    this.modals[id] = true;
  }

  close(id: string) {
    this.modals[id] = false;
  }

  isOpen(id: string) {
    return this.modals[id];
  }
   closeWithResult(id: string, result?: any) {
    this.close(id); 
    if (this.modalResolvers[id]) {
      this.modalResolvers[id](result);
      delete this.modalResolvers[id]; // Cleanup after resolving
    }
  }
  
  openWithResult(id: string): Promise<any> {
    return new Promise((resolve) => {
      this.modals[id] = true;
      this.modalResolvers[id] = resolve; // Store resolver for later
    });
  }

  removeClassById(elementId: string, className: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.remove(className);
    }
  }

  addClassById(elementId: string, className: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.add(className);
    }
  }
}


