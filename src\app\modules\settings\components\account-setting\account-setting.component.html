<div class="font-cairo grid grid-flow-row-dense">

            <h1 class="font-semibold text-3xl text-[#222E48]">{{ 'Settings' | translate }}</h1>
            <p class="border-b-2 py-3 border-dashed border-[#015C8E]"></p>
      



<div class="card mt-4">
    <div class="card-content">
      <form class="grid grid-cols-2 gap-4 xxxl:gap-6">
        <div class="col-span-2 md:col-span-1">
          <label>{{ "Username" | translate }}</label> : {{ profileData?.name }}
        </div>
        <div class="col-span-2 md:col-span-1">
          <label>{{ "nationalId" | translate }}</label> : {{ profileData?.identityId	||("notfound"|translate) }}
        </div>
        <div class="col-span-2 md:col-span-1">
          <label>{{ "MobileNo" | translate }}</label> : {{ profileData?.mobile ||("notfound"|translate)}}
        </div>
     
        <div class="col-span-2 md:col-span-1">
          <label>{{ "CreatedOn" | translate }}</label> : {{ profileData?.createdOn | dateFormat }}
        </div>
        <div class="col-span-2 md:col-span-1">
            <label>{{ "email" | translate }}</label> : {{ profileData?.email ||("notfound"|translate)}}
          </div>
          <div class="col-span-2 md:col-span-1">
            <label>{{ "Status" | translate }}</label> : {{ (profileData?.statusName |translate) ||("notfound"|translate) }}
          </div>
          <div class="col-span-2 flex justify-end w-full gap-2 md:gap-6 mt-2" (click)="goBack()">
            <button  class="btn" type="submit">{{ 'Personal-Growth' | translate }}</button>
          </div>
 
      
   

  
      </form>
      <p class="border-b-2 py-3 border-dashed border-[#015C8E]"></p>
    </div>
  </div>
  <div class="border-b  border-gray-200 dark:border-gray-700">
    <ul
      class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400"
    >
      <li class="me-2" *ngFor="let tab of tabs">
        <a
          (click)="changeTab(tab)"
          [id]="tab"
          [ngClass]="{ 'border-b-primary': activeTab.field === tab.field}"
          class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group transition ease-in-out duration-300"
        >
          {{ tab.title! | translate }}
        </a>
      </li>
    </ul>
  </div>


  @switch (activeTab.field) {
    @case("EditInfo") {
        <app-edit-fintic-info [profileData]="profileData"/>
       
 }
 @case("EditFinticInfo") {
    <app-settings />
}
    @case("Notifications") {
    
 }
   
 @case("pauseAccount") {

}


}


</div>

