<div class="flex col-span-12 gap-4 sm:justify-between justify-center items-center flex-wrap">
  <p>Showing {{ startIndex + 1 }} to {{ endIndex + 1 }} of {{ totalData }} entries</p>
  <ul class="flex gap-2 md:gap-3 flex-wrap md:font-semibold items-center">
    <li>
      <button [disabled]="currentPage == 1" (click)="prevPage()" class="hover:bg-primary text-primary rtl:rotate-180 hover:text-n0 border md:w-10 duration-300 md:h-10 w-8 h-8 flex items-center rounded-full justify-center border-primary">
        <i class="las la-angle-left text-lg"></i>
      </button>
    </li>
    @for (page of pages; track page) {
      <li>
        <button (click)="paginate(page)" [ngClass]="{ 'bg-primary !text-n0': currentPage == page }" class="hover:bg-primary  bg-primary hover:text-n0 border md:w-10 duration-300 md:h-10 w-8 h-8 flex text-primary items-center rounded-full justify-center border-primary">{{ page }}</button>
      </li>
    }
    <li>
      <button [disabled]="currentPage == totalPages" (click)="nextPage()" class="hover:bg-primary text-primary hover:text-n0 rtl:rotate-180 border md:w-10 duration-300 md:h-10 w-8 h-8 flex items-center rounded-full justify-center border-primary">
        <i class="las la-angle-right text-lg"></i>
      </button>
    </li>
  </ul>
</div>
