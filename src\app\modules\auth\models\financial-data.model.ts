export interface financialInformationModel {
  userFinancialDataID?: string;
  userID?: string;
  nationalID?: number;
  annualIncomeRange?: number;
  investmentExperience?: number;
  riskTolerance?: number;
  jobInformation?: number;
  isBoardMember?: boolean;
  isPoliticallyExposedPerson?: boolean;
  isAssociatedWithPEP?: boolean;
  isRealBeneficiary?: boolean;
  jobTitle?: string;
  companyName?: string;
  companyAddress?: string;
}
