<div *ngIf="companyDetails$ | async as companyDetails">

  <div class="box mb-4 grid grid-cols-1 gap-4 xxxl:gap-6 bb-dashaed p-6">
    <div class="col-span-4 pb-2 bb-dashed">
      <h5>{{'companyInformation' | translate}} </h5>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{ 'companyName' | translate }}:</strong> {{ companyDetails.name }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{ 'businessTypeName' | translate }}:</strong> {{ companyDetails.businessTypeName }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p><strong>{{'capitalSharesCount' | translate }}:</strong> {{ companyDetails.capitalSharesCount }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'capitalSharePrice' | translate }}:</strong> {{ companyDetails.capitalSharePrice | number }}
      </p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'capitalAnnouncedAmount' | translate }}:</strong> {{ companyDetails.capitalAnnouncedAmount |
        number }}
      </p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'capitalPaidAmount' | translate }}:</strong> {{ companyDetails.capitalPaidAmount | number }}
      </p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'capitalSubscribedAmount' | translate }}:</strong> {{ companyDetails.capitalSubscribedAmount |
        number }}
      </p>
    </div>
    <!--  -->
    <div class="col-span-4 mt-4 pb-2 bb-dashed">
      <h5>{{'addressInformation' | translate}} </h5>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'locationName' | translate }}:</strong> {{ companyDetails.locationName }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'district' | translate }}:</strong> {{ companyDetails.district }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'streetName' | translate }}:</strong> {{ companyDetails.streetName }}</p>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p> <strong>{{'address' | translate }}:</strong> {{ companyDetails.address }}</p>
    </div>
    <div class="col-span-4 mt-4 pb-2 bb-dashed">
      <h5>{{'contactInformation' | translate}} </h5>
    </div>
    <div class="col-span-2 md:col-span-2">
      <p>
        <strong>{{'email' | translate }}:</strong> {{ companyDetails.email }}
      </p>
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.telephone1">
      <p> <strong>{{'telephone' | translate }}:</strong> {{ companyDetails.telephone1 }}</p>
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.fax1">
      <strong> {{'fax' | translate }}:</strong> {{ companyDetails.fax1 }}
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.postalBox1">
      <strong>{{'postalBox' | translate }}:</strong> {{ companyDetails.postalBox1 }}
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.telephone2">
      <strong>{{'telephone' | translate }}:</strong> {{ companyDetails.telephone2 }}
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.fax2">
      <strong>{{'fax' | translate }}:</strong> {{ companyDetails.fax2 }}
    </div>
    <div class="col-span-2 md:col-span-2" *ngIf="companyDetails.postalBox2">
      <strong>{{'postalBox' | translate }}:</strong> {{ companyDetails.postalBox2 }}
    </div>
    <div class="col-span-2 md:col-span-2">
      <strong> {{'website' | translate }}:</strong> {{ companyDetails.website }}
    </div>
    <!-- <div class="col-span-2 md:col-span-2" *ngFor="let key of getKeys(companyDetails)">
      <strong>{{ key | translate }}:</strong> {{ companyDetails[key] }}
    </div> -->
  </div>

  <div class="box mb-4 flex flex-row flex-wrap p-2">
    <h4 class="basis-full mb-2 pb-3 bb-dashed"> {{'companyOwners' | translate}}: </h4>
    <div class="basis-full p-3 mb-1">
      <div class="basis-full">
        <ng-datatable [columns]="ownersColumns" [isServerMode]="false" [rows]="companyDetails.companyOwners"
          [totalRows]="companyDetails.companyOwners.length" class="whitespace-nowrap table-hover" [pagination]="false"
          firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
          lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
          nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
          previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'>
        </ng-datatable>
      </div>
    </div>
  </div>

  <div class="box flex flex-row flex-wrap p-3 mb-3">
    <h4 class="basis-full mb-2 pb-3 bb-dashed"> {{'commercialRegistration' | translate}}: </h4>
    <div class="basis-full flex">
      <div class="basis-6/12 py-2">
        <strong> {{'commercialRegistrationNumber' | translate }}</strong>: {{companyDetails.crNumber}}
      </div>
      <div class="basis-6/12 py-2">
        <strong> {{'commercialRegistrationStatus' | translate }}</strong>: {{currentLang === 'ar' ?
        companyDetails.statusNameAr : companyDetails.statusNameEn}}
      </div>
    </div>
  </div>

  <div class="box flex flex-row flex-wrap p-3 mb-3">
    <h4 class="basis-full mb-2 pb-3 bb-dashed"> {{'NafathUser' | translate}}: </h4>
    <div class="basis-6/12 py-2">
      <p><strong>{{'nafathUserId' | translate }}:</strong> {{companyDetails.userNafath.userNafathID}}</p>
    </div>
    <div class="basis-6/12 py-2">
      <p>
        <strong> {{'fullName' | translate }}</strong>:
        {{currentLang === 'ar' ?
        companyDetails.userNafath.fullNameAr : companyDetails.userNafath.fullNameEn}}
      </p>
    </div>
    <div class="basis-6/12 py-2">
      <p>
        <strong> {{'nationality' | translate }}</strong>: {{currentLang === 'ar' ?
        companyDetails.userNafath.nationalityAr : companyDetails.userNafath.nationalityEn}}
      </p>
    </div>

    <div class="basis-6/12 py-2">
      <p>
        <strong> {{'gender' | translate }}</strong>: {{
        (companyDetails.userNafath.gender === 'M' ? 'male' : 'female' ) | translate}}
      </p>
    </div>

    <div class="basis-6/12 py-2">
      <p>
        <strong> {{'dateOfBirth' | translate }}</strong>: {{companyDetails.userNafath.dateOfBirth | date}}
      </p>
    </div>

    <div class="basis-6/12 py-2">
      <p>
        <strong> {{'nationalId' | translate }}</strong>: {{companyDetails.userNafath.nationalID}}
      </p>
    </div>
  </div>
</div>
