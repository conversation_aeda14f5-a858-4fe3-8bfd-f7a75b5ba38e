import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../../core/services/toast.service';
import { AuthService } from '../../../../auth/services/auth.service';
import { FinancingData } from '../../interface/financing.model';
import { LoanRequestService } from '../../Service/loan-request.service';
import { CommonModule, DatePipe } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { DateFormatPipe } from '../../../../../shared/pipe/date-format.pipe';

@Component({
  selector: 'app-loan-request-attachments',
  standalone: true,
  imports: [
    DataTableModule,
    CalendarModule,
    FormsModule,
    CommonModule,
    TranslateModule,
  ],
  providers: [DatePipe],
  templateUrl: './loan-request-attachments.component.html',
  styleUrl: './loan-request-attachments.component.css',
})
export class LoanRequestAttachmentsComponent {
  @Input() loanRequestId!: string;
  columns: Array<colDef> = [];
  attachmentRequest!: FormGroup;
  LoanRequstAttachmentsData: any;
  selectedFile: File | null = null;
  fileType = [
    {
      id: 1,
      fileType: this.translate.instant(
        'AdditionalAttachments.DelegationLetter'
      ),
    },
    {
      id: 2,
      fileType: this.translate.instant('AdditionalAttachments.Cashflow'),
    },
    {
      id: 3,
      fileType: this.translate.instant(
        'AdditionalAttachments.FinancialStatements'
      ),
    },
    { id: 4, fileType: this.translate.instant('AdditionalAttachments.Other') },
  ];

  constructor(
    private toastr: ToastService,
    public translate: TranslateService,
    protected LoanRequestservice: LoanRequestService,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private adminUsersService: AuthService
  ) {}

  async ngOnInit() {
    this.initRoleForm();
    this.setupColumns();
    this.getAttachments();
  }

  setupColumns() {
    this.columns = [
      { title: this.translate.instant('Attachment'), field: 'filePath' },
      { title: this.translate.instant('Type'), field: 'fileType' },
      { title: this.translate.instant('requestDate'), field: 'requestDate' },
      { title: this.translate.instant('UploadedAt'), field: 'requestDate' },
      { title: this.translate.instant('Actions'), field: 'Actions' },
    ];
  }

  previewFile(filePath: string) {
    const win = window.open(filePath, '_blank');
    if (win) {
      win.opener = null; // Security best practice
    }
  }

  initRoleForm(): void {
    this.attachmentRequest = this.fb.group({
      AttachmentId: ['', [Validators['required']]],
      File: ['', [Validators['required']]],
    });
  }

  async getAttachments() {
    try {
      this.route.params.subscribe((params) => {
        this.loanRequestId = params['id'];
        this.LoanRequestservice.getLoanRequstAttachments(
          this.loanRequestId
        ).then((res: any) => {
          this.LoanRequstAttachmentsData = res?.data;
        });
      });
    } catch (e: any) {
      this.toastr.error(e);
    }
  }

  async onFileSelected(event: Event, attachmentId: string) {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (!file) {
    this.toastr.error('Please select a file first');
    return;
  }

 
  this.selectedFile = file;

  const previewUrl = URL.createObjectURL(file);

  // Update the row in LoanRequstAttachmentsData
  const index = this.LoanRequstAttachmentsData.findIndex(
    (x: any) => x.id === attachmentId
  );

  if (index !== -1) {
    this.LoanRequstAttachmentsData[index] = {
      ...this.LoanRequstAttachmentsData[index],
      filePath: previewUrl,
      fileType: file.type,
      requestDate: new Date()
    };
  }

  // Clear file input so same file can be reselected later
  input.value = '';
}


  // Combined file selection and request sending
  // async onFileSelected(event: Event, attachmentId: string) {
  //   const input = event.target as HTMLInputElement;
  //   const file = input.files?.[0];

  //   if (!file) {
  //     this.toastr.error('Please select a file first');
  //     return;
  //   }

  //   this.selectedFile = file;
   

  //   input.value = '';
  // }
//  await this.sendRequest(attachmentId);
  async sendRequest(id: string): Promise<void> {
    if (!this.selectedFile) {
      this.toastr.error('No file selected');
      return;
    }

    try {
      
      const formData = new FormData();
      formData.append('AttachmentId', id);
      formData.append('File', this.selectedFile);

      this.LoanRequstAttachmentsData =
        await this.LoanRequestservice.sendAttachmentRequest(formData);

      if (this.LoanRequstAttachmentsData.statusCode === 200) {
        this.toastr.success('Submitted successfully');
        this.getAttachments(); // Refresh attachments list
      } else {
        this.toastr.error(
          this.LoanRequstAttachmentsData.message || 'Upload failed'
        );
      }
    } catch (error) {
      console.error('Upload error:', error);
      this.toastr.error('Error submitting attachment');
    } finally {
      this.selectedFile = null;
    }
  }

  // Add this helper method to your component

  isImageUrl(url: string): boolean {
  if (!url) return false;

  // Blob URLs from local upload
  if (url.startsWith('blob:')) {
    return true;
  }

  // Check normal image extensions
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  return imageExtensions.some((ext) => url.toLowerCase().endsWith(ext));
}


  get currentLang() {
    return this.translate.currentLang;
  }
}
