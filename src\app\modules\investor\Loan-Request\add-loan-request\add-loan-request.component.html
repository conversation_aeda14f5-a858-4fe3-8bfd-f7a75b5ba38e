<div class="mb-2 flex flex-wrap items-center justify-between gap-4 lg:mb-8">
    <h2 class="text-3xl">{{ "loanRequests" | translate }}</h2>
</div>
<div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>
<div class="col-span-12">
<div class="mb-2 flex flex-wrap items-center justify-between gap-4 lg:mb-8">
    <h2 class="text-3xl">{{ "pleaseselectloantype" | translate }}</h2>
</div>

<div class="selectedcount">
    <div class="image-container dark:bg-inherit rounded-xl grid grid-cols-3 gap-4 items-center mx-4">
         @for( loantype of loantypes; track $index){

            <div class="border-2 border-gray-300 rounded-md flex flex-col items-center p-3 max-h-56"
            [ngClass]="selectedLoanItem?.loanRequesttype == loantype.loanRequesttype ? 'border-primary' : ''"
            (click)="handleSelectItem(loantype)">
                <p class="image-text my-8 font-bold dark:text-inherit text-lg">{{ loantype.title | translate }}</p>
                <h5 class="text-xs my-8 font-normal dark:text-inherit leading-6">{{ loantype.text | translate }}</h5>
            </div>


   }
</div>
<div class="col-span-12 lg:col-span-12 my-12 flex justify-end">
    <button class="btn-primary px-36 py-3 rounded-[32px] text-center text-white flex w-96 h-12 bg-[#015C8E]"
    (click)="nextStep()"> {{ 'next' | translate }}
        <img src="assets/images/arrownext.svg" class="w-8 mx-2" />
    </button>
 </div>



</div>
</div>
