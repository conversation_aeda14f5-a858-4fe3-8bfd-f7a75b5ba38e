@if (label) {
  <label [for]="name" class="inline-block mb-2 text-base font-medium">
    {{ label | translate }}
    @if (this.required) {
      <span class="text-red-500"> *</span>
    }
    <!-- @if (control.value?.length) {
      ( {{ control.value?.length }} )
    } -->
  </label>
}
<div class="relative">
  <input
    (click)="fileInput.click()"
    [placeholder]="placeholder   ? (placeholder | translate)  : label ? (label | translate) : '➕'"
    [value]="selectedFileName()"
    [disabled]="control.disabled"
    readonly
    class="centered-input"
  />

  <input
    #fileInput
    (change)="onFileSelected($event)"
    [multiple]="isMultiple"
    accept=".pdf, .jpg, .jpeg, .png"
    class="hidden"
    type="file"
  >

<!--  @if(control && control.value && control.value.fileContent){-->
<!--    <button (click)="filePreview()"-->
<!--            type="button"-->
<!--          class="absolute inset-y-0 flex end-0 items-center w-8 justify-center"-->
<!--          ngxTippy="{{  'filePreview'| translate  }}"-->
<!--  >-->
<!--    <span class="icon-[solar&#45;&#45;eye-line-duotone] size-5 text-info"></span>-->
<!--  </button>-->
<!--  }-->

</div>

@if (control?.invalid && (control?.dirty || control?.touched)) {
  <div id="username-error" class="mt-1 text-sm text-red-500">
    {{ inputHelperService.getErrorMessage(control, label) }}
  </div>
}
