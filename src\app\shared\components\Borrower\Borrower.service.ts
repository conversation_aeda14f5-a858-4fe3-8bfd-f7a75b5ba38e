import { Injectable } from '@angular/core';
import { ApiService } from '../../../core/services/api.service';

@Injectable({
  providedIn: 'root'
})
export class BorrowerService {

  constructor(    private apiService: ApiService) { }

  apiGetCommercialRegistration(CR: string, crNationalNumber: string) {
    return this.apiService.get(`/client/onboarding/commercial-registration/${CR}/${crNationalNumber}`);
  }
  // apiGetCommercialRegistration() {
  //   return this.apiService.get('/client/onboarding/commercial-registration/{CR}/{crNationalNumber}');
  // }

  apiPostCompanyDelegate(CRNumber:number,crNationalNumber:number,exp:string,type:number,DelegationFile?:File) {
    return this.apiService.post(`/client/onboarding/company-delegate?CRNumber=${CRNumber}&crNationalNumber=${crNationalNumber}&ExpiryDate=${exp}&Type=${type}`,{})
  }

}
