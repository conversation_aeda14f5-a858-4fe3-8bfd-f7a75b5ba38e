<div class="flex justify-center">
  <div
    class="max-w-md mx-auto text-center bg-white px-4 sm:px-8 py-10 rounded-xl shadow"
  >
    <header class="mb-8">
      <h1 class="text-2xl font-bold mb-4">{{ title | translate }}</h1>
    </header>
    <form [formGroup]="otpForm" class="w-full" id="otp-form">
      <div
        class="flex w-full items-center justify-center gap-3"
        formArrayName="otp"
      >
        @for(control of otpControls; track control; let i = $index){
        <input
          (focus)="handleFocus(i)"
          (input)="handleInput($event, i)"
          (keydown)="handleKeyDown($event, i)"
          (paste)="handlePaste($event)"
          [formControlName]="i"
          [id]="'otp-input-' + i"
          class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
          maxlength="1"
          type="text"
        />
        }
      </div>
      <button
        #submitButton
        (click)="verify()"
        [disabled]="loadingBar.value$ | async"
        class="w-full mt-4 inline-flex justify-center whitespace-nowrap rounded-lg bg-indigo-500 px-3.5 py-2.5 text-sm font-medium text-white shadow-sm shadow-indigo-950/10 hover:bg-indigo-600 focus:outline-none focus:ring focus:ring-indigo-300 focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 transition-colors duration-150"
        type="submit"
      >
        {{ "verifyAccount" | translate }}
      </button>
    </form>

    <div class="text-sm text-slate-500 mt-4">


      <!-- @if (countdown > 0) {
      <span class="font-medium text-indigo-500 hover:text-indigo-600">
        {{ countdown }}
      </span>
      } @else { -->
      <button
        (click)="reSend.emit()"
        class="font-medium text-indigo-500 hover:text-indigo-600"
      >
        {{ "resend" | translate }}
      </button>
      <!-- } -->
    </div>
  </div>
</div>
