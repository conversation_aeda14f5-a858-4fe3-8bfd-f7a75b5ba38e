export interface ISubAccounts{
    id: string,
    userId: string,
    type: number,
    typeName:  string
    status: number,
    statusName: string,
    riskScore: number,
    riskScoreName: string,
    nationalId: null,
    name:string,
    buildingNumber: string,
    additionalNumber: string,
    streetName:string,
    city: string,
    postCode: string,
    unitNumber: null,
    district: string,
    email: null,
    mobile: string,
    createdOn: Date,
    balance: number,
    rowVersion: string
  }
  