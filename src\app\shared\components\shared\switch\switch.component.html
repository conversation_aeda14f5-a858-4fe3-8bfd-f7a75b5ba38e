<div class="flex items-center justify-center">
  <label [for]="label" class="flex items-center cursor-pointer">
    <div class="relative">
      <input type="checkbox" [id]="label" [(ngModel)]="isChecked" :checked="isChecked" class="sr-only" />
      <div class="block w-14 h-8 rounded-full" [ngClass]="{ 'bg-primary': isChecked, 'bg-primary/20': !isChecked }"></div>
      <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition" [ngClass]="{ 'translate-x-full': isChecked }"></div>
    </div>
  </label>
</div>
