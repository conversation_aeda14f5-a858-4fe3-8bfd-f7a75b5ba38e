export interface IProfileIndividual {  
    "annualIncomeRange": number,
    "investmentExperience": number,
    "riskTolerance": number,
    "jobInformation": number,
    "isBoardMember": boolean,
    "isPoliticallyExposedPerson": boolean,
    "isAssociatedWithPEP": boolean,
    "isRealBeneficiary": boolean,
    "jobTitle": string,
    "companyName": string,
    "companyAddress": string
}

export interface IProfileIndividualResponse {
    "userAccountId": string,
    "annualIncomeRange": number,
    "investmentExperience": number,
    "riskTolerance": number,
    "jobInformation": number,
    "isBoardMember": boolean,
    "isPoliticallyExposedPerson": boolean,
    "isAssociatedWithPEP": boolean,
    "isRealBeneficiary": boolean,
    "jobTitle": string,
    "companyName": string,
    "companyAddress": string
  }