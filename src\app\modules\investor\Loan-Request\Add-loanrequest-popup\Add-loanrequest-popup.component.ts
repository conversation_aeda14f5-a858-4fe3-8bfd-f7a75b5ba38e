
import { Component, Input, Output, EventEmitter, OnInit, OnChanges, inject, signal } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { EncryptStorage } from 'encrypt-storage';
import { Router } from '@angular/router';
import { ModalService } from '@component/form/modals/modal.service';
import { ToastService } from '../../../../core/services/toast.service';
import { AuthService } from '../../../auth/services/auth.service';
import { LoanRequestService } from '../Service/loan-request.service';
import { DepositMethodComponent } from '@component/deposit-method/deposit-method.component';
import { MDModalModule } from '@component/form/modals';

interface IPaymentDetails {
  amount: number;
  isPaid: boolean;
}

@Component({
  selector: 'app-add-loanrequest-popup',
    standalone: true,
  imports: [
  TranslateModule,
    MDModalModule,
    DepositMethodComponent
  ],
  templateUrl: './Add-loanrequest-popup.component.html',
  styleUrls: ['./Add-loanrequest-popup.component.scss']
})
export class AddLoanrequestPopupComponent implements OnInit, OnChanges {
  @Input() id = 'loan-request-popup';
  @Input() reqId!: string;
  @Output() closePopup = new EventEmitter<boolean>();

  paymentDetails = signal<IPaymentDetails | undefined>(undefined);
  showDepositDiv = false;

  // Services
  #loanService = inject(LoanRequestService);
  #translateService = inject(TranslateService);
  #toast = inject(ToastService);
  modalService = inject(ModalService);
  authService = inject(AuthService);
  #router = inject(Router);
  #encryptTokenStorage = new EncryptStorage('MDD_Fintech_is_userToken');

  // Profile data
  profileData: any;
  BalanceCount: any;

  constructor() {
    this.getProfileFromStorageOnly();
  }

  ngOnInit(): void {
    this.getBalance();
  }

  ngOnChanges(): void {
    this.handleGetPaymentDetails();
  }

  async handleGetPaymentDetails() {
    if (this.reqId) {
      try {
        const res: any = await this.#loanService.getPaymentRequest(this.reqId);
        if (res.data) {
          this.paymentDetails.set(res.data as IPaymentDetails);
        }
      } catch (error: any) {
        this.#toast.error(
          this.#translateService.currentLang === 'ar'
            ? error.error.result['arabicMessage']
            : error.error.result['englishMessage']
        );
      }
    }
  }

  async getBalance() {
    try {
      this.BalanceCount = await this.authService.getBalance();
      this.authService.userBalance.set(this.BalanceCount);
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  }

  toggleDepositDiv() {
    this.showDepositDiv = !this.showDepositDiv;
  }

  async handlePayment() {
    if (this.paymentDetails() && this.reqId) {
      try {
        const res: any = await this.#loanService.updatePaymentRequest(this.reqId, {});
        this.modalService.close('loan-request-popup');
        this.closePopup.emit(true);

        this.#toast.success(this.#translateService.instant("paymentsucessfully"))


        if (res.data) {
          this.paymentDetails.set(res.data as IPaymentDetails);
        }
        this.#router.navigate(['/loanRequest']);
        window.location.reload()
      } catch (error: any) {
        this.#toast.error(
          this.#translateService.currentLang === 'ar'
            ? error.error.result['arabicMessage']
            : error.error.result['englishMessage']
        );
      }
    }
  }

  getProfileFromStorageOnly() {
    const profile = this.authService.getCachedProfile();
    if (profile) {
      this.profileData = profile;
      this.authService.userBalance.set(profile?.balance || '');
    } else {
      console.warn('No profile in localStorage');
    }
  }

  onClose() {
    this.modalService.close('loan-request-popup');
    this.closePopup.emit(true);
  }
}
