<div class="col-span-12">
  <form>
    <div class="box col-span-12 lg:col-span-6">
      <div
        class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
      >
        <h6 class="mb-0">
          {{ "approximateAnnualIncome" | translate }}
        </h6>
      </div>
      @for(annualIncome of AnnualIncomeRange | enum; track annualIncome; let i =
      $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="annualIncome{{ i }}" class="ml-2">
            <input
              id="annualIncome{{ i }}"
              name="annualIncome"
              class="radio_animated"
              type="radio"
              [value]="annualIncome[0]"
              (change)="model.annualIncomeRange = annualIncome[0]"
              [checked]="model.annualIncomeRange === annualIncome[0]"
              required
            />
            {{ getLabel(annualIncome[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <div
        class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
      >
        <h6 class="mb-0">
          {{ "investmentKnowledgeAndExperience" | translate }}
        </h6>
      </div>
      @for(investmentExperience of InvestmentExperience | enum; track
      investmentExperience; let i = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="investmentExperience{{ i }}" class="ml-2">
            <input
              id="investmentExperience{{ i }}"
              name="investmentExperience"
              class="radio_animated"
              type="radio"
              [value]="investmentExperience[0]"
              (change)="model.investmentExperience = investmentExperience[0]"
              [checked]="model.investmentExperience === investmentExperience[0]"
              required
            />
            {{ getLabel(investmentExperience[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <div
        class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
      >
        <h6 class="mb-0">
          {{ "clientDesireToTakeRisks" | translate }}
        </h6>
      </div>
      @for(riskTolerance of RiskTolerance | enum; track riskTolerance; let i =
      $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="riskTolerance{{ i }}" class="ml-2">
            <input
              id="riskTolerance{{ i }}"
              name="riskTolerance"
              class="radio_animated"
              type="radio"
              [value]="riskTolerance[0]"
              (change)="model.riskTolerance = riskTolerance[0]"
              [checked]="model.riskTolerance === riskTolerance[0]"
              required
            />
            {{ getLabel(riskTolerance[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div
      class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
    ></div>

    <div class="box col-span-12 lg:col-span-6">
      <h4>{{ "workInformation" | translate }}</h4>
      <p class="mt-2">{{ "answerAllTheFollowingQuestions" | translate }}</p>
      <h6 class="mb-0 mt-2">
        {{ "employmentStatus" | translate }}
      </h6>
      <div class="bb-dashed mb-4 flex flex-wrap items-center gap-4 pb-4"></div>
      @for(jobInformation of JobInformation | enum; track jobInformation; let i
      = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="jobInformation{{ i }}" class="ml-2">
            <input
              id="jobInformation{{ i }}"
              name="jobInformation"
              class="radio_animated"
              type="radio"
              [value]="jobInformation[0]"
              (change)="model.jobInformation = jobInformation[0]"
              [checked]="model.jobInformation === jobInformation[0]"
              required
            />
            {{ getLabel(jobInformation[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <div class="col-span-2 md:col-span-1">
        <app-input
          [control]="financailInfoForm.get('jobTitle')"
          [label]="'jobTitle'"
          (change)="model.jobTitle = financailInfoForm.get('jobTitle')?.value"
          placeholder="{{ 'enterTheJobTitle' | translate }}"
        />
      </div>
      <div class="col-span-2 md:col-span-1">
        <app-input
          [label]="'companyName'"
          [control]="financailInfoForm.get('companyName')"
          (change)="
            model.companyName = financailInfoForm.get('companyName')?.value
          "
          placeholder="{{ 'enterTheCompanyName' | translate }}"
        />
      </div>
      <div class="col-span-2 md:col-span-1">
        <app-input
          [label]="'companyAddress'"
          (change)="
            model.companyAddress =
              financailInfoForm.get('companyAddress')?.value
          "
          [control]="financailInfoForm.get('companyAddress')"
          placeholder="{{ 'enterTheCompanyAddress' | translate }}"
        />
      </div>
    </div>

    <div
      class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
    ></div>
    <div class="box col-span-12 lg:col-span-6">
      <h4>{{ "generalInformation" | translate }}</h4>
      <div
        class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
      ></div>
      <h6 class="mb-0">
        {{ "generalInformation1" | translate }}
      </h6>
      @for(yesOrNo of YesOrNo | enum; track yesOrNo; let i = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="generalInformation1{{ i }}" class="ml-2">
            <input
              id="generalInformation1{{ i }}"
              name="generalInformation1"
              class="radio_animated"
              type="radio"
              [value]="yesOrNo[0]"
              (change)="model.isBoardMember = yesOrNo[1] === 'yes'"
              [checked]="model.isBoardMember === (yesOrNo[1] === 'yes')"
            />
            {{ getLabel(yesOrNo[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <h6 class="mb-0">
        {{ "generalInformation2" | translate }}
      </h6>
      @for(yesOrNo of YesOrNo | enum; track yesOrNo; let i = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="generalInformation2{{ i }}" class="ml-2">
            <input
              id="generalInformation2{{ i }}"
              name="generalInformation2"
              class="radio_animated"
              type="radio"
              [value]="yesOrNo[0]"
              (change)="model.isPoliticallyExposedPerson = yesOrNo[1] === 'yes'"
              [checked]="
                model.isPoliticallyExposedPerson === (yesOrNo[1] === 'yes')
              "
            />
            {{ getLabel(yesOrNo[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <h6 class="mb-0">
        {{ "generalInformation3" | translate }}
      </h6>
      @for(yesOrNo of YesOrNo | enum; track yesOrNo; let i = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="generalInformation3{{ i }}" class="ml-2">
            <input
              id="generalInformation3{{ i }}"
              name="generalInformation3"
              class="radio_animated"
              type="radio"
              [value]="yesOrNo[0]"
              (change)="model.isAssociatedWithPEP = yesOrNo[1] === 'yes'"
              [checked]="model.isAssociatedWithPEP === (yesOrNo[1] === 'yes')"
            />
            {{ getLabel(yesOrNo[1]) }}
          </label>
        </div>
      </div>
      }
    </div>

    <div class="box col-span-12 lg:col-span-6">
      <h6 class="mb-0">
        {{ "generalInformation4" | translate }}
      </h6>
      @for(yesOrNo of YesOrNo | enum; track yesOrNo; let i = $index){
      <div class="flex flex-column gap-3">
        <div class="field-checkbox">
          <label for="generalInformation4{{ i }}" class="ml-2">
            <input
              id="generalInformation4{{ i }}"
              name="generalInformation4"
              class="radio_animated"
              type="radio"
              [value]="yesOrNo[0]"
              (change)="model.isRealBeneficiary = yesOrNo[1] === 'yes'"
              [checked]="model.isRealBeneficiary === (yesOrNo[1] === 'yes')"
            />
            {{ getLabel(yesOrNo[1]) }}
          </label>
        </div>
      </div>
      }
    </div>
    <div class="mt-8 flex gap-12">
      <app-button
        [disabled]="isUpdating || disableSubmit()"
        (onClick)="create()"
        [text]="'Submit'"
      ></app-button>
    </div>
  </form>
</div>
