/* Container for each image and text */
.image-container {
    display: flex;
    flex-direction: column; /* Stack image and text vertically */
    align-items: center; /* Center align image and text */
    margin: 10px; /* Add spacing between columns */
    flex: 1 1 calc(33.33% - 20px); /* Ensure 3 items per row */
    box-sizing: border-box;
    height: 345px;

  }

  /* Image styling */
  .image {
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  /* Text styling */
  .image-text {
    margin-top: 10px; /* Add space between image and text */
    font-size: 14px; /* Adjust font size */
    color: #333; /* Optional: Set text color */
    text-align: center;
  }

  /* Flex container for the row */
  .selectedcount .flex {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping to the next row if needed */
    justify-content: space-between; /* Add spacing between items */
  }
