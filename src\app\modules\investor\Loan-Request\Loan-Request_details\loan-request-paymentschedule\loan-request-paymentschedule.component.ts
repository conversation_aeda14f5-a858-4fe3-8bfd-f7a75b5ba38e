import { Component, Input, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../../core/services/toast.service';
import { AuthService } from '../../../../auth/services/auth.service';
import { LoanRequestService } from '../../Service/loan-request.service';
import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { CalendarModule } from 'primeng/calendar';
import { DateFormatPipe } from '../../../../../shared/pipe/date-format.pipe';
import { ModalService } from '@component/form/modals/modal.service';
import { TopBannerComponent } from '@component/shared/top-banner/top-banner.component';
import { PaymentType } from '../../../../../shared/enums/payment.enum';
import { DepositCreateComponent } from '../../../deposits/deposit-create/deposit-create.component';
import { DepositRequestSearch } from '../../../deposits/models/deposit.model';
import { DepositService } from '../../../deposits/services/deposit.service';

@Component({
  selector: 'app-loan-request-paymentschedule',
  standalone: true,
 imports: [
    DataTableModule,
    TranslateModule,
    DateFormatPipe,
    CommonModule, 
    DecimalPipe,
 
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './loan-request-paymentschedule.component.html',
  styleUrl: './loan-request-paymentschedule.component.css'
})
export class LoanRequestPaymentscheduleComponent {
 @Input() loanRequestId!: string;
 columns: Array<colDef> = [];
   search: DepositRequestSearch = {isServerMode: true} as DepositRequestSearch;
   // deposits: DepositRequestModel[] = [];
   deposits=signal<any[]>([])
   constructor(
     public translate: TranslateService,
     protected depositService: DepositService,
     private router: Router,
     private modalService: ModalService,
   ) {
     this.depositService.initPagination();
   }
 
   async handePageChange(currentPage: number) {
     this.search.page = currentPage;
     await this.getList();
   }
 
 
   async handleSearch(e: any) {
     this.search = {find: (e.target as HTMLInputElement).value, isServerMode: false} as DepositRequestSearch;
     this.deposits()
     this.depositService.initPagination();
     await this.getList();
     this.search.isServerMode = true;
   }
 
   async ngOnInit() {
     this.columns = [
       {title: this.translate.instant('Installmentstobepaid'), field: 'transactionNo',},
       {title: this.translate.instant('allwoncebalance'), field: 'transactionDate',},
       {title: this.translate.instant('status'), field: 'amount',},
       {title: this.translate.instant('payment'), field: 'payment',},
    
     ]
     await this.getList();
   }
 
   async getList() {
     try {
       this.search.paymentType = PaymentType.Deposit;
       this.deposits.set(await this.depositService.getList(this.search) ?? [])
    
     } catch (e: any) {
       console.log(e);
     }
   }
 
   openCreateDeposit() {
     this.modalService.open('deposit-create')
   }
 }
 