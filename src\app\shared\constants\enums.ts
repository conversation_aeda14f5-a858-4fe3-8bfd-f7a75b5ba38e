export enum PaymentType {
  Deposit = 1,
  Withdraw = 2,
}

export enum PaymentStatus {
  Waiting = 1,
  Approved = 2,
  WithdrawPaid = 3,
  WithdrawRejected = 4,
  DepositRejected = 5,
}


export enum TransactionType {
  Deposit = 1,
  Withdraw = 2,
  Invest = 3,
  PayBack = 4,
  PayProfits = 5,
  TransferFromWallet = 6,
  TransferToWallet = 7,
}

export enum TransactionStatus {
  Waiting = 1,
  Approved = 2,
}

export enum OpportunityStatus {
  Open = 1,
  Progress = 2,
  Completed = 3,
  Expired = 4,
  Waiting = 5,
  Cancelled = 6,
}

export enum InvestmentStatus {
  Open = 1,
  Approved = 2,
  Closed = 3,
  Cancelled = 4,
}

export enum UserType {
  User = 1,
  Investor = 2,
  CompanyUser = 3,
  SuperAdmin = 4,
}

export enum UserStatus {
  NafathVerfication = 0,
  Active = 1,
  Band = 2,
  NoActive = 3,
}

export enum CompanyStatus {
  Active = 1,
  Inactive = 2,
  Blocked = 3,
}

export enum OperationNumber {
  Invest = 2,
  RequestWithdraw = 3,
  RequestDeposit = 4,
  ApproveDeposits = 5,
  ApproveWithDraw = 6,
  ChangePassword = 7,
  ChangeBankAccount = 8,
  PayWithDraw = 11,
  CreateApprovedWithdraw = 12,
  ResetPassword = 15,
  EnableFaceID = 16,
  CloseOpportunity = 17,
  CancelOpportunity = 18,
  CloseInvestment = 19,
  CancelInvestment = 20,
  CreateWalletTransfer = 21,
  ResetEmail = 22,
  ResetMobile = 24,
  CreatePaidWithdraw = 26,
  registerEmail = 27,
  registerMobile = 28,
  upgradeInvestor = 29,
  QualificationApproveReject = 30,
}

export enum HttpStatusCodes {
  INTERNET_CONNECTION = 0,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  NO_BALANCE = 402,
  FORBIDDEN = 403, // & INVALID_MFA_CODE
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
}

export enum PlatformTypes {
  WebBrowser = 0,
  IOS = 1,
  Android = 2,
}

export enum SettingCategory {
  GeneralSettings = 1,
  EmailSettings = 2,
  SMSSettings = 3,
  OperationsSettings = 4,
}

export enum DataType {
  Number = 1,
  String = 2,
  Boolean = 3,
}

export enum AnnualIncomeRange {
  minimumThan72000 = 0,
  from72000To120000 = 1,
  from120000To180000 = 2,
  from180000To300000 = 3,
  moreThan300000 = 4,
}

export enum InvestmentExperience {
  low = 0,
  medium = 1,
  high = 2,
}

export enum RiskTolerance {
  low = 0,
  medium = 1,
  high = 2,
}

export enum JobInformation {
  governmentEmployee = 0,
  privateSectorEmployee = 1,
  retired = 2,
  freelancer = 3,
  student = 4,
  unemployed = 5,
}

export enum QualificationStatus {
  waiting = 0,
  rejected = 1,
  approved = 2,
}

export enum QualificationReason {
  condition_one = 0,
  condition_two = 1,
  condition_three = 2,
}

export enum YesOrNo {
  yes = "yes",
  no = "no",
}
