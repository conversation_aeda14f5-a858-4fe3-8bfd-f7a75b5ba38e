<div class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header box mb-4 xxxl:mb-6">
      <h4>{{ "Legal Documents" | translate }}</h4>
    </div>
    <div class="w-full border-t border-dashed border-gray-400 mb-4"></div>
    <div class="w-full max-w-3xl mx-auto mt-10 grid gap-4">
      <div
        *ngFor="let item of accordionItems; let i = index"
        class="box bg-primary/5 dark:bg-bg3 border border-n30 dark:border-n500 rounded-xl transition-shadow hover:shadow-md overflow-hidden"
      >
        <button
          class="w-full text-left px-4 md:px-6 py-4 font-semibold text-primary flex justify-between items-center"
          (click)="toggleSection(i)"
        >
          <span class="text-sm">{{ item.title }}</span>
          <i
            class="transition-transform duration-300 text-base"
            [ngClass]="{
              'rotate-180': activeIndex === i
            }"
          >
            ▼
          </i>
        </button>

        <div
        
          class="accordion-content px-4 md:px-6 pb-4 text-sm text-gray-700 dark:text-gray-300 transition-all duration-300 ease-in-out"
          [ngClass]="{
            'hidden': activeIndex !== i
          }"
          [innerHTML]="item.content"
        >
          {{ item.content }}
        </div>
      </div>
    </div>
    <div class="flex justify-end mt-4">
      <button
        type="submit"
        class="btn btn-primary pull-right me-1 rounded"
        style="border-radius: 9999px !important;"
        (click)="acceptAllDocumentsSequentially()">
        {{ "Accept" | translate }}
      </button>
    </div>
  </div>
</div>
