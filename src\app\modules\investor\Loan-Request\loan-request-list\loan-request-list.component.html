
  @if (LoanRequestService.pagination.loading) {
  <!-- Show spinner while loading -->
 <div class="grid justify-center items-center m-[20vh]">
    <div class="inline-block h-14 w-14 animate-spin my-12 rounded-full border-8 border-solid border-primary border-r-transparent">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
}
  
  @else if(this.LoanRequest().length>0){
    <app-top-banner buttonText="createLoan" title="loanRequests" (onClick)="openCreateloanRequest()"/>


<div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-3 lg:mb-4 lg:pb-4">
</div>
  <div class="box col-span-12 lg:col-span-6">
    <div class="bb-dashed mb-4 flex flex-wrap items-center justify-end gap-4 pb-4 lg:mb-6 lg:pb-6">
    
      <div class="flex flex-wrap md:flex-nowrap  justify-end gap-4">
        <div
          class="bg-primary/5 dark:bg-bg3 border border-n30 dark:border-n500 flex gap-3 rounded-[30px] focus-within:border-primary p-1 items-center justify-between min-w-[200px] xxl:max-w-[319px] w-full">
          <input (change)="handleSearch($event)"
                 #searchValue
                 class="bg-transparent border-none text-sm ltr:pl-4 rtl:pr-4 py-1 w-full" placeholder="Search"
                 type="text"/>
          <button

            (click)="searchValue.click()"
            class="bg-primary shrink-0 rounded-full w-7 h-7 lg:w-8 lg:h-8 flex justify-center items-center text-n0">
            <i class="las la-search text-lg"></i>
          </button>
        </div>
        <div class="flex items-center gap-3 whitespace-nowrap">
          <span>{{ 'sortBy' | translate }} : </span>
          <app-dropdown/>
        </div>
      </div>
    </div>
    <div class="mt-5 overflow-x-auto">
     
        <div class="datatable">
   

          <ng-datatable
            (changeServer)="handePageChange($event.current_page)"
            [columns]="columns"
            [sortColumn]="'requestDate'"
            [sortDirection]="'desc'"
            [loading]="LoanRequestService.pagination.loading"
            [page]="LoanRequestService.pagination.page"
            [rows]="LoanRequest()"
            [showPageSize]="false"
            [pageSize]="15"
            [pagination]="true"
            [pageSizeOptions]="[15]"
            [totalRows]="LoanRequestService.pagination.totalRows"
            class="whitespace-nowrap table-hover"
            firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
            nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
            paginationInfo=" {{ 'pagination_info' | translate:{ start: '{0}', end: '{1}', total: '{2}' } }}"
            previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
          >
            <ng-template let-value="data" slot="loanRequestNo">
              <span (click)="Update(value.id)" class="cursor-pointer">
                {{ value.loanRequestNo }}
              </span>
          
            </ng-template>
            <ng-template let-value="data" slot="requestDate">
              <span (click)="Update(value.id)" class="cursor-pointer">
                {{ value.requestDate | dateFormat }}
              </span>
          
            </ng-template>
            <ng-template let-value="data" slot="requestedAmount">
                 <span (click)="Update(value.id)" class="cursor-pointer">
                      {{ value.requestedAmount | number }}
                 </span>
             
            </ng-template>
            <ng-template let-value="data" slot="periodInDays">
                <span (click)="Update(value.id) " class="cursor-pointer">
                         {{ value.periodInDays | number }}
                </span>
             
            </ng-template>
             <ng-template let-value="data" slot="requestStatusName">

       <span (click)="Update(value.id) " class="cursor-pointer">
            
          <span [ngClass]="{
            'bg-gray-200 border-gray-400 text-gray-700 dark:bg-gray-800 dark:border-gray-900 dark:text-gray-300': value.requestStatus == '0', 
            'bg-yellow-200 border-yellow-400 text-yellow-700 dark:bg-yellow-800 dark:border-yellow-900 dark:text-yellow-300': value.requestStatus == 1,
            'bg-green-200 border-green-400 text-green-700 dark:bg-green-800 dark:border-green-900 dark:text-green-400': value.requestStatus == 4,
            'bg-orange-200 border-orange-400 text-orange-700  dark:bg-orange-800  dark:border-orange-900 dark:text-orange-400': value.requestStatus == 2,
            'bg-red-200 border-red-400 text-red-700 dark:bg-red-800 dark:border-red-900 dark:text-red-400': value.requestStatus == 5,
            'bg-orange-200 border-orange-400 text-orange-700 dark:bg-orange-800 dark:border-orange-900 dark:text-orange-300': value.requestStatus == 2, 
            'bg-blue-200 border-blue-400 text-blue-700 dark:bg-blue-800 dark:border-blue-900 dark:text-blue-300': value.requestStatus == 3, 
            'bg-green-200 border-green-400 text-green-700 dark:bg-green-800 dark:border-green-900 dark:text-green-300': value.requestStatus == 4, 
            'bg-red-200 border-red-400 text-red-700 dark:bg-red-800 dark:border-red-900 dark:text-red-300': value.requestStatus == 5,
            'bg-teal-200 border-teal-400 text-teal-700 dark:bg-teal-800 dark:border-teal-900 dark:text-teal-300': value.requestStatus == 6, 
            'bg-indigo-200 border-indigo-400 text-indigo-700 dark:bg-indigo-800 dark:border-indigo-900 dark:text-indigo-300': value.requestStatus == 7, 
            'bg-cyan-200 border-cyan-400 text-cyan-700 dark:bg-cyan-800 dark:border-cyan-900 dark:text-cyan-300': value.requestStatus == 8, 
            'bg-rose-200 border-rose-400 text-rose-700 dark:bg-rose-800 dark:border-rose-900 dark:text-rose-300': value.requestStatus == 9, 
            'bg-lime-200 border-lime-400 text-lime-700 dark:bg-lime-800 dark:border-lime-900 dark:text-lime-300': value.requestStatus == 10

         }"
            class="block w-28 rounded-[30px] border text-wrap px-4 border-n30  py-2 text-center text-xs  dark:border-n500 dark:bg-bg3 xxl:w-26">
            {{ value.requestStatusName | translate }}
          </span>
       </span>
        </ng-template>
           
            <ng-template let-value="data" slot="requestTypeName">
                     <span (click)="Update(value.id) " class="cursor-pointer">
                  <span
                
                    [ngClass]="value.requestStatusName"
                    class="block w-28 rounded-[30px] border border-n30 bg-primary/10 py-2 text-center text-xs text-primary dark:border-n500 dark:bg-bg3 xxl:w-36"
                  >
                    {{ value.requestTypeName | translate }}
                  </span>
                  </span>
            </ng-template>
         
            <ng-template let-value="data" slot="actions" >
              @if (value.requestStatusName =='Draft') {
              <button  (click)="handleSelectItem(value)"
                  class="bg-primary rounded-3xl block px-11 py-2   text-n0">
                 {{"Edit" |translate}}
              </button>
            }
              @if (value.requestStatusName =='PaymentPending') {
              <button  (click)="PaymentNow(value.id)"
                  class="bg-primary rounded-3xl block px-11 py-2   text-n0">
                 {{"PayNow" |translate}}
              </button>
            }
            </ng-template>
         
          
          </ng-datatable>
        </div>
      
     
    </div>
  </div>
}@else if (this.LoanRequest().length===0) {
  <div class="grid justify-center items-center m-[20vh]">
    <h2 class="block center">{{"NoloanRequests"| translate }}</h2>
    <button (click)="nextstep()"
    class="bg-primary rounded-3xl my-8 block px-11 py-2   text-n0">
 {{"RequestLoanRequests" |translate}}
  </button>
  </div>
}


@if (modalService.isOpen('loan-request-popup')) {
  <app-add-loanrequest-popup
    [reqId]="reqId"
    (closePopup)="handleClosePopup($event)"
  />
}