import {Component, inject} from '@angular/core';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {InputComponent} from "@component/form/input/input.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {passwordValidator} from "../../../shared/validators/password.validator";
import {confirmPasswordValidator} from "../../../shared/validators/confirm-password.validator";

import {ModalService} from "@component/form/modals/modal.service";

import {AuthService} from "../services/auth.service";
import {OperationType} from "../../../core/enums/operation-type.enum";
import { OtpService } from '../../../shared/otp/services/otp.service';
import { OtpModalComponent } from '../../../shared/otp/otp-modal/otp-modal.component';
import { Router, RouterOutlet } from '@angular/router';
import { EncryptStorage } from 'encrypt-storage';
import { Store } from '@ngrx/store';
import { LayoutState } from '@store/reducer';
import { Observable } from 'rxjs';
import { ToastService } from '../../../core/services/toast.service';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-change-password',
  standalone: true,
  imports: [
    FormsModule,
    InputComponent,
    TranslateModule,
    OtpModalComponent,
    ReactiveFormsModule,
    NgIf
   
  ],
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.css'
})
export class ChangePasswordComponent {
  isPasswordHidden = true;
  changePasswordForm!: FormGroup
  encryptStorage = new  EncryptStorage('User_info_login');
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  changePasswordData:any
  constructor(
    private fb: FormBuilder,
    protected modalService: ModalService,
    private otpService: OtpService,
    private authService: AuthService,
     private router: Router,
      private toast: ToastService,
         private translate: TranslateService,
  ) {
     this.layout$ = this.store.select('layout')
  }
   private store = inject(Store)
    colorMode = ''
    layout$: Observable<LayoutState>
 
 

  ngOnInit() {
     this.layout$.subscribe((theme) => {
        this.colorMode = theme.theme
      })
    
    this.changePasswordForm = this.fb.group({
      currentPassword: [null, [Validators.required]],
      newPassword: [null, [Validators.required, passwordValidator()]],
      confirmPassword: [null, [Validators.required]],
    }, {
      validators: confirmPasswordValidator('newPassword', 'confirmPassword')
    })
  }


passwordVisibility = {
  currentPassword: true,
  newPassword: true,
  confirmPassword: true
};

// Update the toggle function to accept a field name
togglePasswordVisibility(field: 'currentPassword' | 'newPassword' | 'confirmPassword') {
  this.passwordVisibility[field] = !this.passwordVisibility[field];
}
  // async verifyChangePassword() {
   
  //   if (this.changePasswordForm.valid) {
  //     try {
  //       await this.otpService.send({operationType: OperationType.Change_Password})
  //       this.modalService.open('otp-change-password-modal')
  //     } catch (e: any) {
  //     }
  //   } else {
  //   }
  // }

  verifyChangePassword() {
  if (this.changePasswordForm.valid) {
    this.otpService.send({ operationType: OperationType.Change_Password }).then(() => {
      this.modalService.open('otp-change-password-modal');
    });
  } else {
    this.changePasswordForm.markAllAsTouched();
  }
}


  async changePassword(otp: any) {
    try {
      const body = {
        currentPassword: this.changePasswordForm.value.currentPassword,
        newPassword: this.changePasswordForm.value.newPassword,
        mfaCode: otp
      }
    this.changePasswordData = await this.authService.changePassword(body)
      this.modalService.close('otp-change-password-modal')     
          this.toast.success(this.translate.instant("Change Password Successfully"))
          this.router.navigate(['/auth'])
    } catch (e: any){ 
      
          this.toast.error(e.error.arabicMessage)
          this.modalService.close('otp-change-password-modal')
    }
  }
     goBack() {
           this.#encryptTokenStorage.removeItem('token');
  this.#encryptTokenStorage.removeItem('tokenExpiry');
  this.encryptStorage.removeItem('userInfo');
   this.router.navigate(['/auth'])
  }
}
