export  enum IncomeRange {
     Less_Than_72K =0,
      Between_72_And_120 =1,
       Between_120_And_180=2, 
       Between_180_And_240=3,
        Between_240_And_300=4,
         More_Than_300K=5
         }
export  enum InvestmentExperience { 
    Low =0,
     Medium =1,
      High=2 
    }
export enum RiskTolerance { 
    Low=0, 
    Medium=1,
     High=2 }
export enum JobInformation {
     GovernmentEmployee =0,
      PrivateSectorEmployee =1,
       Retired=2, 
       BusinessOwner=3, 
       Student=4,
        NotWorking=5 }

       export enum LookupTypeKYC
    {
        EducationLevel = 1,
        AnnualIncomeRange = 2,
        SourceOfIncome = 3,
        TotalAssetsRange = 4,
        Nationalities = 5,
    }