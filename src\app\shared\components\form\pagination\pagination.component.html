<pagination-template #p="paginationApi"
  (pageChange)="config.currentPage = $event"
  [id]="config.id!">
  <div class="datatable">
    <div class="bh-datatable bh-antialiased bh-relative bh-text-black bh-text-sm bh-font-normal">
      <div class="bh-pagination  pt-3">
        <div class="bh-flex bh-items-center bh-flex-wrap bh-flex-col sm:bh-flex-row bh-gap-4">
          <div class="bh-pagination-info bh-flex bh-items-center">
            <span class="bh-mr-2"> <ng-content></ng-content> </span>
          </div>
          @if (p.pages.length > 1) {
            <div class="bh-pagination-number sm:bh-ml-auto bh-inline-flex bh-items-center bh-space-x-1">
              <button (click)="p.previous()" [class.disabled]="p.isFirstPage()" class="bh-page-item previous-page"
                type="button">
                <svg class="w-4.5 h-4.5  rtl:hidden" fill="none" height="24" viewBox="0 0 24 24"
                  width="24" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M15 5L9 12L15 19" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
                </svg>
                <svg class="w-4.5 h-4.5 ltr:hidden" fill="none" height="24" viewBox="0 0 24 24" width="24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
                </svg>
              </button>
              <ng-container>
                @for (page of p.pages; track page) {
                  <button
                    (click)="p.setCurrent(page.value)"
                    [class.bh-active]="p.getCurrent() === page.value"
                    class="bh-page-item"
                    type="button"
                    >
                    {{ page.label }}
                  </button>
                }
              </ng-container>
              <button (click)="p.next()" [class.disabled]="p.isLastPage()" class="bh-page-item next-page"
                type="button">
                <svg class="w-4.5 h-4.5  ltr:hidden" fill="none" height="24" viewBox="0 0 24 24"
                  width="24" xmlns="http://www.w3.org/2000/svg">
                  <path
                    class=""
                    d="M15 5L9 12L15 19" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
                </svg>
                <svg class="w-4.5 h-4.5 rtl:hidden" fill="none" height="24" viewBox="0 0 24 24" width="24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
                </svg>
              </button>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
</pagination-template>
