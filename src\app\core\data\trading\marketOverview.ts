import { TransactionStatus } from "@data/dashboards/style2Transactions";

export const marketOverviewData = [
  {
    id: 1,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 475.22,
    charge: 11,
    status: TransactionStatus.Successful,
    change: 3,
    title: 'EUR/USD',
    process: 20
  },
  {
    id: 2,
    icon1: 'assets/images/usa-sm.png',
    icon2: 'assets/images/jp-sm.png',
    amount: 785.22,
    charge: 21,
    status: TransactionStatus.Cancelled,
    change: 25,
    title: 'USD/JPY',
    process: 70
  },
  {
    id: 3,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 255.22,
    charge: 21,
    status: TransactionStatus.Pending,
    change: -4,
    title: 'EUR/USD',
    process: 45
  },
  {
    id: 4,
    icon1: 'assets/images/uk-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 448.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: -1,
    title: 'GBP/USD',
    process: 80
  },
  {
    id: 5,
    icon1: 'assets/images/usa-sm.png',
    icon2: 'assets/images/rs-sm.png',
    amount: 456.22,
    charge: 21,
    status: TransactionStatus.Pending,
    change: 10,
    title: 'USD/RSA',
    process: 90
  },
  {
    id: 6,
    icon1: 'assets/images/rs-sm.png',
    icon2: 'assets/images/euro-sm.png',
    amount: 365.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: -4,
    title: 'RSA/EUR',
    process: 35
  },
  {
    id: 7,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/uk-sm.png',
    amount: 425.22,
    charge: 21,
    status: TransactionStatus.Cancelled,
    change: -5,
    title: 'EUR/GBP',
    process: 75
  },
  {
    id: 8,
    icon2: 'assets/images/euro-sm.png',
    icon1: 'assets/images/usa-sm.png',
    amount: 775.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: 18,
    title: 'USD/EUR',
    process: 85
  },
  {
    id: 9,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/rs-sm.png',
    amount: 555.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: -12,
    title: 'EUR/RSA',
    process: 65
  },
  {
    id: 10,
    icon2: 'assets/images/euro-sm.png',
    icon1: 'assets/images/uk-sm.png',
    amount: 875.22,
    charge: 7,
    status: TransactionStatus.Successful,
    change: 5,
    title: 'USD/EUR',
    process: 49
  },
  {
    id: 11,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 335.22,
    charge: 11,
    status: TransactionStatus.Successful,
    change: 3,
    title: 'EUR/USD',
    process: 20
  },
  {
    id: 12,
    icon1: 'assets/images/usa-sm.png',
    icon2: 'assets/images/jp-sm.png',
    amount: 225.22,
    charge: 21,
    status: TransactionStatus.Cancelled,
    change: 25,
    title: 'USD/JPY',
    process: 70
  },
  {
    id: 13,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 475.22,
    charge: 21,
    status: TransactionStatus.Pending,
    change: -4,
    title: 'EUR/USD',
    process: 45
  },
  {
    id: 14,
    icon1: 'assets/images/uk-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 875.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: -1,
    title: 'GBP/USD',
    process: 80
  },
  {
    id: 15,
    icon1: 'assets/images/usa-sm.png',
    icon2: 'assets/images/rs-sm.png',
    amount: 415.22,
    charge: 21,
    status: TransactionStatus.Pending,
    change: 10,
    title: 'USD/RSA',
    process: 90
  },
  {
    id: 16,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/rs-sm.png',
    amount: 845.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: -4,
    title: 'EUR/RSA',
    process: 35
  },
  {
    id: 17,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/uk-sm.png',
    amount: 955.22,
    charge: 21,
    status: TransactionStatus.Cancelled,
    change: -5,
    title: 'EUR/GBP',
    process: 75
  },
  {
    id: 18,
    icon1: 'assets/images/euro-sm.png',
    icon2: 'assets/images/usa-sm.png',
    amount: 615.22,
    charge: 21,
    status: TransactionStatus.Successful,
    change: 18,
    title: 'EUR/USD',
    process: 85
  }
]
