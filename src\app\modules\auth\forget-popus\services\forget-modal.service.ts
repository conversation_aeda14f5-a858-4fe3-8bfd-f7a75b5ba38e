import {Injectable} from '@angular/core';
import {ModalService} from "@component/form/modals/modal.service";
import {ApiService} from "../../../core/services/api.service";

@Injectable({
  providedIn: 'root'
})
export class ForgetPasswordService {
  path = '/shared/OTPs';

  constructor(
    private apiService: ApiService,
    protected modalService: ModalService,
  ) {
  }

  // async send(data: { operationType: number }) {
  //   await this.apiService.post(this.path, data);
  // }
}
