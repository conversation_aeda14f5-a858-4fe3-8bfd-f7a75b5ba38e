<div
  class="box xl:p-6 dark:bg-bg4 grid grid-cols-12 gap-4 xxxl:gap-6 items-center shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
  <div class="col-span-12 lg:col-span-7 w-full">
    <div class="box bg-primary/5 dark:bg-bg3 lg:p-6 xl:p-8 border border-n30 dark:border-n500">
      <img [src]="'assets/images/logo-with-text.png'" alt="logo" class="md:hidden p-6 block mx-auto"/>

      @switch (selectedRegisterTab) {
        @case (RegisterTabs.mobile) {
          <h3 class="h3 mb-4 text-center font-extrabold text-[28px]">{{ 'CreateAccount' | translate }}
            <a class="text-primary " href="#">
              @switch (userType()) {
                @case(1){
                  <span  class="text-[28px]"> {{ 'asInvestor' | translate }}</span>
                }
                @case(2){
                  <span class="text-[28px]"> {{ 'asRequired' | translate }}</span>
                }
                @case(3){
                  <span class="text-[28px]">
                    {{'asCoraporate'|translate}}
                  </span>
                }
              }
              </a>
          </h3>
          <p class="md:mbH-6 pb-4 mb-4  md:pb-6 bb-dashed text-sm md:text-base">{{ 'enterPhoneNumber' | translate }}</p>
          <form (ngSubmit)="registerMobile()" [formGroup]="mobileForm" class="flex flex-col space-y-4" id="mobileForm">
            <app-input
              [control]="mobileForm.get('mobile')"
              [label]="'phone'"
              [type]="'tel'"
              [autofocus]="true"
            />
            <app-input
              (endIconClick)="togglePasswordVisibility()"
              [control]="mobileForm.get('password')"
              [endIcon]="isPasswordHidden ? 'icon-[solar--eye-line-duotone]': 'icon-[solar--eye-closed-line-duotone]'"
              [label]="'password'"
              [type]="isPasswordHidden ? 'password': 'text'"
              class="text-inherit"
            />
            <p> 
              {{ 'agreeTo' | translate }}
              <a class="text-primary" href="#"> {{ 'termsOfUse' | translate }} </a>,
              <a class="text-primary" href="#"> {{ 'privacyPolicy' | translate }} </a>.
            </p>
          </form>
          <div class="mt-8">
            <app-button
              (onClick)="registerMobile()"
              [text]="'register'"
            ></app-button>
          </div>
        }
        @case (RegisterTabs.nationalId) {
          <h3 class="h3 mb-4">{{ 'continueCreatingAccount' | translate }} </h3>
          <p
            class="md:mbH-6 pb-4 mb-4 md:pb-6 bb-dashed text-sm md:text-base">{{ 'completeIdentityDetails' | translate }}</p>
          <form (ngSubmit)="sendNafathRequst()" [formGroup]="nationalIdForm" class="flex flex-col space-y-4"
                id="nationalIdForm">
            <app-input
              [control]="nationalIdForm.get('nationalId')"
              [label]="'nationalId'"
              [type]="'number'"
              [autofocus]="true"
            />
            <!--            <app-date-picker-->
            <!--              [control]="nationalIdForm.get('dateOfBirth')"-->
            <!--              [label]="'dateOfBirth'"-->
            <!--            />-->
            <p>
              {{ 'agreeTo' | translate }}
              <a class="text-primary" href="#"> {{ 'termsOfUse' | translate }} </a>,
              <a class="text-primary" href="#"> {{ 'privacyPolicy' | translate }} </a>.
            </p>
          </form>
          <div class="mt-8">
            <app-button
              (onClick)="sendNafathRequst()"
              [text]="'register'"
            ></app-button>
          </div>
        }

      }
    </div>
  </div>
  <div class="hidden md:flex col-span-12 lg:col-span-5 justify-center items-center">
    @switch (selectedRegisterTab) {
      @case (RegisterTabs.mobile) {
        <img alt="img" height="560" src="assets/images/Illustration.svg"  width="533"/>
      }
      @case (RegisterTabs.nationalId) {
        <img alt="img" height="561" src="assets/images/nationalId.svg" width="533" class="h-[561px] w-[533px]"/>
      }

    }
  </div>
</div>

@if (modalService.isOpen('otp-modal')) {
  <app-otp-modal
    (submitOtp)="verifyOtp($event)"
  />
}

@if (modalService.isOpen('nafath-modal')) {
  <app-nafath-modal
    (onSuccess)="onNafathSuccess($event)"
    [response]="response"
  />
}

