{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"MDDPlusInvestor": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/mdd-plus-investor", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.scss", "src/assets/icons/line-awesome/css/line-awesome.min.css", "node_modules/flatpickr/dist/flatpickr.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2000kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "outputHashing": "all"}, "development": {"budgets": [{"type": "initial", "maximumWarning": "2000kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "outputHashing": "all", "optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "2000kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"options": {"port": 4100}, "builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "MDDPlusInvestor:build:production"}, "development": {"buildTarget": "MDDPlusInvestor:build:development"}, "staging": {"buildTarget": "MDDPlusInvestor:build:staging"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "MDDPlusInvestor:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.css", "src/assets/icons/line-awesome/css/line-awesome.min.css"], "scripts": []}}}}}, "cli": {"analytics": false}}