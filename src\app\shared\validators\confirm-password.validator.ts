import {AbstractControl, FormGroup, ValidationErrors, ValidatorFn} from '@angular/forms';

export function confirmPasswordValidator(controlName: string, matchingControlName: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) {
      throw new Error('Validator needs to be applied on a FormGroup');
    }

    const formGroup = control as FormGroup;
    const controlValue = formGroup.controls[controlName].value;
    const matchingControlValue = formGroup.controls[matchingControlName].value;

    if (controlValue !== matchingControlValue) {
      formGroup.controls[matchingControlName].setErrors({ confirmPasswordValidator: true });
      return { confirmPasswordValidator: true };
    } else {
      formGroup.controls[matchingControlName].setErrors(null);
      return null;
    }
  };
}
