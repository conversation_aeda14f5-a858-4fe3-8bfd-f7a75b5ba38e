import {computed, Injectable, signal} from '@angular/core';
import {BehaviorSubject, Observable} from "rxjs";
import {IUser} from "../models/user.model";
import {
  IChangePasswordForm,
  ICheckNafathStatusForm,
  IRegisterMobileForm,
  IResetPasswordForm,
  ISendNafathRequestForm,
  ISendNafathResponse,
  ISendNafathResponse2,
  IValidateLoginOTPBody,
  IValidateMobileForm,
  LoginForm,
} from "../models/login.model";
import {ICommercialRegistrations} from "../models/commercial-registrations.model";
import {ApiService} from "../../../core/services/api.service";
import {BaseResponse} from "../../../core/models/api-response.model";
import { ICompanyDocument } from '@component/layouts/companies/CompayList';
import { IBankAccount, Icreditwallet } from '../models/bank-account.model';
import { ISubAccounts } from '../models/sub-account.model';
import { Router } from '@angular/router';
import { EncryptStorage } from 'encrypt-storage';
import { EncryptStorageService } from './encrypt-storage.service';
import { IUserAccounts } from '../../settings/models/user-accounts.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  encryptStorageStep = new  EncryptStorage('User_info_step');

  public currentUser: Observable<IUser | null>;
  private path = '/Investor/Authentication';
  private clientpath = '/client';
  private currentUserSubject: BehaviorSubject<IUser | null>;
  tokenUser:string=localStorage.getItem('localToken')||'';
  private password?: string;
  addSubAccount = signal<boolean>(false)
  selectedSubAccount = signal<number|undefined>(undefined)
  #encryptStorage: EncryptStorage;
  #encryptTokenStorage: EncryptStorage;

  userBalance = signal<string>('')
  constructor(
    private apiService: ApiService,
     private router: Router,
     private encryptStorageService: EncryptStorageService
     
  ) {
    this.#encryptStorage = new EncryptStorage('MDD_Fintech_is_the_best__@2o2!');
    this.#encryptTokenStorage = new EncryptStorage('MDD_Fintech_is_userToken');
    this.currentUserSubject = new BehaviorSubject<IUser | null>(this.#encryptTokenStorage.getItem('currentUser') as IUser);
    this.currentUser = this.currentUserSubject.asObservable();
  }

get _currentUserSubject(){
  return this.currentUserSubject.getValue()
}
 setTokenUser(token:string){
  this.#encryptTokenStorage.setItem("localToken",token)
  this.tokenUser=token
 }
  public get currentUserValue(): IUser | null {
    return this.currentUserSubject.value;
  }

  isLoggedIn(): boolean {
    return !!this.currentUser;
  }


  hasRequiredRole(requiredRoles: number[]): boolean {
    return true;
    // const userEmployeeType = this.currentUserSubject.value?.employee?.employeeType;
    // // Check if userEmployeeType is defined and then check if it's included in the requiredRoles
    // return userEmployeeType !== undefined && requiredRoles.includes(userEmployeeType);
  }

  getAuthToken() {
    return this.currentUserSubject.value?.token;
  }

  hasPermission(permission: string): boolean {
    return true;
    // return this.currentUserValue?.roles.some(role => role.roleName === permission) ?? false;
  }

  async login(body: LoginForm) {
    this.#encryptTokenStorage.removeItem('token');
    return (await this.apiService.post<BaseResponse<string>>(`${this.path}/Login`, body)).list?.[0];
  }
  async login2(body: LoginForm) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.clientpath}/authentication/Login`, body));
  }

  async validateLoginOTP(body: IValidateLoginOTPBody) {
    const response = await this.apiService.post<BaseResponse<IUser>>(`${this.path}/ValidateLoginOTP`, body);
    this.currentUserSubject.next(response!.list![0]);
    this.#encryptTokenStorage.setItem('currentUser', response!.list![0]);
    const encodedToken = encodeURIComponent(response!.list![0].token)
    this.#encryptTokenStorage.setItem('token', encodedToken);
    this.setTokenUser(encodedToken)
    this.#encryptTokenStorage.setItem('TokenExpiryDate', response!.list![0]?.tokenExpiryDate);
    return this.currentUserSubject.value;
  }
  async validateLoginOTP2(body: IValidateLoginOTPBody) {
    const response:any = await this.apiService.post<BaseResponse<IUser>>(`${this.clientpath}/authentication/ValidateLoginOTP`, body);
    this.currentUserSubject.next(response!.data);
    this.#encryptTokenStorage.setItem('currentUser', response!.data);

    // localStorage.setItem('currentUser', JSON.stringify(response!.data));
    const encodedToken = encodeURIComponent(response!.data.token)
    this.#encryptTokenStorage.setItem('token', encodedToken);
    this.setTokenUser(encodedToken)
    this.#encryptTokenStorage.setItem('TokenExpiryDate', response!.data?.tokenExpiryDate);
    return this.currentUserSubject.value;
  }
  get isTokenExpired(): boolean {

   
    if(this.tokenUser.length>0){
      const tokenExpiryDate = this.#encryptTokenStorage.getItem('TokenExpiryDate');
      if (!tokenExpiryDate) return true;
      return new Date() > new Date(tokenExpiryDate);
    }
   
    return false
  }

  async registerMobile(body: IRegisterMobileForm) {
    await this.apiService.post(`${this.path}/RegisterMobile`, body);
  }
  async registerMobile2(body: IRegisterMobileForm) {
    await this.apiService.post(`${this.clientpath}/onboarding/RegisterMobile`, body);
  }
  async validateMobile(body: IValidateMobileForm) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.path}/ValidateMobile`, body)).list?.[0];
  }
  async validateMobile2(body: IValidateMobileForm) {
    return (await this.apiService.post<any>(`${this.clientpath}/onboarding/ValidateMobile`, body));
  }
  async sendNafathReqeust(body: ISendNafathRequestForm) {
    return (await this.apiService.post<BaseResponse<ISendNafathResponse>>(`${this.path}/SendNafathRequst`, body)).list?.[0];
  }

  async sendNafathReqeust2(body: ISendNafathRequestForm) {
    return (await this.apiService.post<any>(`${this.clientpath}/onboarding/send-nafath-request`, body))
  }
  async checkNafathStatus(body: ICheckNafathStatusForm) {
    return (await this.apiService.post<BaseResponse<IUser>>(`${this.path}/CheckNafathStatus`, body)).list?.[0];
  }
  checkNafathStatus2(body: ICheckNafathStatusForm) {
    return  this.apiService.post<BaseResponse<IUser>>(`${this.clientpath}/onboarding/check-nafath-status`, body);
  }

     async getprofileAccount() { 
      return await this.apiService.get<IUserAccounts>(`${this.clientpath}/profile`);
    }
     async getlookupsKYC() { 
      const pathKYC ='/shared/Lookups/AllLookups';
      return await this.apiService.get<IUserAccounts>(`${pathKYC}`);
    }

  async commercialRegistrationsList() {
    return (await this.apiService.get<ICommercialRegistrations>(`${this.clientpath}/onboarding/commercial-registrations`));
  }
  async companySubmitDocuments(item:number,body:FormData) {
    return (await this.apiService.post(`${this.clientpath}/onboarding/company-attachment`,body));
  }
  async companySubmitRegister(body:number,type:number) {
    const selected_type = this.encryptStorageStep.getItem('selected_type')
    // console.log(selected_type,'selected_type')
    return (await this.apiService.post(`${this.clientpath}/onboarding/commercial-registration`,{
      "cr":String(body),
      "crNationalNumber":String(type),
      "type":JSON.parse(this.encryptStorageStep.getItem('selected_type') || 'null')
    }
  ));
  }
  apiPostCompanyDelegate(cr:number,exp:string,type:number){
    return this.apiService.post(`${this.clientpath}/onboarding/company-delegate?CRNumber=${cr}&ExpiryDate=${exp}&Type=${type}`,{})
  }

  async commercialRegistration(body: ICheckNafathStatusForm) {
    await this.apiService.post(`${this.path}/CommercialRegistration`, body);
  }

  async forgotPassword(body: IRegisterMobileForm) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.path}/ForgotPassword`, body)).list?.[0];
  }
  async forgotPassword2(body: IRegisterMobileForm) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.clientpath}/authentication/ForgotPassword`, body));
  }

  async forgotPasswordValidateOTP(body: IValidateLoginOTPBody) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.path}/ForgotPassword/ValidateOTP`, body)).list?.[0];
  }
  async forgotPasswordValidateOTP2(body: IValidateLoginOTPBody) {
    return (await this.apiService.post<BaseResponse<string>>(`${this.clientpath}/authentication/ForgotPassword/ValidateOTP`, body));
  }

  async resetPassword(body: IResetPasswordForm) {
    await this.apiService.post(`${this.clientpath}/authentication/ResetPassword`, body);
  }

  async changePassword(body: IChangePasswordForm) {
    await this.apiService.post<BaseResponse<string>>(`${this.clientpath}/authentication/ChangePassword`, body);
  }

async getCollectioncount(){
  return this.apiService.get<Icreditwallet>(`${this.clientpath}/profile/collection-account`)
}



async getBalance(){
  return this.apiService.get<BaseResponse<string>>(`${this.clientpath}/profile/balance`)
}

async getAccounts(){
  return (await this.apiService.get<ISubAccounts>(`${this.clientpath}/profile/accounts`));
}
private profileCache = signal<any | null>(null);
readonly profile$ = computed(() => this.profileCache());

getCachedProfile(): any {
  const profile = this.#encryptTokenStorage.getItem('profile');
 
  
  return profile || null;
}


startTokenExpirationCheck() {
  setInterval(() => {
      if (this.encryptStorageService.isTokenExpired) {
          this.logout();
      }
  }, 60000); // Check every minute
}
logout(): void {
  localStorage.clear()
   // or whatever your token key is
   this.tokenUser=''
  this.currentUserSubject.next(null);
  this.router.navigate(['/auth']);
}

  // logout(): void {

  //   localStorage.removeItem('currentUser');
  //   localStorage.removeItem('token');
  //   this.currentUserSubject.next(null);
  //   this.tokenUser = '';
  //   // window.location.reload()
  //   this.router.navigate(['/auth']);

  // }
}
