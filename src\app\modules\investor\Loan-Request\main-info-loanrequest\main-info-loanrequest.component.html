@if(!firstStep()){
    <div class="box p-4 md:p-6 font-cairo grid grid-cols-12 gap-2 md:gap-5 items-center shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
      <div class="col-span-12 p-3">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-2xl md:text-3xl font-extrabold">{{ 'mainInfo' | translate }}</h3>
          <p class="text-gray-500 cursor-pointer" (click)="goToBackStep()">{{"Back"|translate}}</p>
        </div>
        <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>
        <div [formGroup]="loanrequestForm" class="w-full">
            <div class="w-full flex flex-col">
                <!-- Amount and Period Row -->
                <div class="flex flex-col md:flex-row gap-4 mb-6">
                    <div class="w-full md:w-1/2">
                        <app-input
                            [control]="loanrequestForm.get('RequestedAmount')"
                            [label]="'RequestedAmount' | translate"
                            [type]="'text'"
                            [autofocus]="true"
                        />
                    </div>
                    <div class="w-full md:w-1/2">
                      <p class="mb-1">{{ "Peroid" |translate}}</p>
                      <app-npx-dropdown 
                          [btnClassProps]="'rounded-3xl h-[49px] border border-gray-400 w-full'" 
                          [dropdownClassProps]="'w-full border-2 border-gray-300'" 
                          [placeholder]="'Peroid'|translate" 
                          [options]="incestExperienceList" 
                          [selectModel]="loanrequestForm.get('PeriodInDays')?.valueChanges|async" 
                          (selectModelChange)="handlePeriodInDaysEvent($event)"/>
                    </div>
                </div>
                
                <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>

                <!-- Upload Documents Section -->
                <div class="mt-6">
                  <p class="text-lg font-semibold text-gray-600 pb-4 md:pb-8">{{"pleaseattachmentfiles" |translate }}</p>

                  <!-- File Upload Components -->
                  <!-- Delegation Letter -->
                  <div class="mt-4">
                    @if (checkItAttchmentType(1)) {
                      <div class="relative">
                        <div class="w-full border-2 border-gray-300 rounded-3xl p-3 md:p-4 flex items-center bg-gray-50">
                          <div class="mr-3 text-blue-500">
                            @switch (getFileType(checkItAttchmentType(1)?.filePath)) {
                              @case ('image') {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              }
                              @case ('pdf') {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                              @default {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                            }
                          </div>

                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              {{getFileName(checkItAttchmentType(1)?.filePath)}}
                            </p>
                            <p class="text-xs text-gray-500">
                              {{getFileSize(checkItAttchmentType(1)?.fileSize)}} • {{getFileType(checkItAttchmentType(1)?.filePath) | uppercase}}
                            </p>
                          </div>

                          <button class="ml-2 p-1 text-blue-600 hover:text-blue-800" 
                                  (click)="previewFile(checkItAttchmentType(1)?.filePath)">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                        </div>

                        <button class="absolute top-1 right-1 md:top-2 md:right-2 bg-white/80 rounded-full p-1 text-red-600 hover:text-red-800" 
                                (click)="deleteAttachment(checkItAttchmentType(1)?.id)"
                                [attr.aria-label]="'Delete attachment'">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 md:w-5 md:h-5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    } @else {
                      <app-file-upload 
                        [control]="loanrequestForm.controls['DelegationLetter']" 
                        [isConvertToBase64]="false" 
                        label="{{'bankAccount'|translate}}" 
                        [placeholder]="'clickattachmenthere'" 
                        class="dark:bg-inherit block w-full"/>
                    }
                  </div>

                  <!-- Financial Statements -->
                  <div class="mt-4">
                    @if (checkItAttchmentType(2)) {
                      <div class="relative">
                        <div class="w-full border-2 border-gray-300 rounded-3xl p-3 md:p-4 flex items-center bg-gray-50">
                          <div class="mr-3 text-blue-500">
                            @switch (getFileType(checkItAttchmentType(2)?.filePath)) {
                              @case ('image') {
                                <img src="{{checkItAttchmentType(2)?.filePath}}" 
                                     class="w-16 h-16 object-cover rounded-lg" 
                                     alt="Preview">
                              }
                              @case ('pdf') {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                              @default {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                            }
                          </div>

                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              {{getFileName(checkItAttchmentType(2)?.filePath)}}
                            </p>
                            <p class="text-xs text-gray-500">
                              {{getFileType(checkItAttchmentType(2)?.filePath) | uppercase}}
                            </p>
                          </div>

                          @if (getFileType(checkItAttchmentType(2)?.filePath) !== 'image') {
                            <button class="ml-2 p-1 text-blue-600 hover:text-blue-800" 
                                    (click)="previewFile(checkItAttchmentType(2)?.filePath)">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            </button>
                          }
                        </div>

                        <button class="absolute top-1 right-1 md:top-2 md:right-2 bg-white/80 rounded-full p-1 text-red-600 hover:text-red-800" 
                                (click)="deleteAttachment(checkItAttchmentType(2)?.id)"
                                [attr.aria-label]="'Delete attachment'">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 md:w-5 md:h-5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    } @else {
                      <app-file-upload 
                        [control]="loanrequestForm.controls['FinancialStatements']" 
                        [isConvertToBase64]="false" 
                        label="{{'FinticList' |translate}}" 
                        [placeholder]="'clickattachmenthere'" 
                        class="dark:bg-inherit block w-full"/>
                    }
                  </div>

                  <!-- Cashflow -->
                  <div class="mt-4">
                    @if (checkItAttchmentType(3)) {
                      <div class="relative">
                        <div class="w-full border-2 border-gray-300 rounded-3xl p-3 md:p-4 flex items-center bg-gray-50">
                          <div class="mr-3 text-blue-500">
                            @switch (getFileType(checkItAttchmentType(3)?.filePath)) {
                              @case ('image') {
                                <img src="{{checkItAttchmentType(3)?.filePath}}" 
                                     class="w-16 h-16 object-cover rounded-lg" 
                                     alt="Preview">
                              }
                              @case ('pdf') {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                              @default {
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 md:w-8 md:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                            }
                          </div>

                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              {{getFileName(checkItAttchmentType(3)?.filePath)}}
                            </p>
                            <p class="text-xs text-gray-500">
                              {{getFileType(checkItAttchmentType(3)?.filePath) | uppercase}}
                            </p>
                          </div>

                          @if (getFileType(checkItAttchmentType(3)?.filePath) !== 'image') {
                            <button class="ml-2 p-1 text-blue-600 hover:text-blue-800" 
                                    (click)="previewFile(checkItAttchmentType(3)?.filePath)">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            </button>
                          }
                        </div>

                        <button class="absolute top-1 right-1 md:top-2 md:right-2 bg-white/80 rounded-full p-1 text-red-600 hover:text-red-800" 
                                (click)="deleteAttachment(checkItAttchmentType(3)?.id)"
                                [attr.aria-label]="'Delete attachment'">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 md:w-5 md:h-5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    } @else {
                      <app-file-upload 
                        [control]="loanrequestForm.controls['Cashflow']" 
                        [isConvertToBase64]="false" 
                        label="{{'Cashflow' |translate}}" 
                        [placeholder]="'clickattachmenthere'" 
                        class="dark:bg-inherit block w-full"/>
                    }
                  </div>
                </div>

                <!-- Next Button -->
                <div class="mt-8 flex justify-end">
                  <button (click)="goToNextStep()" type="submit" class="px-6 md:px-9 w-full md:w-60 py-2 border border-blue-500 text-blue-500 rounded-3xl">
                    {{"next"|translate}}
                  </button>
                </div>
            </div>
       </div>
    </div>
    </div>
}

<!------- Second Step ------->
@if(firstStep()){
  <div class="box p-4 md:p-6 font-cairo grid grid-cols-12 gap-2 md:gap-5 items-center shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
    <div class="col-span-12 p-3">
      <div class="flex justify-between items-start mb-4">
        <h3 class="text-2xl md:text-3xl font-extrabold">{{ 'pleaseselectpurchaseorder' | translate }}</h3>
        <p class="text-gray-500 cursor-pointer" (click)="goToBackStep()">{{"Back"|translate}}</p>
      </div>
      <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>
      <div [formGroup]="loanrequestForm" class="w-full">
          <div class="w-full flex flex-col">
              <!-- Type Selection -->
              <!-- <div class="flex flex-col md:flex-row gap-4 mb-6">
                  <div (click)="handleSelectType('Governmental')" 
                       [ngClass]="loanrequestForm.get('type')?.value == 'Governmental' ? 'border-primary bg-blue-50 dark:bg-bg3 dark:text-inherit' : 'border-gray-300'" 
                       class="w-full border-2 rounded-3xl p-3 text-center cursor-pointer transition-colors">
                      <p>{{"Government" |translate}}</p>
                  </div>
                  <div (click)="handleSelectType('Private')" 
                       [ngClass]="loanrequestForm.get('type')?.value == 'Private' ? 'border-primary bg-blue-50 dark:bg-bg3 dark:text-inherit' : 'border-gray-300'" 
                       class="w-full border-2 rounded-3xl p-3 text-center cursor-pointer transition-colors">
                      <p>{{"private" |translate}}</p>
                  </div>
              </div> -->
              <div class="flex flex-col md:flex-row gap-4 mb-2">
  <div (click)="handleSelectType('Governmental')" 
       [ngClass]="loanrequestForm.get('type')?.value == 'Governmental' 
                  ? 'border-primary bg-blue-50 dark:bg-bg3 dark:text-inherit' 
                  : 'border-gray-300'" 
       class="w-full border-2 rounded-3xl p-3 text-center cursor-pointer transition-colors">
    <p>{{"Government" |translate}}</p>
  </div>
  <div (click)="handleSelectType('Private')" 
       [ngClass]="loanrequestForm.get('type')?.value == 'Private' 
                  ? 'border-primary bg-blue-50 dark:bg-bg3 dark:text-inherit' 
                  : 'border-gray-300'" 
       class="w-full border-2 rounded-3xl p-3 text-center cursor-pointer transition-colors">
    <p>{{"private" |translate}}</p>
  </div>
</div>

<div *ngIf="loanrequestForm.get('type')?.invalid && loanrequestForm.get('type')?.touched"
     class="text-red-500 text-sm">
  {{"Please select a type (Governmental or Private)" | translate}}
</div>

              <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>

              <!-- Form Inputs -->
              <div class="space-y-4">
                  <app-input
                      [control]="loanrequestForm.get('clientCRNumber')"
                      [label]="'CompetitionNo' | translate"
                      [type]="'text'"
                      [autofocus]="true"
                  />
                  
                  <app-input
                      [control]="loanrequestForm.get('amount')"
                      [label]="'amountloan' | translate"
                      [type]="'text'"
                  />
                  
                  <app-input
                      [control]="loanrequestForm.get('invoiceVAT')"
                      [label]="'RefNumber' | translate"
                      [type]="'text'"
                  />
                  
                  <app-file-upload 
                      [control]="loanrequestForm.controls['Other']" 
                      [isConvertToBase64]="false" 
                      label="{{'Purchaseorderfile'|translate}}" 
                      [placeholder]="'clickattachmenthere'" 
                      class="dark:bg-inherit block w-full"/>
              </div>

              <!-- Save Button -->
              <div class="mt-8 flex justify-end">
                <button (click)="addListOfItems()" type="submit" class="px-6 md:px-9 w-full md:w-60 py-2 border border-blue-500 text-blue-500 rounded-3xl">
                  {{"save"|translate}}
                </button>
              </div>
          </div>
      </div>
    </div>
    
    <!-- List of Selected Items -->
    <div class="col-span-12 p-3">
      <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>
      
      @for(item of listOfSelectedItems; track item){
        <div class="border rounded-xl p-4 flex flex-col gap-2 rtl text-right shadow-sm w-full my-3">
          <!-- Top row -->
          <div class="flex flex-col md:flex-row justify-between items-start text-sm gap-2">
            <div class="flex-1">
              <span class="text-gray-500">{{'CompetitionNo'|translate}}: </span>
              <span class="font-bold">{{item.clientCRNumber}}</span>
            </div>
            <div class="flex-1">
              <span class="text-gray-500">{{'RefNumber'|translate}}: </span>
              <span class="font-bold">{{item.invoiceVAT || 0}}</span>
            </div>
            <div class="flex-1 flex items-center justify-end gap-2">
              <button class="text-red-600 hover:text-red-800" (click)="deleteItem(item.id)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <span class="text-gray-500 cursor-pointer" (click)="downloadFile(item.id)">{{'File'|translate}}</span>
            </div>
          </div>

          <!-- Bottom row -->
          <div class="flex flex-col md:flex-row justify-start items-start text-sm gap-2">
            <div class="flex-1">
              <span class="text-gray-500">{{'amountloan'|translate}}: </span>
              <span class="font-bold"> {{item.amount || 0 }} {{'SAR' |translate}}</span>
            </div>
            <div class="flex-1">
              <span class="text-gray-500">{{'Issuer'|translate}}: </span>
              <span class="font-bold">{{item.type}}</span>
            </div>
          </div>
        </div>
      }
    </div>
    
    <!-- Consent and Submit Buttons -->
    <div class="col-span-12 p-3">
      <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"></div>
      <div class="flex flex-col items-start justify-center gap-6 p-4 bg-white dark:bg-bg3 dark:text-inherit">
        <!-- Consent checkbox -->
        <label class="flex items-center text-gray-500 space-x-2 rtl:space-x-reverse">
          <input type="checkbox" [(ngModel)]="isConsentChecked" class="form-checkbox accent-blue-600" />
          <span>{{"MddSelectcheckbox" | translate}}</span>
        </label>

        <!-- Buttons -->
        <div class="flex flex-col md:flex-row justify-end w-full gap-4">
          <button (click)="onSubmit(1)" class="px-6 py-2 text-base md:text-lg rounded-full border-2 border-primary hover:bg-primary hover:text-white transition w-full md:w-auto">
            {{"send"|translate}}
          </button>
          <button (click)="onSubmit(0)" class="px-6 py-2 text-base md:text-lg rounded-full border-2 border-primary hover:bg-primary hover:text-white transition w-full md:w-auto">
            {{"Draft"|translate}}
          </button>
        </div>
      </div>
    </div>
  </div>
}

@if (modalService.isOpen('loan-request-popup')) {
  <app-add-loanrequest-popup
    [reqId]="reqId"
    (closePopup)="handleClosePopup($event)"
  />
}