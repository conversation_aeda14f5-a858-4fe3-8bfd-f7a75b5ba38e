import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoanRequestListComponent } from './loan-request-list/loan-request-list.component';
import { AddLoanRequestComponent } from './add-loan-request/add-loan-request.component';
import { MainInfoLoanrequestComponent } from './main-info-loanrequest/main-info-loanrequest.component';
import { LoanRequestMainDetailsComponent } from './Loan-Request_details/loan-request-main-details/loan-request-main-details.component';

const routes: Routes = [
  {path:'', component:LoanRequestListComponent },
  {path:'create', component: AddLoanRequestComponent },
  {path:'edit/:id', component: AddLoanRequestComponent },
  {path:'addInfo', component: MainInfoLoanrequestComponent },
  {path:'Details/:id', component: LoanRequestMainDetailsComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LoanRequestRoutingModule { }
