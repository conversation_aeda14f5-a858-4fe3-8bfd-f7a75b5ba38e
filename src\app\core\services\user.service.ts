import {Injectable} from '@angular/core';
import {IUser} from "../../modules/auth/models/user.model";
import {ApiService} from "./api.service";
import {IBankAccount} from "../../modules/auth/models/bank-account.model";

@Injectable({
  providedIn: 'root'
})
export class UserService {
  currentUser?: IUser;
  private path = '/investor/Users/<USER>';

  constructor(
    private apiService: ApiService,
  ) {
  }

  async getCurrentUser() {
    const path = '/investor/profile';
    // this.currentUser = (await this.apiService.get<IUser>(path)).list?.[0];
  }


  async bankAccount() {
    return (await this.apiService.get<IBankAccount>(this.path)).list?.[0];
  }

}
