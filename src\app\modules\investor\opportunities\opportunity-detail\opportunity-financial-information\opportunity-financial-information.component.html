<!-- first-two-------------->
<div class="grid grid-flow-row-dense grid-cols font-cairo">
  <p class="text-base  font-bold border-b-2 bb-dashed mt-3 mb-2 py-3">
    {{"finicialinfocompany" | translate }}
  </p>

  <div class="grid grid-cols-1 gap-4 mt-2 bg-[#B9BBBD0D] p-2">


    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal"> {{"ownerDebtEquity" | translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.debtToEquityRatio}} %</span>
    </div>
    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal">{{"Cashcycleindays" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold"> {{companyInfoData?.cashCycleDays}} </span>
    </div>



    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal">{{"Salesturnoverrateindays" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.inventoryTurnoverDays}}</span>
    </div>
  </div>

  <div class="grid grid-cols-1 gap-4 mt-5 bg-[#B9BBBD0D]  p-2">


    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal"> {{"Returnonequity" | translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.returnOnEquityPercentage}}%</span>
    </div>
    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal">{{"Currentassetscurrentliabilities" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.currentAssetsToLiabilitiesRatio}}</span>
    </div>
    <div class="flex justify-between items-center">
      <span class="text-black dark:text-inherit font-normal">{{"Profitmarginbeforedepreciation" |translate}}</span>
      <span class="text-black dark:text-inherit font-bold"> {{companyInfoData?.ebitdaProfitMarginPercentage}} %</span>
    </div>

    <!--tabs-->
    <div class="border-b border-gray-200 mt-3 mb-6">
      <ul class="flex flex-wrap -mb-px">
        <li *ngFor="let tab of tabs" class="me-2">
          <a (click)="changeTab(tab)"
            [ngClass]="{'text-[#015C8E] border-[#015C8E]': activeTab.field === tab.field, 'text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab.field !== tab.field}"
            class="inline-block p-3 border-b-2 rounded-t-lg font-medium justify- mx-2 cursor-pointer transition-colors">
            {{ tab.title! | translate }}
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
<!-- second-two-------------->


<!-- Tab Content -->
<div class="min-h-[300px]">
  @switch (activeTab.field) {
  @case("finianciallist") {
  <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D]  px-3">
    <p class="text-base  font-bold border-b-2 bb-dashed mt-1 mb-2 py-3 ">
      {{"financiallistCenter" | translate }}
    </p>

    <div class="grid grid-cols-1 gap-8 mt-3 py-2">


      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal">{{"Currentassets" |translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> {{companyInfoData?.currentAssetsPercentage}} % </span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"NOCurrentassets" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.nonCurrentAssets}} </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"totalassets" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.totalAssets}}</span>
      </div>

    </div>
  </div>
  <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D]  px-3">
    <div class="grid grid-cols-1 gap-8 mt-3 py-2">
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Propertyrights" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.equity}}</span>
      </div>
    </div>

  </div>
  <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D]  px-3">
    <div class="grid grid-cols-1 gap-8 mt-3 py-2">
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Currentliabilities" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.currentLiabilitiesPercentage}} %</span>
      </div>
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Non-currentliabilities" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.nonCurrentLiabilitiesPercentage}} %</span>
      </div>
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Totalliabilities" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.totalLiabilitiesPercentage}}%</span>
      </div>
    </div>

  </div>

  }
  @case("incomelist") {
   <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D] px-3">
    <p class="text-base  font-bold border-b-2 bb-dashed mt-1 mb-2 py-3 ">
      {{"IncomeStatement" | translate }}
    </p>

    <div class="grid grid-cols-1 gap-8 mt-3 py-2">


      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal">{{"incoming" |translate}}</span>
        <span class="text-black dark:text-inherit font-bold">  109,098,000 <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /> </span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"Grossprofit" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> 100,098,000  <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
   

    </div>
  </div>
  <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D] px-3">
    <div class="grid grid-cols-1 gap-8 mt-3 py-2">
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Otherrevenues" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> 109,098,000 <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Earningsbeforeinteresttaxesandzakat" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> 109,098,000 <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
    </div>

  </div>
  <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D] px-3">
    <div class="grid grid-cols-1 gap-8 mt-3 py-2">
      <div class="flex justify-between items-center ">
        <span class="text-black dark:text-inherit font-normal"> {{"Netprofit" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> 109,098,000 <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /> </span>
      </div>
     
    </div>

  </div>
  }
  @case("Cashflows") {
    <div class="grid grid-flow-row-dense grid-cols bg-[#B9BBBD0D] px-3">
    <p class="text-base  font-bold border-b-2 bb-dashed mt-1 mb-2 py-3 ">
      {{"CashFlowStatement" | translate }}
    </p>

    <div class="grid grid-cols-1 gap-8 mt-3 py-2">


      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal">{{"Cashflowsfromoperatingactivities" |translate}}</span>
        <span class="text-black dark:text-inherit font-bold">  {{companyInfoData?.operatingCashFlow}} <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /> </span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"Cashflowsfrominvestingactivities" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold"> {{companyInfoData?.investingCashFlow}}  <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"Cashflowsfromfinancingactivities" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.financingCashFlow}} <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"Netcashflows" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.netCashFlow}} <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-black dark:text-inherit font-normal"> {{"Lastperiodcashbalance" | translate}}</span>
        <span class="text-black dark:text-inherit font-bold">{{companyInfoData?.endingCashBalance}} <img
          src="https://www.sama.gov.sa/ar-sa/Currency/Documents/Saudi_Riyal_Symbol-2.svg"  class="inline w-3 dark:filter dark:invert dark:brightness-0 dark:contrast-200" /></span>
      </div>
   

    </div>
  </div>

  }
  }
</div>