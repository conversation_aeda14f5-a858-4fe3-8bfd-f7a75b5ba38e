<div class="relative min-h-screen bg-secondary1/5 dark:bg-bg3 hidden-horizental">


  <svg width='300' height='300' class="shap_top" preserveAspectRatio="none">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color: #015C8E;"></stop>
        <stop offset="100%" style="stop-color: #015C8E;"></stop>
      </linearGradient>
    </defs>

        <path
        d="M100,100 C70,80 80,50 100,30 L100,100 Z"
        fill='url(#gradient)'

        >
      <animate attributeName='d' dur='5s' repeatCount='indefinite'
         values='
         M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z;

         M453.5,289Q413,328,399.5,374Q386,420,331.5,393.5Q277,367,239,415.5Q201,464,152.5,449Q104,434,129,365Q154,296,141,273Q128,250,108,211Q88,172,137,171Q186,170,198.5,124.5Q211,79,244,104.5Q277,130,310,131.5Q343,133,388.5,147.5Q434,162,464,206Q494,250,453.5,289Z;


         M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z'
      />
      </path>
  </svg>

  <svg width='500' height='500' class="shap_bottom" preserveAspectRatio="none">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color: #015C8E;"></stop>
        <stop offset="100%" style="stop-color: #015C8E;"></stop>
      </linearGradient>
    </defs>

        <path
        d="M100,100 C70,80 80,50 100,30 L100,100 Z"
        fill='url(#gradient)'

        >
      <animate attributeName='d' dur='5s' repeatCount='indefinite'
         values='
         M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z;

         M453.5,289Q413,328,399.5,374Q386,420,331.5,393.5Q277,367,239,415.5Q201,464,152.5,449Q104,434,129,365Q154,296,141,273Q128,250,108,211Q88,172,137,171Q186,170,198.5,124.5Q211,79,244,104.5Q277,130,310,131.5Q343,133,388.5,147.5Q434,162,464,206Q494,250,453.5,289Z;


         M476,299Q454,348,426.5,392.5Q399,437,347.5,444Q296,451,246.5,467.5Q197,484,147,463Q97,442,82.5,390Q68,338,55.5,294Q43,250,44.5,201Q46,152,85.5,122.5Q125,93,162.5,61.5Q200,30,248,39Q296,48,340.5,64.5Q385,81,423,114.5Q461,148,479.5,199Q498,250,476,299Z'
      />
      </path>
  </svg>
    <img [src]="colorMode == 'light' ? 'assets/images/logo.png' : 'assets/images/logo.png'" class="p-6 hidden md:flex shrink-0 lg:p-8 relative z-[2]" alt="logo" />
  <div class="flex items-center justify-center md:mt-1 min-h-screen-sm-down">
    <div class="relative z-[2] max-w-[1164px] h-[612px] mx-auto px-3 pb-10">
      <div class="flex min-h-screen items-center justify-center py-10 md:py-16 lg:py-20 px-3">
        <div class="flex flex-col items-center justify-center text-center max-w-[640px] mx-auto">
          <h2 class="h2 mb-4 lg:mb-6 font-extrabold text-6xl">{{ 'errorPageNotFound' | translate }}</h2>
          <img src="assets/images/404.svg" alt="confirm illustration" class="mb-10 lg:mb-14" />
      
       
          <a routerLink="/" class="btn"> {{ 'backToHome' | translate }} </a>
        </div>
      </div>
      
    </div>
  </div>
</div>






