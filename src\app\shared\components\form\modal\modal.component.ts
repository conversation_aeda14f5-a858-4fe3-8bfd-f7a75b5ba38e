import {Component, EventEmitter, Input, Output} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {ButtonComponent} from "../button/button.component";
import {NgClass} from "@angular/common";
import {MDModalModule} from "../modals";
import {ModalService} from "../modals/modal.service";

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [

    MDModalModule,
    TranslateModule,
    ButtonComponent,
    NgClass
  ],
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.scss'
})
export class ModalComponent {
  @Input() id!: string;
  @Input() title!: string;
  @Input() isLarger: boolean = true;
  @Output() onSubmit = new EventEmitter<any>();
  @Output() onClose = new EventEmitter<any>();

  constructor(
    private modalService: ModalService,
  ) {
  }

  onClickSubmit() {
    this.onSubmit.emit();
  }

  onClickClose() {
    this.onClose.emit();
    this.modalService.close(this.id);
  }
}
