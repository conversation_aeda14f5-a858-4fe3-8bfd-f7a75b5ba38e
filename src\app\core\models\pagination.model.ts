
export interface IPagination {
  totalRows: number,
  page: number,
  pageSize: number,
  loading: boolean,
  pageSizeOptions: number[],
}

export interface IQuery extends IPagination {
  find: string,
  page:number,
  isServerMode: boolean,
}


export const PAGINATION: IPagination = {
  pageSize:10,
  pageSizeOptions: [6, 10],
} as IPagination;

export class Search {
  email?: string;
  iDs?: string;
  sort?: string;
  find?: string;
  page?: number;
  status?: any;
  url?: string;
  employeeID?: string;
  organizationID?: string;
  orderID?: string;
  orderType?: number;
  itemsCount?: number;
  limit?: number;
  offset?: number;
  pages?: number;
  next?: number;
  previous?: number;
  assginmentType?: string;
  includeType?: any;
}
