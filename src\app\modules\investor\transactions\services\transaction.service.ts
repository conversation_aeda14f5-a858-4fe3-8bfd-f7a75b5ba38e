import { Injectable } from '@angular/core';

import {InvestmentModel} from "../../investments/models/investment.model";
import {TransactionModel, TransactionSearch} from "../models/transaction.model";
import { PAGINATION } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';
import { BaseResponse } from '../../../../core/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {

   pagination = PAGINATION;
  private path = '/investor/Transactions/search';

  constructor(
    private apiService: ApiService,
  ) {
  }

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);



  async getList(query?: TransactionSearch) {
    query = {...query, limit: this.pagination.pageSize} as TransactionSearch;
    const response = await this.apiService.post<BaseResponse<TransactionModel>>(`${this.path}`, query);
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response.list;
  }
}
