import {Injectable} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {read, utils} from "xlsx";
import {HttpClient} from "@angular/common/http";

interface ErrorMessage {
  [key: string]: (params?: any) => string;
}

@Injectable({
  providedIn: 'root'
})
export class InputHelperService {
  constructor(
    private translate: TranslateService,
    private http: HttpClient
  ) {
  }

  uid = () => 'id' + Date.now().toString(16) + Math.random().toString(16);
  getFormControlName = (fc: any) => Object.keys(fc?.parent?.controls ?? {}).find(key => fc.parent.controls[key] === fc);
  isRequired = (control: any) => control?.errors?.required ?? false

  getErrorMessage(control: any, label?: string): string | null {
    const errorMessages: ErrorMessage = {
      required: () => this.translate.instant('fieldRequired', {field: this.translate.instant(label ?? 'field')}),
      maxlength: (params: any) => `This field cannot be more than ${params.requiredLength} characters. You entered ${params.actualLength} characters.`,
      minlength: (params: any) => `This field must be at least ${params.requiredLength} characters. You entered ${params.actualLength} characters.`,
      email: () => 'Please enter a valid email address.',
      serverError: (params: any) => params,
      // Add more validators as needed
    };
    if (control.errors) {
      // Find the first error
      const firstErrorKey = Object.keys(control.errors)[0];
      const getErrorMessageFunc = errorMessages[firstErrorKey];
      // Check if there's a custom message for this error
      if (getErrorMessageFunc) {
        return getErrorMessageFunc(control.errors[firstErrorKey]);
      }
    }
    return null;
  }

  uploadFile(callback: (e: Event) => void, accept = '.xlsx') {
    const fileInput = Object.assign(document.createElement('input'), {
      type: 'file',
      accept: accept,
      onchange: callback
    });
    fileInput.click();
  }

  downloadFile(name: string) {
    const basePath = {
      'xlsx': './assets/excels/',
      'pdf': './assets/pdfs/',
    };
    const extension = name.split('.').pop();
    if (!extension || !(extension in basePath)) return;
    const downloadLink = document.createElement('a');
    downloadLink.href = basePath[extension as keyof typeof basePath] + name;
    downloadLink.download = name;
    downloadLink.click();
  }

  readFileAndProcess<T>(event: Event, processItems: (items: T[]) => void): void {
    const file = (event.target as HTMLInputElement)?.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = (e: ProgressEvent<FileReader>) => {
        if (e.target?.result) {
          const data = new Uint8Array(e.target.result as ArrayBuffer);
          const workbook = read(data);
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const items: T[] = utils.sheet_to_json(worksheet);
          processItems(items); // Callback function for processing items
        }
      };
      reader.readAsArrayBuffer(file);
    }
  }

  uploadAndReadFile<T>(processItems: (items: T[]) => void) {
    this.uploadFile((event: Event) => {
      this.readFileAndProcess<T>(event, processItems);
    });
  }

  convertBlobToBase64(blob?: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = () => reject(reader.error);
      reader.onload = () => {
        // The result includes the data URL, which is split to get only the Base64 string
        const base64Data = (reader.result as string).split(',')[1];
        resolve(base64Data);
      };
      reader.readAsDataURL(blob!);
    });
  }

  // Function to fetch a file from URL and convert it to a File object
  urlToFile(url: string): Promise<File> {
    return this.http.get(url, {
      observe: 'response',
      responseType: 'blob' as 'json'  // Cast 'blob' as 'json' to bypass TypeScript's strict type checks
    }).toPromise().then(response => {
      if (!response || !response.body) {
        throw new Error("Failed to load the blob from the URL or response is undefined");
      }
      const blob: Blob = response.body as Blob;  // Cast the body to Blob
      const contentDisposition = response.headers.get('Content-Disposition') || '';
      const contentType = response.headers.get('Content-Type') || '';
      // Extract filename from Content-Disposition header if present
      let filename = 'default-name';
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(contentDisposition);
      if (matches != null && matches[1]) {
        filename = matches[1].replace(/['"]/g, '');
      }
      return new File([blob], filename, {type: contentType});
    });
  }

  isBase64(str: string): boolean {
    try {
      return btoa(atob(str)) === str;
    } catch (err) {
      return false;
    }
  }

  isUrl(str: string): boolean {
    try {
      new URL(str);
      return true;
    } catch (_) {
      return false;
    }
  }
}
