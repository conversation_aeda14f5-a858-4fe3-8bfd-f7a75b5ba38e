<div class="card">
  <div class="flex items-center gap-3 card-body">
    <div
      [ngClass]="color"
      class="flex items-center justify-center w-12 h-12 rounded-md text-15 shrink-0">
      <lucide-angular [name]="icon"></lucide-angular>
    </div>
    <div class="grow">
      <h5 [countUp]="count" class="mb-1 text-16 counter-value">0</h5>
      <p class="text-slate-500 dark:text-zink-200">{{ title | translate }}</p>
    </div>
  </div>
</div>
