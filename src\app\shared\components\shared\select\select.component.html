@if (label) {
  <label [for]="control?.name ?? placeholder">
    {{ label | translate }}
    @if (required) {
      <span class="text-red-500"> *</span>
    }
  </label>
}
@if (control) {
  <ng-select
    [hideSelected]="true"
    [editableSearchTerm]="editableSearchTerm"
    [notFoundText]="'notFoundText' | translate"
    [addTagText]="'addTagText' | translate"
    (change)="onSearchSelect($event)"
    (clear)="clearSearch()"
    (click)="onClickSelect()"
     [compareWith]="compareWith"
    [required]=" required"
    [bindLabel]="bindLabel"
    [bindValue]="bindValue"
    [formControl]="control"
    [placeholder]="placeholder | translate"
    [items]="items"
    [typeahead]="lookup.input$"
    [loading]="lookup.loading"
    [minTermLength]="lookup.minTermLength"
    [multiple]="multiple"
    [readonly]="disabled  || (!items && !addTag)"
    [ngClass]="{'error': control?.invalid && (control?.dirty || control?.touched) && !(disabled || (!items && !addTag))}"
    [addTag]="addTag">
  </ng-select>
} @else {
  <ng-select
    [hideSelected]="true"
    [(ngModel)]="selectModel"
    [editableSearchTerm]="editableSearchTerm"
    [notFoundText]="'notFoundText' | translate"
    [addTagText]="'addTagText' | translate"
    (change)="onSearchSelect($event)"
    (clear)="clearSearch()"
     [compareWith]="compareWith"
    (click)="onClickSelect()"
    [bindLabel]="bindLabel"
    [bindValue]="bindValue"
    [placeholder]="placeholder | translate"
    [items]="items"
    [typeahead]="lookup.input$"
    [loading]="lookup.loading"
    [minTermLength]="lookup.minTermLength"
    [multiple]="multiple"
    [readonly]="disabled || (!items && !addTag)"
    [ngClass]="{'error': control?.invalid && (control?.dirty || control?.touched) && !(disabled || (!items && !addTag))}"
    [addTag]="addTag">
  </ng-select>
}

<ng-content></ng-content>


@if (control?.invalid && (control?.dirty || control?.touched) && label && !(disabled || (!items && !addTag))) {
  <div id="username-error" class="mt-1 text-sm text-red-500">
    {{ inputHelperService.getErrorMessage(control, label) }}
  </div>
}
