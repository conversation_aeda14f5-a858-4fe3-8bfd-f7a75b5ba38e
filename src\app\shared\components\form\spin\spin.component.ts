import {Component, EventEmitter, Input, Output} from '@angular/core';
import {LucideAngularModule} from "lucide-angular";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";

@Component({
  selector: 'app-spin',
  standalone: true,
  imports: [LucideAngularModule, ReactiveFormsModule, FormsModule],
  templateUrl: './spin.component.html',
  styleUrl: './spin.component.scss'
})
export class SpinComponent {
  @Input() control: any;
  @Output() onChange = new EventEmitter<number>();
  value: number = 0;
  minus = () => {
    if (this.value < 0) return;
    this.value -= 1;
    this.control && this.control.setValue(this.value - 1);
    this.onChange.emit(this.value);
  };
  plus = () => {
    this.value += 1;
    this.control && this.control.setValue(this.value + 1);
    this.onChange.emit(this.value);
  };

  handleOnChange(event: Event): void {
    this.onChange.emit(+(event.target as HTMLInputElement).value);
  }
}
