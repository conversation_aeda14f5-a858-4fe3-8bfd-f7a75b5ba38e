import { Injectable } from "@angular/core";
import { EncryptStorage } from "encrypt-storage";
import { AuthService } from "../../auth/services/auth.service";

@Injectable({ providedIn: 'root' })
export class ProfileService {
  private profileDataCache: any = null;
  private accountIdCache: string | null = null;

  #encryptTokenStorage = new EncryptStorage('MDD_Fintech_is_userToken');

  constructor(private authService: AuthService) {}

  async loadProfileData(): Promise<any> {
    if (this.profileDataCache) {
      return this.profileDataCache;
    }

    try {
      const encryptedProfile = this.#encryptTokenStorage.getItem('profile');

      if (!encryptedProfile) {
        console.warn('No profile in storage');
        return null;
      }

      const profile = typeof encryptedProfile === 'string'
        ? JSON.parse(encryptedProfile)
        : encryptedProfile;

      this.accountIdCache = profile.id;

      const allAccounts = await this.authService.getAccounts();
      const matched = Array.isArray(allAccounts.data)
        ? allAccounts.data.find((item: any) => item.id === this.accountIdCache)
        : null;

      if (matched) {
        this.profileDataCache = matched;
        return matched;
      } else {
        console.warn('No matching account');
        return null;
      }

    } catch (e) {
      console.error('Failed to load profile data', e);
      return null;
    }
  }

  getCachedProfile() {
    return this.profileDataCache;
  }
}
