<div
  class="box xl:p-6 dark:bg-bg4 grid grid-cols-12 gap-4 xxxl:gap-6 items-center shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
  <div class="col-span-12 lg:col-span-7 w-full">
    <div class="box bg-primary/5 dark:bg-bg3 lg:p-6 xl:p-8 border border-n30 dark:border-n500">
      <img [src]="'assets/images/logo-with-text.png'" alt="logo" class="md:hidden p-6 block mx-auto"/>
      <h3 class="h3 mb-4">{{ 'resetPassword' | translate }}</h3>
      <p class="md:mb-6 pb-4 mb-4 md:pb-6 bb-dashed text-sm md:text-base">{{ 'setNewPasswordMessage' | translate }}</p>
      <form (ngSubmit)="onSubmit()" [formGroup]="formGroup" class="flex flex-col space-y-4"
            id="signupForm">
        <app-input
          (endIconClick)="togglePasswordVisibility()" [control]="formGroup.get('newPassword')"
          [endIcon]="isPasswordHidden ? 'icon-[solar--eye-line-duotone]': 'icon-[solar--eye-closed-line-duotone]'"
          [label]="'newPassword'"
          [type]="isPasswordHidden ? 'password': 'text'"
        />
        <app-input
          (endIconClick)="togglePasswordVisibility()" [control]="formGroup.get('confirmPassword')"
          [endIcon]="isPasswordHidden ? 'icon-[solar--eye-line-duotone]': 'icon-[solar--eye-closed-line-duotone]'"
          [label]="'confirmPassword'"
          [type]="isPasswordHidden ? 'password': 'text'"
        />
      </form>
      <div class="mt-8">
        <app-button
          (onClick)="onSubmit()"
          [text]="'set'"
        ></app-button>
      </div>
    </div>
  </div>
  <div class="hidden md:flex col-span-12 lg:col-span-5 justify-center items-center">
    <img alt="img" height="560" src="assets/images/reset-password.png" width="533"/>
  </div>
</div>
