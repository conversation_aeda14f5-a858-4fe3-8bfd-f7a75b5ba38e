<!--<div>-->
<!--  <div class="flex items-center gap-2">-->
<!--    <input id="checkboxDefault1"-->
<!--           [formControl]="control"-->
<!--           [checked]="checked"-->
<!--           class="w-4 h-4 border rounded-sm appearance-none bg-slate-100 border-slate-200 dark:bg-zink-600 dark:border-zink-500 checked:bg-primary-500 checked:border-primary-500 dark:checked:bg-primary-500 dark:checked:border-primary-500 checked:disabled:bg-primary-400 checked:disabled:border-primary-400"-->
<!--           type="checkbox" value="">-->
<!--    <label for="checkboxDefault1"-->
<!--           class="inline-block text-base font-medium align-middle cursor-pointer">-->
<!--      {{ label }}</label>-->
<!--  </div>-->
<!--</div>-->
<div class="relative flex gap-x-2">
  <div class="flex h-6 items-center">
    @if (control) {
      <input [formControl]="control"
             [id]="id ?? name"
             [name]="name"
             (change)="handleOnChange($event)"
             class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600
                  border appearance-none bg-slate-100 dark:bg-zink-600 dark:border-zink-500
                  checked:bg-primary-500 checked:border-primary-500 dark:checked:bg-primary-500
                  dark:checked:border-primary-500 checked:disabled:bg-primary-400 checked:disabled:border-primary-400
                  cursor-pointer"
             type="checkbox">
    } @else {
      <input
        [id]="id ?? name"
        [name]="name"
        [checked]="value"
             [disabled]="disabled"
        (change)="handleOnChange($event)"
        class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600
                  border appearance-none bg-slate-100 dark:bg-zink-600 dark:border-zink-500
                  checked:bg-primary-500 checked:border-primary-500 dark:checked:bg-primary-500
                  dark:checked:border-primary-500 checked:disabled:bg-primary-400 checked:disabled:border-primary-400
                  cursor-pointer"
        type="checkbox">
    }
  </div>
  @if(label){
    <div class="text-sm leading-6">
    <label [for]="id ?? name"
           class="font-medium inline-block text-base cursor-pointer select-none">{{ label | translate }}</label>
    @if (description) {
      <p class="text-gray-500">{{ description }}</p>
    }
  </div>
  }
</div>
