import { NgClass } from '@angular/common'
import { Component, HostListener } from '@angular/core'

@Component({
  selector: 'app-notification-dropdown',
  standalone: true,
  imports: [NgClass],
  templateUrl: './notification.component.html'
})


export class NotificationDropdownComponent {
  isOpen = false;

  toggleOpen(event: Event) {
    event.stopPropagation(); // Prevent the click from bubbling up
    this.isOpen = !this.isOpen;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const notificationElement = document.querySelector('#notification');
    const buttonElement = document.querySelector('#notification-btn');
    
    // Check if click is outside both the dropdown and the button
    if (notificationElement && buttonElement) {
      const clickedInsideDropdown = notificationElement.contains(event.target as Node);
      const clickedOnButton = buttonElement.contains(event.target as Node);
      
      if (!clickedInsideDropdown && !clickedOnButton) {
        this.isOpen = false;
      }
    }
  }
}
// export class NotificationDropdownComponent {
//   isOpen = false
//   toggleOpen() {
//     this.isOpen = !this.isOpen
//   }
//   @HostListener('document:click', ['$event'])
//   onDocumentClick(event: MouseEvent): void {
//     // Check if the click event target is not within the sidebar
//     if (!document.querySelector('#notification')!.contains(event.target as Node) && !document.querySelector('#notification-btn')!.contains(event.target as Node)) {
//       this.isOpen = false // Close the sidebar
//     }
//   }
// }
