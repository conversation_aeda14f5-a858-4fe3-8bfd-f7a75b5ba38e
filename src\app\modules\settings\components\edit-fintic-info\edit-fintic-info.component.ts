import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { InputComponent } from '@component/form/input/input.component';
import { SelectComponent } from '@component/form/select/select.component';
import { TranslateModule } from '@ngx-translate/core';
import { OtpModalComponent } from '../../../../shared/otp/otp-modal/otp-modal.component';
import { ModalService } from '@component/form/modals/modal.service';
import { OperationType } from '../../../../core/enums/operation-type.enum';
import { ToastService } from '../../../../core/services/toast.service';
import { OtpService } from '../../../../shared/otp/services/otp.service';
import { IUserAccounts } from '../../models/user-accounts.model';
import { SettingsService } from '../../services/settings.service';
import { AuthService } from '../../../auth/services/auth.service';
import { EncryptStorage } from 'encrypt-storage';

@Component({
  selector: 'app-edit-fintic-info',
  standalone: true,
   imports: [
      // FileUploadComponent,
      FormsModule,
      InputComponent,
      ReactiveFormsModule,
      TranslateModule,
     
      OtpModalComponent,
      CommonModule
      // ChangePasswordComponent
    ],
  templateUrl: './edit-fintic-info.component.html',
  styleUrl: './edit-fintic-info.component.css'
})
export class EditFinticInfoComponent implements OnInit{

   userAccountsForm!: FormGroup
    phoneForm!: FormGroup
    emailForm!: FormGroup
    commercialRegistrationForm!: FormGroup
    userAccount?: IUserAccounts;
    banks: any
  accountDetails: any;
   @Input() profileData: any;
  // accountDetails: string | null = null;
  allcounts: any;
   #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
    constructor(
      private fb: FormBuilder,
      protected modalService: ModalService,
      private otpService: OtpService,
      private toast: ToastService,
      private settingsService: SettingsService,
      public authService: AuthService,
     
    ) {
    }
  
    async ngOnInit() {
      this.userAccountsForm = this.fb.group({
        bankID: [null, [Validators.required]],
        accountNo: [null, [Validators.required]],
        iban: [null, [Validators.required]],
      })
      this.phoneForm = this.fb.group({
        newMobileNumber: [null, [Validators.required]],
      })
      this.emailForm = this.fb.group({
        newEmailAddress: [null, [Validators.required]],
      })
  
      await this.getBanks();
      await this.getUserAccounts();
    
         this.updateEmailForm();
    }
  

    async getBanks() {
      try {
        this.banks = (await this.settingsService.getBanks());
       
        // this.banks = response ?? [];
      } catch (error: any) {
        console.error('Error fetching banks:', error);
      }
    }
  
    async getUserAccounts() {
      try {
        // this.userAccount = (await this.settingsService.getUserAccounts()).list?.[0];
        if (this.userAccount) {
          this.userAccountsForm.patchValue({
            bankID: {id: this.userAccount.bankID, name: this.userAccount.bankName},
            accountNo: this.userAccount.accountNo,
            iban: this.userAccount.iban
          });
  
        }
      } catch (e: any) {
      }
    }
  

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['profileData'] && this.profileData?.email) {
      this.updateEmailForm();
    }
    if (changes['profileData'] && this.profileData?.mobile) {
      this.updatemobileForm();
    }
  }

  updateEmailForm() {
    this.emailForm.patchValue({
      newEmailAddress: this.profileData.email
    });
  }
  updatemobileForm() {
    this.phoneForm.patchValue({
      newMobileNumber: this.profileData.mobile
    });
  }

    async updateUserAccountsVerified(otp: string) {
      if (this.userAccountsForm.valid) {
        try {
          const body = {
            bankID: this.userAccountsForm.value.bankID.id,
            bankName: this.userAccountsForm.value.bankID.name,
            accountNo: this.userAccountsForm.value.accountNo,
            iban: this.userAccountsForm.value.iban,
            mfaCode: otp
          }
          await this.settingsService.updateUserAccounts(body)
          this.modalService.close('otp-modal')
        } catch (error: any) {
          this.toast.error(error)
        }
      } else {
        this.userAccountsForm.markAllAsTouched()
      }
    }
  
    async updateUserAccounts() {
      if (this.userAccountsForm.valid) {
        try {
          await this.otpService.send({operationType: OperationType.Change_Bank_Account})
          this.modalService.open('otp-modal')
        } catch (error: any) {
        }
      } else {
        this.userAccountsForm.markAllAsTouched()
      }
    }
  
  
    async changeMobileNumber() {
      if (this.userAccountsForm.valid) {
        try {
          await this.otpService.send({operationType: OperationType.Change_Mobile_Number})
          this.modalService.open('otp-reset-mobile-modal')
        } catch (error: any) {
        }
      } else {
        this.userAccountsForm.markAllAsTouched()
      }
    }
  
    async updateEmail() {
    if (this.emailForm.valid) {
      try {
        await this.otpService.send({operationType: OperationType.Change_Email})
        this.modalService.open('otp-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }
  

    async updateConfirmEmail() {
    if (this.emailForm.valid) {
      try {
        await this.otpService.send({operationType: OperationType.Change_Email})
        this.modalService.open('otp-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }

  async resetEmail(otp: any) {
    if (this.emailForm.valid) {
      try {
        const body = {
          newEmailAddress: this.emailForm.value.newEmailAddress,
          mfaCode: otp
        }
     const response = await this.settingsService.resetEmail(body) as { success: boolean };
      this.toast.success('please confirm reset successfully');
      this.modalService.close('otp-reset-email-modal');
     
      if (response) {
         this.toast.success('please confirm reset successfully');
        this.modalService.open('otp-confirm-reset-email-modal');
      }
    
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }

  async confirmResetEmail(otp: any) {
    if (this.emailForm.valid) {
      try {
        const body = {
          newEmailAddress: this.emailForm.value.newEmailAddress,
          mfaCode: otp
        }
        await this.settingsService.confirmResetEmail(body)
        this.toast.success('Email reset successfully');
  window.location.reload();
        this.modalService.close('otp-confirm-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }
  
    async resetMobileNumber(otp: any) {
      if (this.phoneForm.valid) {
        try {
          const body = {
            newMobileNumber: this.phoneForm.value.newMobileNumber,
            mfaCode: otp
          }
          await this.settingsService.resetMobileNumber(body)
          this.modalService.close('otp-reset-mobile-modal')
          this.modalService.open('otp-confirm-reset-mobile-modal')
        } catch (error: any) {
        }
      } else {
        this.phoneForm.markAllAsTouched()
      }
    }
  
    async confirmResetMobileNumber(otp: any) {
      if (this.phoneForm.valid) {
        try {
          const body = {
            newMobileNumber: this.phoneForm.value.newMobileNumber,
            mfaCode: otp
          }
          await this.settingsService.confirmResetMobileNumber(body)
          this.modalService.close('otp-confirm-reset-mobile-modal')
        } catch (error: any) {
        }
      } else {
        this.phoneForm.markAllAsTouched()
      }
    }
  
  }
  


