<div class="col-span-12">
  <div class="box col-span-12 lg:col-span-6">
    <div
      class="flex flex-wrap items-center justify-between gap-4 pb-1 lg:mb-2 lg:pb-6"
    >
      <h6 class="h4">{{ "chooseOneOfTheFollowingConditions" | translate }}</h6>
      <!-- <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
      </div> -->
    </div>
    <div
      class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6"
    >
      <p class="h4">
        {{ "toBecomeQualifiedInvestorInTheFintech" | translate }}
      </p>
      <div class="flex flex-wrap md:flex-nowrap items-center gap-4"></div>
    </div>
    @for(reason of QualificationReason | enum; track reason; let i = $index){
    <div class="flex flex-column gap-3">
      <div class="field-checkbox mt-4" >
        <label for="reason{{ i }}" class="ml-2">
          <input
            id="reason{{ i }}"
            name="reason"
            class="radio_animated"
            type="radio"
            [value]="reason[0]"
            (change)="updateQualificationReason(reason[0])"
            [checked]="qualification.qualificationReason === reason[0]"
            required
          />
          {{ getLabel(reason[1]) }}
        </label>
      </div>
    </div>
    }
  </div>
</div>
