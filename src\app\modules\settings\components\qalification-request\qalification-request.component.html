<div class=" p-4 font-cairo">
    <div class="grid grid-flow-row-dense grid-cols-3 ">
        <div class="col-span-2"> <h1 class="font-semibold text-3xl text-[#222E48]">{{ 'Upgradetoqualifiedinvestor' | translate }}</h1></div>
       
        <div class="mt-4"><span class="justify-end items-end mx-16 my-3"
            [ngClass]="{' border-orange-500 text-orange-500 px-9 py-1 border-2 rounded-3xl dark:border-orange-500': statusName == 'Waiting',
            'bg-secondary-100 border-rose-800 text-rose-800 px-9 py-1 border-2 rounded-3xl dark:border-rose-800': statusName == 'Rejected',
}">{{statusName |translate}}</span></div>
    
      </div>
  

<p class="border-b-2 py-4 border-dashed border-[#015C8E]"></p>
@if(isRejected){
  <h3 class="font-semibold text-xl py-4 ">{{'qualificationRejectRequest' | translate}}</h3>
  <div class="border-2  h-48 rounded-xl p-16  my-8 bg-red-100">
    <p class="text-base font-semibold text-center text-[#8E8E8E]">{{'yourQualificationRequestRejected' | translate}}</p>
  </div>
}@else{
  <h3 class="font-semibold text-xl py-4 ">{{'selectmethod' | translate}}</h3>
  <span class="font-medium text-base text-[#8E8E8E]">{{ 'canselectminchoice' | translate }}</span>
}
<div [formGroup]="qualificationRequestForm" (ngSubmit)="onSubmit()">
@for (reason of QualificationReason | enum; track reason; let i = $index) {
    <div class="flex flex-column gap-3">
        <div class="field-checkbox mt-4">
            <label for="reason{{ i }}" class="ml-2">
                <ul>
                    <li
                        class="border-2 text-base rounded-3xl border-[#015C8E1A] text-justify py-8 px-4 w-[632px] h-24"
                        [ngClass]="{'selected-background': isSelected(+reason[0])}"
                        (click)="!(qualificationRquest?.data?.length>0)&& selectReason(+reason[0])">
                        {{ getLabel(reason[1]) }}
                    </li>
                </ul>
            </label>
        </div>
    </div>
}

<div>
    <h2 class="text-base font-semibold mt-8">{{"pleaseattachmentfile" | translate}}</h2>
    <div class="border-2 w-[632px] h-56 rounded-xl p-16  my-8">
      @if(!(qualificationRquest?.data?.length>0)){
        <p class="">{{ "shouldattachmentupload" |translate}}</p>

        <div class="flex mx-32 my-4">
        <app-file-upload [control]="qualificationRequestForm.get('Files')" [isConvertToBase64]="false"  [placeholder]="'attachmentFile'" class="dark:bg-inherit"/>
        <img src="../../../../../assets/images/File.svg" class="-mx-10" />
      </div>
      }@else {
        <img [src]="qualificationRquest?.data[0]?.attachments[0]?.path" class="w-24 h-24 mx-auto rounded-md" alt="uploaded_img">
      }
</div>
</div>

<div class="mt-8 flex justify-end">
  @if(qualificationRquest?.data?.length>0){
    @if(isRejected){
      <button (click)="createNewRequest()" type="submit" class=" py-2 border border-blue-500 text-white bg-[#015C8E]  px-16 rounded-[32px] flex items-end justify-end">

        {{"qualificationRejectBtnRequest" | translate }}
      </button>
    }
  }@else {
    <button (click)="onSubmit()" type="submit" class=" py-2 border border-blue-500 text-white bg-[#015C8E]  px-16 rounded-[32px] flex items-end justify-end">

      {{"send" | translate }}<img src="../../../../../assets/images/Send.svg"  class="text-blue-500 mx-3"/>
    </button>
  }


  </div>
</div>
</div>
@if (modalService.isOpen('otp-modal')) {
    <app-otp-modal
    [title]="'confirmprocess'"
      (submitOtp)="postQualificationRequest($event)"
    />
  }
