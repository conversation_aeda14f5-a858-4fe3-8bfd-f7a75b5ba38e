<ng-modals [id]="id" [placement]="'modal-top'"
           className="fixed flex flex-col bg-gray-100  transition-all md:w-[30rem] duration-300 ease-in-out left-2/4 z-50 -translate-x-2/4 translate-y-8">
        @if(!errorMessage){

          <div class="bg-white rounded-lg shadow-lg p-6 w text-center">
           <h2 class="text-lg font-bold text-gray-900">{{title |translate}} <span class="text-green-600">{{'Nafath'|translate}}</span></h2>
           <p class="text-gray-500 mt-2">{{subTitle|translate}}<p>
           <div class="mt-4 relative flex items-center justify-center">
               <span class="text-2xl font-bold bg-gray-200 w-[50px] h-[50px] rounded-full  py-2">{{code}}</span>
           </div>
           <p class="bg-green-100 text-green-800 mt-4 py-2 px-4 rounded-md font-medium">
             {{hint|translate}}
           </p>
       </div>
        }@else {
          <div class="bg-red-300 text-red-700 rounded-lg shadow-lg p-6 w text-center">
            {{errorMessage}}
          </div>
        }

</ng-modals>
