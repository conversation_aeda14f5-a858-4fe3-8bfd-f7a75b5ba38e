<div class="p-6 space-y-6">
  <!-- Messages Container -->
  <div class="h-[600px] overflow-y-auto p-4 bg-white rounded-xl shadow-md">
    <!-- Header -->
    <h2 class="text-xl font-bold mb-2">{{ "Comments" | translate }}</h2>
    <div class="border-t border-dashed border-gray-300 mb-4"></div>

    <!-- Messages List -->
    <div class="space-y-4">
      <ng-container *ngFor="let msg of messages">
        <div class="flex" [ngClass]="msg.sender === 'admin' ? 'justify-end' : 'justify-start'">
          <div [ngClass]="msg.sender === 'admin'
              ? 'bg-gray-100 text-right'
              : 'bg-primary-50 text-right'"
              class="rounded-xl px-4 py-3 text-sm w-fit max-w-lg relative">
            <div class="font-semibold text-black mb-1">{{ msg.senderName }}</div>
            <div class="text-primary-800">{{ msg.text }}</div>
            <div class="text-[11px] text-gray-400 mt-1">{{ msg.date }}</div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Comment Input -->
  <div class="p-4 border-t bg-white rounded-xl shadow-md">
    <div class="flex items-center gap-4 mb-3">
      <input type="checkbox" [(ngModel)]="isPrivate" id="isPrivate" />
      <label for="isPrivate">{{ "Is the comment private" | translate }}</label>
    </div>

<div class="relative flex items-center rounded-full border border-gray-200 px-12 pl-4 py-2 text-sm text-right">
  <!-- Send Button - Allow pointer events -->
  <div class="absolute left-3 flex space-x-3 rtl:space-x-reverse cursor-pointer">
    <button type="button" class="text-primary-600 hover:text-primary-800" (click)="sendComment()">
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M3.4,20.6L20.6,12L3.4,3.4L3.4,10L17,12L3.4,14L3.4,20.6Z" />
      </svg>
    </button>
  </div>

  <!-- Mention Input - Prevent pointer events on the container -->
  <div class="mention-container relative overflow-visible pointer-events-none w-full">
    <input
      [(ngModel)]="newMessage"
      [mention]="items"
      mentionLabelKey="label"
      mentionValueKey="label"
      [mentionConfig]="{triggerChar: '@', labelKey: 'label'}"
      (keyup)="extractMentionIds()"
      (keydown.enter)="sendComment()"
      class="w-full text-gray-800 placeholder-gray-400 bg-transparent py-4 pointer-events-auto"
      [placeholder]="'Write The Comment Here' | translate"
    />
  </div>
</div>

    
  </div>
</div>
