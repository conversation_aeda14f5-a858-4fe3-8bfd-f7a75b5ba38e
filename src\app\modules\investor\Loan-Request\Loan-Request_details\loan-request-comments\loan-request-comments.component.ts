import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputComponent } from '@component/form/input/input.component';

import { MentionModule } from 'angular-mentions';

// import { UserModel, UserSearch } from '../users/models/user.model';
import { LoanRequestService } from '../../Service/loan-request.service';
import { ToastService } from '../../../../../core/services/toast.service';
import { UserService } from '../../../../../core/services/user.service';
import { ActivatedRoute } from '@angular/router';
import { EncryptStorage } from 'encrypt-storage';
import { AuthService } from '../../../../auth/services/auth.service';


interface Message {
  sender: 'admin' | 'client';
  senderName: string;
  text: string;
  date: string;
}
@Component({
  selector: 'app-loan-request-comments',
  standalone: true,
  imports: [TranslateModule, CommonModule, FormsModule, MentionModule],
  templateUrl: './loan-request-comments.component.html',
  styleUrl: './loan-request-comments.component.css'
})
export class LoanRequestCommentsComponent implements OnInit {
   @Input() loanRequestId!: string;
   resComment!: string;
   profile:any
profileData:any
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(
    private authService: AuthService,
    private toastr: ToastService,
    private translate: TranslateService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute) {
  }


  mentionTrigger = '@';
  mentionStartIndex: number | null = null;
  showMentionList = false;
  selectedMentionIndex = 0;
  items: any[] = []
  filteredUsers: string[] = [];
  // search: UserSearch = { isServerMode: true } as unknown as UserSearch;
  users: any
  @ViewChild('inputBox') inputBox!: ElementRef<HTMLInputElement>;

  @Input() id: any;
  isPrivate: boolean = false
  userId: any
  userName: any
  newMessage = '';
  messages :Message[] =[]
  comments: any
  mentionedUserIds: number[] = [];

  async ngOnInit() {
 
       this.route.params.subscribe((params:any) => {
              this.loanRequestId=params['id'];
              ;})
             
   
  }


async getLoanRequestComment() {
  try {
     this.comments = await this.loanRequestService.getLoanRequstComment(this.loanRequestId)
    
    
    this.comments = this.comments.data
   this.messages = this.comments.map((comment: any) => {
      let senderType = '';
  

      return {
        sender: senderType,
        senderName: comment.userFullName ?? 'known',
        text: comment.content,
        date: new Date(comment.createdAt).toLocaleString('en-GB'),
      };
    });

} catch (error) {
  console.error(error);
}

}

  async sendComment() {
    if (!this.newMessage) {
      this.toastr.error(this.translate.instant('Please enter a comment.'));
      return;
    }
    try {
  
      const requestBody = {
        loanRequestId: this.loanRequestId,
     
        content: this.newMessage,
   
      };
      const response = await this.loanRequestService.SendLoanRequstComment(requestBody);
   
      
      this.resComment = String(response);
    
      this.toastr.success(this.translate.instant('Comment sent successfully.'));
     this.getLoanRequestComment()
      // if (res.statusCode === 200) {
      //   this.messages.push({
      //     sender: 'admin',
      //     senderName: this.userName,
      //     text: this.newMessage,
      //     date: new Date().toLocaleString('en-GB'),
      //   });
      //   this.newMessage = '';
      //   this.isPrivate = false;
      // } else {
      //   this.toastr.error(this.translate.instant('Failed to send comment.'));
      // }
    } catch (error) {
      this.toastr.error(this.translate.instant('An error occurred: ') + error);
    }
  }

onMentionSelect(event: any) {
  const user = event?.item;
  if (user?.id && !this.mentionedUserIds.includes(user.id)) {
    this.mentionedUserIds.push(user.id);
  }
}

extractMentionIds() {
  this.mentionedUserIds = [];

  for (const item of this.items) {
    const mentionTag = `@${item.label}`;
    if (this.newMessage.includes(mentionTag)) {
      this.mentionedUserIds.push(item.id);
    }
  }
}
}
