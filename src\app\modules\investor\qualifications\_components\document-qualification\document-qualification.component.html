<div class="col-span-12">
    <div class="box col-span-12 lg:col-span-6">
      <div class="flex flex-wrap items-center justify-between gap-4 pb-1 lg:mb-2 lg:pb-6">
        <h6 class="h4">{{ 'chooseOneOfTheFollowingConditions' | translate }}</h6>
        <!-- <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
        </div> -->
      </div>
      <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6">
        <p class="h4">  {{'toBecomeQualifiedInvestorInTheFintech'|translate}}</p>
        <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
        </div>
      </div>
    </div>
  </div>
<div class="col-span-12">
    <div class="box col-span-12 lg:col-span-6">
      <div class="flex flex-wrap items-center justify-between gap-4 pb-1 lg:mb-2 lg:pb-6">
        <ngx-dropzone (change)="onSelect($event)" [multiple]="true" class="dropzone">
            <ngx-dropzone-label>
            

          
              <div class="dz-message needsclick">
                <div class="docs-upload">
                <i class="las la-upload"></i>
                <h6>{{ "pleaseAttachTheDocument" | translate }}</h6>
              </div>
                <span>{{ "attachDocumentThatFulfillsTheRequirements" | translate }}</span>
                <p>{{ "filesSupported" | translate }}</p>
              </div>
            </ngx-dropzone-label>
          
            <!-- Display previews of selected files -->
            <ngx-dropzone-preview *ngFor="let f of files" [removable]="true" (removed)="onRemove(f)">
              <ngx-dropzone-label>
                <div *ngIf="f.type.startsWith('image')">
                  <img [src]="f.dataUrl" [alt]="f.name" style="max-width: 150px; max-height: 150px; object-fit: cover;" />
                </div>
                <div *ngIf="!f.type.startsWith('image')">
                  {{ f.name }} ({{ f.type }})
                </div>
              </ngx-dropzone-label>
            </ngx-dropzone-preview>
          </ngx-dropzone>
         
      </div>
   
    </div>
  </div>
