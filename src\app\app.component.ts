import { EncryptStorageService } from './modules/auth/services/encrypt-storage.service';
import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {NavigationStart, Router, RouterOutlet} from '@angular/router';
import {LangChangeEvent, TranslateModule, TranslateService} from "@ngx-translate/core";
import {NgOptimizedImage} from "@angular/common";
import {LoadingBarModule} from "@ngx-loading-bar/core";
import {MatTooltip} from "@angular/material/tooltip";
import {UserService} from "./core/services/user.service";
import { AuthService } from './modules/auth/services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { filter } from 'rxjs';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, TranslateModule, LoadingBarModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  title = 'MDDPlusInvestor';
  loading = true;
  private routerSubscription: any;

  constructor(private router: Router,private toast:ToastrService,private translate: TranslateService,
     private userService: UserService, private cdRef: ChangeDetectorRef,private authService:AuthService) {
    // the lang to use, if the lang isn't available, it will use the current loader to get them
  }

  ngOnInit() {
    this.routerSubscription = this.router.events.pipe(
      filter((event): event is NavigationStart => event instanceof NavigationStart)
    ).subscribe((event) => {
    if (this.authService.isTokenExpired) {
      this.authService.logout();
      this.toast.error(this.translate.instant('SessionExpired'));
      }
    });
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this.translate.use(event.lang);
    });
    this.translate.use(localStorage.getItem('dir') === 'ltr' ? 'en' : 'ar');
    this.userService.getCurrentUser().finally(() => { this.loading = false});
  }



ngOnDestroy(): void {
  if (this.routerSubscription) {
    this.routerSubscription.unsubscribe();
  }
}
}
