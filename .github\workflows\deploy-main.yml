name: Pushes changes on Merge of PRs
on:
  pull_request:
    types: [closed]
    branches:
          - main

jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest

    steps:
    # Step 1: Checkout the latest code
    - name: 🚚 Get latest code
      uses: actions/checkout@v2

    # Step 2: Set up Node.js environment (adjust version as needed)
    - name: 🔧 Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20' # or the version required by your project

    # Step 3: Install dependencies
    - name: 📦 Install dependencies
      run: npm install --force

    # Step 4: Build the application
    - name: 🛠 Build the app
      run:  npm run build

    # Step 5: Copy built files to the server via SSH
    - name: 📤 Copy files via SSH
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.FTP_PRODUCTION_SERVER }}
        username: ${{ secrets.FTP_PRODUCTION_USERNAME }}
        port: ${{ secrets.FTP_PRODUCTION_PORT }}
        password: ${{ secrets.FTP_PRODUCTION_PASSWORD }}
        source: "./dist/mdd-plus-investor/"
        target: "/var/www/test-investor.mdd.sa"
        strip_components: 3
