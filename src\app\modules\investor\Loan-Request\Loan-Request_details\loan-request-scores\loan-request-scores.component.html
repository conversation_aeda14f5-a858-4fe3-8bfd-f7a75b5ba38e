
<div class="bg-white dark:bg-inherit shadow rounded-lg p-6 mt-6">
  <h2 class="text-xl font-bold mb-4">{{ "Financingagreement" | translate }}</h2>
<hr class="border-t-2 border-dashed border-gray-300 mt-6 mb-6" />

  <!-- Cards Grid  *ngIf="requestStatus == 5" -->


  <div class="grid grid-cols-3 gap-4">
    <ng-container *ngFor="let card of cards; let i = index">
      <div class="min-h-[160px] p-6 rounded shadow text-center flex flex-col justify-center bg-primary/5">
        <h2 class="text-lg font-semibold mb-3">{{ card.title | translate }}</h2>
        <hr class="border-t-2 border-dashed border-gray-300 mb-3" />
        <ng-container *ngIf="card.title === 'AnnualYield'; else normalValue">
          {{ card?.body }}%
        </ng-container>
        <ng-template #normalValue>
          {{ mapValue(card?.body) | translate }}
        </ng-template>
      </div>
    </ng-container>
  </div>

  <!-- Dashed Line -->
  <hr class="border-t-2 border-dashed border-gray-300 mt-6" />

  <!-- Main Table -->
  <div class="mt-5 overflow-x-auto flex justify-center">
    <div class="w-full">
      @if(loanRequest!=null) {



      <div class="datatable" >
        <ng-datatable
        (changeServer)="handePageChange($event.current_page)"
          [columns]="columns"
          [isServerMode]="false"
        [loading]="isLoading"
          [page]="LoanRequestService.pagination.page"
         [totalRows]="totalRecords"
          [pageSize]="LoanRequestService.pagination.pageSize"
          [rows]="apiData"

          [showPageSize]="false"
           [pageSize]="15"
            [pageSizeOptions]="[15]"

          class="whitespace-nowrap table-hover"
          firstArrow='<svg width="24" height="24"...></svg>'
          lastArrow='<svg width="24" height="24"...></svg>'
          nextArrow='<svg width="24" height="24"...></svg>'
          previousArrow='<svg width="24" height="24"...></svg>'
        >


          <ng-template let-value="data" slot="amount">{{ value.amount }}</ng-template>
          <ng-template let-value="data" slot="profit">{{ value.profit }}</ng-template>
        <ng-template let-value="data" slot="dueDays">{{ value.dueDate |dateFormat}}</ng-template>

        </ng-datatable>
      </div>



            @if(loanRequest?.requestStatus == 9) {
            <!-- <div class="flex justify-end items-end">
              <button
                class="border-green-600 rounded-3xl my-8 mx-4 px-11 py-2 text-green-600 border-2"
                (click)="ApprovalscoreloanRequest('accept')">
                {{"accept" |translate}}
              </button>

              <button
                class="border-red-600 text-red-600 rounded-3xl my-8 border-2 px-11 py-2"
                (click)="ApprovalscoreloanRequest('reject')">
                {{"reject" |translate}}
              </button>
            </div> -->
      <div class="flex justify-end items-end">
  <button
    class="border-green-600 rounded-3xl my-8 mx-4 px-11 py-2 text-green-600 border-2"
    (click)="ApprovalscoreloanRequest('accept')">
    {{"accept" |translate}}
  </button>

  <button
    class="border-red-600 text-red-600 rounded-3xl my-8 border-2 px-11 py-2"
    (click)="ApprovalscoreloanRequest('reject')">
    {{"reject" |translate}}
  </button>
</div>
            }}
   @else {
      <div class="flex justify-center items-center h-64">
        <p class="text-gray-500">{{ "NoDataAvailable" | translate }}</p>
      </div>
    }

    </div>
  </div>

</div>

@if (modalService.isOpen('otp-change-password-modal')) {
  <app-otp-modal
    [id]="'otp-change-password-modal'"
    (submitOtp)="submitAccptance($event)"
  />
}



