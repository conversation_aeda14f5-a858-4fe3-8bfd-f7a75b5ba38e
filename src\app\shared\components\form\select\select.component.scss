label {
  @apply inline-block mb-2 text-base font-medium;
}


.ng-select {
  .ng-select-container {
    @apply bg-white p-0 form-input  rounded-3xl border-slate-200 dark:border-zink-500
    dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100
    placeholder:text-slate-400 dark:placeholder:text-zink-200;

    .ng-value-label {
      @apply text-slate-500 dark:text-zink-200 #{!important};
    }
  }
}

.ng-dropdown-panel {
  @apply border border-n30 cursor-pointer dark:bg-bg3 dark:border-n500  #{!important};

  .ng-dropdown-panel-items {
    .ng-option {
      @apply dark:bg-bg3 border-slate-200 text-slate-500 dark:text-zink-200 dark:border-zink-500;

      &.ng-option-marked {
        @apply text-slate-700 dark:text-zink-50 #{!important};
      }
    }
  }
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
  @apply text-slate-700 dark:text-zink-50 bg-white dark:bg-zink-700 #{!important};
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input > input {
  @apply text-slate-700 dark:text-zink-50 md:px-6 md:py-3 px-3 py-2 #{!important};
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  @apply bg-slate-100 dark:bg-zink-600 #{!important};
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  @apply bg-slate-100 dark:bg-zink-600 text-current #{!important};
}


.ng-select.ng-select-opened > .ng-select-container {
  @apply bg-white dark:bg-zink-700 border-slate-200 dark:border-zink-500 #{!important};
}

.ng-select.ng-select-disabled > .ng-select-container {
  @apply bg-slate-100 dark:bg-zink-600 #{!important};
}


.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
  @apply bg-primary-500 text-white border-primary-500 md:px-6 md:py-3 px-3 py-2  #{!important};

  .ng-value-label {
    @apply bg-primary-500 text-white border-primary-500 #{!important};
  }
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon:hover {
  @apply bg-primary-500 #{!important};
}

.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  @apply top-1/2 -translate-y-1/2 md:px-6 md:py-3 px-3 py-2 #{!important};
}

.ng-select.ng-select-focused > .ng-select-container {
  @apply border border-n30 cursor-pointer dark:bg-bg3 bg-primary/5 dark:border-n500  #{!important};
}

.ng-select .ng-select-container {
  @apply h-[46px] min-h-[46px] bg-primary/5  border border-n30 cursor-pointer dark:bg-bg4 dark:border-n500  flex gap-2 items-center justify-between lg:py-3 md:text-base px-3 py-1.5  select-none sm:px-4 sm:py-2 text-sm w-full #{!important};
}
.ng-select .ng-select-container:hover {
  @apply shadow-none dark:bg-bg4 #{!important};
}

.ng-select.error .ng-select-container {
  @apply border-danger bg-danger/[0.08]  placeholder-danger/70 focus:border-danger;
}

.ng-select.error .ng-select-container .ng-placeholder {
  @apply text-danger #{!important};
}



