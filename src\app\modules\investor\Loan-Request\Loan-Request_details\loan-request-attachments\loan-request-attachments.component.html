<div class="grid grid-flow-row-dense grid-cols font-cairo">

    <div class="container-fluid">

        <div class="box mb-4 xxxl:mb-6 m-2">
            <div class="card  m-4">
                <div class="col-span-3">
                    <div class="p-4 ">

                        <p class="text-3xl font-bold border-b-2 border-[#015C8E] border-dashed py-3">
                            {{"additionalattachments" | translate }}</p>

                        <div class="mt-5 overflow-x-auto">

                            <div class="w-full">
                                <div class="datatable">
                                    <ng-container *ngIf="LoanRequstAttachmentsData?.length > 0; else noData">
                                        <ng-datatable [columns]="columns" [isServerMode]="true" [loading]="false"
                                            [page]="LoanRequestservice.pagination.page"
                                            [pageSize]="LoanRequestservice.pagination.pageSize"
                                            [rows]="LoanRequstAttachmentsData" [showPageSize]="false" [totalRows]="10"
                                            class="whitespace-nowrap table-hover"
                                            firstArrow='<svg width="24" height="24"...></svg>'
                                            lastArrow='<svg width="24" height="24"...></svg>'
                                            nextArrow='<svg width="24" height="24"...></svg>'
                                            previousArrow='<svg width="24" height="24"...></svg>'>
                                            <!-- Your existing ng-template definitions here -->
                                            <ng-template let-value="data" slot="filePath">
                                                <div class="relative inline-block group">
                                                    <img *ngIf="isImageUrl(value.filePath)" [src]="value.filePath"
                                                        class="h-10 w-10 object-cover rounded cursor-pointer"
                                                        [title]="value.filePath" (click)="previewFile(value.filePath)">
                                                    <span *ngIf="!isImageUrl(value.filePath)"
                                                        class="text-blue-500 underline cursor-pointer"
                                                        [title]="value.filePath" (click)="previewFile(value.filePath)">
                                                        View File
                                                    </span>
                                                    <div
                                                        class="absolute z-10 hidden group-hover:block bg-gray-800 text-white text-xs p-2 rounded mt-1">
                                                        {{value.filePath}}
                                                    </div>
                                                </div>
                                            </ng-template>
                                            <ng-template let-value="data" slot="fileType">{{ value.fileType }}
                                            </ng-template>
                                            <ng-template let-value="data" slot="requestDate">
                                                {{value.requestDate | date: 'yyyy-MM-dd'}}
                                            </ng-template>
                                            <ng-template let-value="data" slot="Actions">
                                                <div class="flex justify-center">
                                                    <span class="underline"
                                                        (click)="previewFile(value.filePath)">{{"Show" |translate}}
                                                        /</span>
                                                    <input type="file" (change)="onFileSelected($event, value.id)"
                                                        style="display: none" #fileInput>
                                                    <span class="underline cursor-pointer" (click)="fileInput.click()">
                                                        {{"UploadFile" |translate}}
                                                    </span>
                                                </div>
                                            </ng-template>
                                        </ng-datatable>
                                    </ng-container>

                                    <ng-template #noData>
                                        <div class="text-center py-4 text-gray-500">
                                         {{ "NoDataAvailable" | translate }}
                                        </div>
                                    </ng-template>
                                </div>
                            </div>


                        </div>
                        @if(LoanRequstAttachmentsData?.length > 0) {

                        <div class="flex justify-end items-end">
                            <button (click)="sendRequest(LoanRequstAttachmentsData[0]?.id)" class="border-primary rounded-3xl my-8 mx-4  px-11 py-2   text-green-600 border-2">
                                {{"save" |translate}}
                            </button>


                        </div> }

                    </div>

                </div>


            </div>
        </div>
    </div>


</div>