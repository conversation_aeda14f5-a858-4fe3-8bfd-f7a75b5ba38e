{"Add": "Add", "Dashboard": "Dashboard", "WithdrowsList": "Withdraws List", "CreateNew": "Create New", "CreateWithdraw": "Request withdraw", "WithdrawAmount": "Withdraw Amount", "IBAM": "IBAN", "Bank": "Bank", "Back": "Back", "AccountDetails": "Account Details", "Notes": "Notes", "TransactionNo": "Transaction No", "TransactionDate": "Transaction Date", "Status": "Status", "companydetails": "Company Details", "Search": "Search", "DepositsList": "Deposits List", "MyTransactions": "My Transactions", "Title": "Title", "CreatedOn": "Created On", "CreatedBy": "Created By", "DueDate": "Due Date", "dueDays": "Due Days", "TransactionsList": "Transactions List", "DepositCreate": "Deposit Create", "PaymentRequests": "Payment Requests", "Actions": "Actions", "Details": "Details", "CreatingOpportunity": "Creating Opportunity", "Delete": "Delete", "MyInvestments": "My Investments", "InvestmentsList": "Investments List", "OpportunityNo": "Opportunity No", "InvestDate": "Invest Date", "Profits": "Profits", "ProfitsDueDate": "Profits Due Date", "OpenOpportunities": "Open Opportunities", "OpportunitiesList": "Opportunities List", "Id": "Id", "OrderNo": "Order No", "Peroid": "Per<PERSON>", "YourBalance": "Your Balance", "UpcomingBalance": "Upcoming Balance", "TotalInvestments": "Total Investments", "TotalProfits": "Total Profits", "CurrnetInvstments": "Currnet Invstments", "CurrentProfits": "Current Profits", "LastInvestments": "Last Investments", "AdminPanel": "Admin Panel", "Account": "Account", "Settings": "Settings", "Logout": "Logout", "orderDate": "Order Date", "walletbalance": "Wallet Balance", "yourDepositRequestHasBeenSentSuccessfully": "Your Deposit RequestHasBeen Sent Successfully", "CreateWithdrawDesc": "From this form, you can request to withdraw your balance of money.", "WithdrawsList": "Withdraws List", "WithdrawsListDesc": "", "Amount": "Amount", "MyTransactionsDesc": "this list contain all Transactions.", "SR": "SAR", "List": "List", "OpportunityDetail": "Opportunity Detail", "OpportunityAmount": "Opportunity Amount", "ReturndProfits": "Returnd Profits", "profitPercentage": "Profit Percentage", "Invest": "Invest", "OpenOpportunitiesDesc": "", "MyInvestmentsDesc": "All your investments list", "NewOpportunity": "New Opportunity", "Summary": "Summary", "ReturnPeroidDays": "Return Investment Period (Days)", "ReturnPeroid": "Return Period", "ReturnProfitsPer": "Return Profits (Percentage)", "ReturnProfits": "Return Profits", "ExpiryDate": "Expiry Date", "AllManyInvestments": "Allow many investments for this opportunity", "MinInvestment": "Min amount for each investment", "OpportunitiesListDesc": "", "TransactionsListDesc": "", "InvestorName": "Investor Name", "OrderDetails": "Order Details", "Qty": "Qty", "Price": "Price", "Total": "Total", "NewInvestment": "New Investment", "YourInvestmentAmount": "Your Investment Amount", "YourRetuenedProfits": "Your Retuened Profits", "Confirm": "Confirm", "Close": "Close", "CollectingFunds": "Collecting Funds", "AwaitingInternalApproval": "Awaiting Internal Approval", "AwaitingClientApproval": "Awaiting <PERSON><PERSON>", "Users": "Users", "users": "Users", "investors": "Investors", "UsersListDesc": "", "FullName": "FullName", "email": "Email", "MobileNo": "Mobile No", "IdNumber": "National Identity/Iqama", "birthDateCalnder": "Date Type", "birthDate": "Birth Date", "Type": "Type", "userType": "User Type", "Gregorian": "<PERSON><PERSON>", "Hijri": "<PERSON><PERSON><PERSON>", "InvestmentAmount": "Investment Amount", "InvestmentDetailsDesc": "", "InvestmentDetails": "Investment Details", "Deposits": "Deposits", "SignInToAccount": "Sign in to account", "SignInToAccountDesc": "Enter your email & password to login", "EmailRequired": "Email is required", "InvalidEmail": "<PERSON><PERSON><PERSON>", "Password": "Password", "PasswordRequired": "Password is required", "ForgotPassword?": "Forgot password ?", "Login": "<PERSON><PERSON>", "DontHaveAccount": "Don't have account ?", "CreateAccount": "Create Account", "PayNow": "Pay now", "CancelInvestmentdesc": "The amount was invested", "CancelInvestment": "Cancel Investment", "AreYouSure": "Are You Sure", "confirmCancelInvestment": "Confirm Cancel Investment", "confirmInvestment": "Confirm Investment", "CreateDeposit": "Create deposit", "CreateWithdrawal": "Create withdrawal", "EnterTransactionNo": "Enter transaction no", "UploadTransactionFile": "Upload vochar", "DepositsListDesc": "", "CreateDepositDesc": "", "Date": "Date", "Hyjrydate": "Hyjry Date", "InvestmentAmountMinimum": "Investment Amount Minimum 1000", "InvestmentAmountRequired": "Investment Amount Required", "AmountMustBeExactly": "Amount Must Be Exactly", "InsufficientBalance": "Insufficient Balance", "EnterTransactionDate": "Enter transaction date", "Withdraws": "Withdraws", "MinInvestmentAmount": "Min investment amount", "InvestmentPeriod": "Investment period", "ErrorTitle": "<PERSON><PERSON>", "ErrorMessage": "Error occured, please try again later", "Operations": "Operations", "ApprovedWithdrows": "Approved Withdraws", "PendingWithdrows": "Pending Withdraws", "ApprovedDeposits": "Approved Deposists", "PendingDeposits": "Pending Deposists", "MyMenu": "My Menu", "DepositsRequests": "Deposit Requests", "WithdrawsRequests": "Withdraw Requests", "Opportunities": "Opportunities", "Investments": "Investments", "Transactions": "Transactions", "Companies": "Companies", "Configrations": "Configrations", "MyDeposits": "My Deposits", "MyWithdraws": "My Withdraws", "ApproveDeposit": "Approve Deposit", "Approve": "Approve", "Attachment": "Attachment", "DepositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "ErrorDepositAmount": "Error De<PERSON>t Amount is not correct!", "ErrorWithdrawAmount": "Error withdraw amount is not correct!", "FormNotValid": "The entered values are not correct!", "MFACode": "Verfication Code", "bankAccountNotExists": "You can not request withdraws, your bank account is not setup!", "User": "User", "Investor": "Investor", "Investors": "Investors", "NewUser": "New User", "PageUnderConstraction": " This Page is under constraction", "CurrentPassword": "Current Password", "NewPassword": "New Password", "ConfirmPassword": "Confirm Password", "ConfirmNewPassword": "Confirm New Password", "Save": "Save", "ChangePassword": "Change Password", "Profile": "Proile", "BankName": "Bank Name", "IBAN": "IBAN", "AccountNo": "Account No", "BankAccount": "Bank Account", "bankAccountstatment": "Bank Account Statment", "ApprovedWithdraws": "Approved Withdraws", "Period": "Period", "Day": "Day", "Pay": "Pay", "Cancel": "Cancel", "EnterBirthDate": "Enter BirthDate", "DepositRequestDeleted": "Deposit request is deleted", "withdrawRequestDeleted": "Withdraw Request is Deleted", "OpportunityDeleted": "Opportunity is Deleted", "PasswordChanged": "Your Password has been changed", "WithdrawDeleted": "Withdraw is deleted", "InvestmentDeleted": "Investment is Deleted", "InvalidCredentials": "Invalid Credentials", "AccountIsNotActivated": "Your account is not activated", "UserAccounts": "User Accounts", "UserAccountAdded": "User Account has been added", "UserAccountUpdated": "User Account has been updated", "UserCreated": "User has been created", "UserUpdated": "User has been updated", "UserDeleted": "User has been deleted", "AdminApproval": "Your approval has been sent successfully.", "PayRequest": "Your pay has been sent successfully.", "WithdrawRequest": "Your withdraw Request has been sent successfully.", "OpportunitiesOpen": "Open", "OpportunitiesProgress": "Progress", "OpportunitiesCompleted": "Completed", "OpportunitiesExpired": "Expired", "Expired": "Expired", "Open": "Open", "Progress": "Progress", "Completed": "Completed", "DeleteConfirmedTitle": "Please Confirm", "DeleteConfirmedBody": "Do you really want to delete this record?", "CancelConfirmedBody": "Do you really want to cancel this record?", "BalanceNotEnoughTitle": "Not Enough Balance", "BalanceNotEnoughBody": "You don't have enough balance in your account", "InvestmentsSummary": "Investments Summary", "Reports": "Reports", "InvestableAmount": "Open Amount", "TypeError": "Only PDF - JPG - PNG files are accepted", "SizeError": "Files that exceed 2 MB are not allowed", "InvestmentsMenu": "Investments List", "TransactionsMenu": "Transactions List", "PaidWithdraws": "<PERSON><PERSON>", "PayWithdraw": "Pay withdraw", "PayWithdrawDesc": "", "ApproveWithdraw": "Approve withdraw", "ApproveWithdrawDesc": "", "WithdrawPaid": "<PERSON><PERSON><PERSON>", "Approved": "Approved", "Closed": "Closed", "EmailDoesNotExist": "Email doesn't exist", "TransactionDetails": "Transaction Details", "ErrorDepositDate": "Transaction date format is not correct!", "Done": "Done", "ResetPasswordTitle": "Reset Your Password", "OTPSent": "OTP has been sent to Your Mobile ", "LoginDesc": "Login to Your Account", "GroupsListDesc": "", "permissionGroupNameAr": "Permission Group in Arabic", "permissionGroupNameEn": "Permission Group in English", "NewPermissionGroup": "New Permission Group", "PermissionGroupSuccess": "Permission Group has been created successfully", "EnglishName": "English Name", "ArabicName": "Arabic Name", "PermissionGroupDeleted": "Permission Group has been deleted successfully", "PermissionGroupUpdated": "Permission Group has been updated successfully", "Edit": "Edit", "Permissions": "Permissions", "GroupName": "Group Name", "Code": "Code", "NewPermission": "New Permission", "PermissionNameAr": "Permission Name in Arabic", "PermissionNameEn": "Permission Name in English", "PermissionGroup": "Permission Group Name", "PermissionSuccess": "Permission has been created successfully", "PermissionDeleted": "Permission has been deleted successfully", "AgreeOnTermsTitle": "Agree on Terms and Conditions", "AgreeOnTermsDesc": "I agree on terms and condition that are presented on this contract", "Submit": "Submit", "PersonalData": "Personal Information", "CloseOpportunity": "Close", "CloseOpportunityAndPay": "Close & Pay back", "CloseOpportunityMessage": "عند اغلاق الفرصة سيقوم النظام باغلاق جميع الاستثمارات واضافة المبالغ الاستثمارية مع الارباح الى محافظ المستثمرين.", "CloseOpportunityAndPayMessage": "عند اغلاق الفرصة سيقوم النظام باغلاق جميع الاستثمارات واضافة المبالغ الاستثمارية مع الارباح الى محافظ المستثمرين واصدار طلب سحب.", "Update": "Update Info", "UpdateBank": "Update Bank", "investmentPer": "Investments Per", "TransactionsSummary": "Transactions Summary", "AllInvestments": "All Investments", "AllWithdraws": "All Withdraws", "AllOpportunities": "All Opportunities", "AllDeposits": "All Deposits", "Opened": "Opened", "Cancelled": "Canceelled", "Waiting": "Waiting", "Paid": "Paid", "InvestorLast5Transactions": "Last 5 Transactions", "InvestorNextInvestments": "Investments Due Soon", "CurrentBalanceDesc": "Current Balance is: ", "InvestableAmountDesc": "Investable Amount is: ", "WrongInvestmentValue": "Investment Amount is not allowed", "UpComingBalance": "Upcoming Balance", "UpComingInvestments": "Upcoming Investments", "UpComingProfits": "Upcoming Profits", "UpComingBalanceDesc": "Upcoming Balance List", "UpComingInvestmentsDesc": "Upcoming Investments List", "UpComingProfitsDesc": "Upcoming Profits List", "ApproveOpportunity": "Approve Opportunity", "ApproveOpportunityConfitm": "Do you really want to approve this opportunity", "OpportunityApproved": "Opportunity Approved Successfully", "OK": "OK", "CancelInvesment": "Cancel Investment", "CancelOpportunity": "Cancel Opportunity", "CancelOpportunityConfirm": "Do you want really to cancel this opportunity", "OpportunityCannotBeCancelled": "This opportunity cannot be cancelled because it has investments", "OpportunityCancelled": "Opportunity has been cancelled", "ActualDueDate": "Actual Due Date", "DownloadAttachment": "Download Attachment", "RejectWithdraw": "Reject Withdraw", "RejectWithdrawDescription": "Confirm reject withdraw?", "RejectDepositDescription": "Confirm reject deposit?", "RejectRequest": "Withdraw Request has been rejected", "DepositRejectRequest": "Deposit Request has been rejected", "AveragePercentageForInvestments": "The average percentage of profits on your investments in the wallet", "is": "is", "Duration": "% for a Average of ", "DaysInDashboard": "Days ", "RejectDeposit": "Reject Deposit", "SystemLogs": "System Logs", "SystemLogsListDesc": "", "Username": "Username", "UserID": "User ID", "Data": "Data", "MethodName": "Method Name", "ExceptionMessage": "Exception Message", "dueSoonTransactions": "Due Soon Transactions", "Wallets": "Wallets", "walletName": "Wallet Name", "close": "Close", "update": "Update", "wallet": "Wallet", "somethingWentWrong": "Something Went Wrong", "createWallet": "Create Wallet", "name": "Wallet Name", "notes": "Notes", "add": "Add", "back": "Back", "yourNewListItsBeenCreated": "It's been created", "opps...": "Opps...", "createOn": "Created On", "exportToExcel": "Export To Excel", "walletList": "Wallet List", "editWallet": "Edit <PERSON>", "MyWallet": "My Wallet", "enterName": "Enter Wallet Name", "enterNotes": "Enter Notes", "thisFieldIsRequired": "This Field Is Required!!", "walletHasBeenUpdated": "<PERSON>et Has <PERSON> Updated", "CloseInvestment": "Close Investment", "DepositRejected": "<PERSON><PERSON><PERSON><PERSON> Rejected", "changePassowrd": "Change Passowrd", "creatBankAccount": "Creat Bank Account", "OpportunityClosed": "Opportunity Closed", "investmentClosed": "investment Closed", "enterTransactionFromDate": "Transaction Date From", "enterTransactionToDate": "Transaction Date To", "transcationCreatedOnFrom": "Transaction Date From", "transcationCreatedOnTo": "Transaction Date From", "WithdrawRejected": "Rejected", "waiting": "Waiting", "approved": "Approved", "depositRejected": "<PERSON><PERSON><PERSON><PERSON> Rejected", "ReturnPeroidPre": "Returnd Profits", "allStatus": "All Status", "withdrawRejected": "<PERSON><PERSON><PERSON> Rejected", "statusSearch": "status Search", "ViewDownloadFile": "View Attachment", "thisListContainAllDeposits": "This List Contain All Deposits", "thisListContainAllPendingDeposits": "This List Contain All Pending Deposits.", "InvestmentsListDesc": "This List Contain All Investments.", "returndProfits": "Returnd Profits", "newBalance": "New Balance", "transferBalance": "Transfer Balance", "walletBalance": "Wallet Balance", "fromWallet": "From Wallet", "toWallet": "To Wallet", "transfer": "Transfer", "transferBalanceSuccess": "Transfer Balance Success", "transferBalanceError": "Transfer Balance Error", "showStatistics": "Show Statistics", "hideStatistics": "Hide Statistics", "investmentNo": "Investment No.", "thereAreNoInvestmentsDueSoon": "There Are No Investments Due Soon", "thereAreNoTransactions": "There Are No Transactions", "officials": "Officials", "companies": "Companies", "createCompany": "Create Company", "companyName": "Company Name", "crStatus": "CR Status", "verified": "Verified", "verify": "Verify", "reset": "Reset", "companyInformation": "Company Information", "createWithdraw": "Create Withdraw", "crName": "CR Name", "crNumber": "CR Number", "crEntityNumber": "CR Entity Number", "crMainNumber": "CR Main Number", "cancellation": "Cancellation", "expiryDate": "Expiry Date", "issueDate": "Issue Date", "fiscalYear": "Fiscal Year", "day": "Day", "month": "Month", "calendarType": "Calendar Type", "companyPeriod": "Company Period", "period": "Period", "startDate": "Start Date", "endDate": "End Date", "businessType": "Business Type", "location": "Location", "companyStatus": "Company Status", "companyActivity": "Company Activity", "companyActivityAr": "Company Activity Ar", "companyActivityEn": "Company Activity En", "next": "Next", "previous": "Previous", "sendLoginInfo": "Send Login Info", "contactInformation": "Contact Information", "submit": "Submit", "theTransferAmountIsGreaterThanTheWalletBalance": "The Transfer Amount Is Greater Than The Wallet Balance", "COMPANY.CREATE.SUCCESS": "Company Created Successfully", "checkCRNumber": "Check CR Number", "fullName": "Full Name", "hide": "<PERSON>de", "show": "Show", "applicationName": "Application Name", "expired": "Expired", "tokens": "Tokens", "token": "Token", "createToken": "Create Token", "validity": "Validity", "TOKEN.CREATE.SUCCESS": "<PERSON><PERSON> Created Successfully", "durationOfTokenInSeconds": "Duration Of Token In Seconds", "durationOfTokenInMinutes": "Duration Of Token In Minutes", "copy": "Copy", "TOKEN.COPIED.SUCCESS": "<PERSON><PERSON> Copied Successfully", "UserBankAccountUpdated": "User Bank Account Updated", "UserBankAccountCreated": "User Bank Account Created", "roles": "Roles", "Roles": "Roles", "createRole": "Create Role", "updateRole": "Update Role", "roleName": "Role Name", "description": "Description", "ROLE.CREATED.SUCCESSFULLY": "Role Created Successfully", "ROLE.UPDATED.SUCCESSFULLY": "Role Updated Successfully", "PERMISSION.PERMISSION.REQUIRED": "Must Select At Least One Permission", "selectRoles": "Select Roles", "Banks": "Banks", "OTPs": "OTPs", "Notifications": "Notifications", "AccessTokens": "Access Tokens", "createUser": "Create User", "updateUser": "Update User", "walletsBalance": "Wallets Balance", "superAdmins": "Super Admins", "superAdminName": "Super Admin Name", "SuperAdmin": "Super Admin", "InvestmentDetail": "Investment Detail", "COMPANY.CHECKCR.PERMISSION_WARNING": "You Don't Have Permission To Check CR Number", "COMPANY.CREATE.PERMISSION_WARNING": "You Don't Have Permission To Create Company", "TOKEN.CREATE.PERMISSION_WARNING": "You Don't Have Permission To Create Token", "ROLE.CREATE.PERMISSION_WARNING": "You Don't Have Permission To Create Role", "ROLE.UPDATE.PERMISSION_WARNING": "You Don't Have Permission To Update Role", "USER.ACCOUNT.VIEW.PERMISSION_WARNING": "You Don't Have Permission To View User Account", "DEPOSIT.APPROVE.PERMISSION_WARNING": "You Don't Have Permission To Approve Deposit", "WITHDRAW.PAY.PERMISSION_WARNING": "You Don't Have Permission To Pay Withdraw", "CREATE.APPROVED.WITHDRAW.PERMISSION_WARNING": "You Don't Have Permission To Create Approved Withdraw", "CREATE.PAID.WITHDRAW.PERMISSION_WARNING": "You Don't Have Permission To Create Pa<PERSON>", "profileSettings": "Profile Settings", "RESET.EMAIL.SUCCESS": "A verification code has been sent to the new email", "CONFIRM.RESET.EMAIL.SUCCESS": "Email has been changed successfully", "RESET.MOBILE.SUCCESS": "A verification code has been sent to the new mobile", "CONFIRM.RESET.MOBILE.SUCCESS": "Mobile has been changed successfully", "newMobileNumber": "New Mobile Number", "newEmailAddress": "New Email Address", "submitOTP": "Submit OTP", "PasswordsNotMatch": "Passwords Not Match", "sendOTP": "Send OTP", "CreateBankAccount": "Create Bank Account", "OPPORTUNITY.CREATE.PERMISSION_WARNING": "You Don't Have Permission To Create Opportunity", "investorAccountStatement": "Investor Account Statement", "startBalance": "Start Balance", "endBalance": "End Balance", "startUpcomingBalance": "Start Upcoming Balance", "endUpcomingBalance": "End Upcoming Balance", "dueDateStart": "Start Due Date", "dueDateEnd": "End Due Date", "balance": "Balance", "createPaidWithdraw": "Create <PERSON><PERSON>", "createApprovedWithdraw": "Create Approved Withdraw", "investedAmountIsRequired": "Invested amount is required", "yourInvestmentIsLowerThanMinAmount": "Your investment is Lower than min amount", "yourInvestmentIsHigherThanTheRemainingAmountOfOpportunity": "Your investment is higher than the remaining amount of opportunity", "yourBalanceIsInsufficient": "Your Balance Is Insufficient", "verificationSent": "A verification code has been sent to your \n (phone number/email address).", "resendOTP": "Resend OTP", "second": "Sec", "sync": "Sync", "CreditLimits": "Credit Limits", "creditLimit": "Credit Limit", "createCreditLimit": "Create Credit Limit", "updateCreditLimit": "Update Credit Limit", "SYNC.PERMISSIONS.SUCCESS": "Permissions Synced Successfully", "creditLimitName": "Credit Limit Name", "descriptionAr": "Description in Arabic", "descriptionEn": "Description in English", "priority": "Priority", "CREDIT_LIMIT.CREATE.PERMISSION_WARNING": "You Don't Have Permission To Create Credit Limit", "CREDIT_LIMIT.UPDATE.PERMISSION_WARNING": "You Don't Have Permission To Update Credit Limit", "GeneralSettings": "General Settings", "EmailSettings": "<PERSON><PERSON>s", "SMSSettings": "SMS Settings", "OperationsSettings": "Operations Settings", "SETTING.UPDATED.SUCCESSFULLY": "Settings Updated Successfully", "updateSettings": "Update Settings", "SystemSettings": "System Settings", "SETTING.UPDATE.PERMISSION_WARNING": "You Don't Have Permission To Update Settings", "termsAndCondions": "Terms And Conditions", "agreeWith": "Agree With", "signUp": "Sign Up", "alreadyHaveAnAccount": "Already have an account?", "signIn": "Sign In", "selectaccounttype": "Select Account type", "IndivdualInvstors": "Indivdual Invstors", "Enter the companys commercial register": "Enter the company's commercial register", "createYourAccount": "Create Your Account", "enterYourPersonalDetailsToCreateAccount": "Enter your personal details to create account", "yourName": "Your Name", "MobileNotValid": "Mobile Number is not valid", "EmailNotValid": "Email is not valid", "nameIsRequired": "Name is required", "nameIsInvalid": "Name is invalid", "registerNewAccount": "Register New Account", "register": "Register", "idNumber": "ID Number", "idNumberIsRequired": "ID number is required", "idNumberIsInvalid": "ID number is invalid", "hijri": "<PERSON><PERSON><PERSON>", "gregorian": "<PERSON><PERSON>", "dateOfBirth": "Date Of Birth", "byRegisteringYouAgreeToOur": "By clicking on the registration button, you agree to the terms and conditions", "changeTheDefaultWallet": "Change the default wallet", "cancel": "Cancel", "ok": "Ok", "dateOfBirthIsRequired": "Date of birth is required", "pleaseWaitForConfirmation": "Please wait for confirmation", "arFullName": "Full Name (Arabic)", "enFullName": "Full Name", "dobH": "Date of Birth (Hijri)", "dobG": "Date of Birth (<PERSON><PERSON>)", "gender": "Gender", "arFirst": "First Name (Arabic)", "enFirst": "First Name", "arFamily": "Family Name (Arabic)", "enFamily": "Family Name", "arFather": "Father's Name (Arabic)", "enFather": "Father's Name", "arGrand": "Grandfather's Name (Arabic)", "enGrand": "Grandfather's Name", "idVersion": "ID Version", "dIssueDateG": "Issue Date (<PERSON><PERSON>)", "idIssueDateH": "Issue Date (Hijri)", "idExpiryDateG": "Expiry Date (<PERSON><PERSON>)", "idExpiryDateH": "Expiry Date (Hijri)", "arNationality": "Nationality (Arabic)", "enNationality": "Nationality", "language": "Language", "activateTheAccount": "Activate The Account", "checkNafathStatus": "Check Nafath Status", "completeRegistration": "Complete Registration", "investorType": "Investor Type", "upgradeToQualifiedInvestor": "Upgrade to Qualified Investor", "minimumThan72000": "Less than 72,000", "from72000To120000": "From 72,000 to 120,000", "from120000To180000": "From 120,000 to 180,000", "from180000To300000": "From 180,000 to 300,000", "moreThan300000": "More than 300,000", "low": "Low", "medium": "Medium", "high": "High", "personalInformationFromAbsher": "Personal Information From <PERSON><PERSON><PERSON>", "financialInformation": "Financial information", "governmentEmployee": "Government Employee", "Government": "Government", "privateSectorEmployee": "Private Sector Employee", "retired": "Retired", "unemployed": "Unemployed", "student": "Student", "freelancer": "Freelancer", "approximateAnnualIncome": "1- Approximate annual income (Saudi riyals)?", "investmentKnowledgeAndExperience": "2- Investment knowledge and experience", "clientDesireToTakeRisks": "3- The client’s desire to take risks", "workInformation": "Work information", "answerAllTheFollowingQuestions": "Answer all the following questions", "employmentStatus": "Employment status", "jobTitle": "Job title", "enterTheJobTitle": "Enter the job title", "enterTheCompanyName": "Enter the company name", "companyAddress": "Company address", "enterTheCompanyAddress": "Enter the company address", "generalInformation": "General information", "generalInformation1": "1- Are you a member of the board of directors, a member of the audit committee, or an executive in a listed company?", "generalInformation2": "2- Are you a Politically Exposed Person (PEP)?", "generalInformation3": "3- Are you related to a Politically Exposed Person (PEP)?", "generalInformation4": "4- Are you the real beneficiary of the investment account in your financial venture?", "yes": "Yes", "no": "No", "done": "Done", "nafathMessage": "In case of confirming the account activation from the Nafaz platform,\n please click the confirmation button.", "realCapital": "Real Capital", "totalBalance": "Total Balance", "returnOnInvestment": "Return On Investment", "moreDetails": "More Details", "capitalAnalysis": "Capital Analysis", "totalCapital": "Total Capital", "investmentsCount": "Investments Count", "walletsDetails": "Wallets Details", "noInvestments": "No Investments", "qualificationsRequests": "Qualifications Requests", "Qualification": "Qualification", "condition_one": "Do you have assets worth three million Saudi riyals or more?", "condition_one_response": "Have assets worth three million Saudi riyals or more", "condition_two": "Do you work, or have you ever worked, in the financial sector in an investment or finance-related role for a period of at least three years?", "condition_two_response": "Worked in the financial sector in an investment or finance-related role for a period of at least three years", "condition_three": "Do you have a degree in the field of finance or investment accredited by an internationally recognized body?", "condition_three_response": "Have a degree in the field of finance or investment accredited by an internationally recognized body", "Reason": "Reason", "rejected": "Rejected", "reject": "Reject", "accept": "Accept", "UPDATE.QUALIFICATION.STATUS.PERMISSION_WARNING": "You Don't Have Permission To Update Qualification status", "qualificationDetails": "Qualification Details", "requestDetails": "Request Details", "employees": "Employees", "whatAreTheTypesOfInvestors": "What are the types of investors?", "named": "Named", "individualInvestor": "Individual Investor", "theQualifiedInvestorUpgrade": "What documents need to be attached to prove ownership of assets?", "theQualifiedInvestorUpgradeDetails": "To be able to invest more than the limit (200,000), you need to upgrade from an individual investor to a qualified investor.", "documentsNeedToBeAttached": "What documents need to be attached to prove ownership of assets?", "documentsNeedToBeAttachedDetails": "Real estate deeds, investment portfolios, bank accounts, bank accounts or any other assets that belong to you", "chooseOneOfTheFollowingConditions": "Choose one of the following conditions", "toBecomeQualifiedInvestor": "To become a qualified investor on the FinTech platform, you must meet one of the requirements of the Central Bank", "pleaseAttachTheDocument": "Please attach the document", "attachDocumentThatFulfillsTheRequirements": "Attach a document that fulfills the requirements in addition to proving the investor's ownership of it.", "filesSupported": "Files supported (PDF, JPG, PNG, JPEG)", "toBecomeQualifiedInvestorInTheFintech": "To become a qualified investor in the Fintech platform, you must meet one of the requirements of the Central Bank", "theUpgradeRequestWasSuccessfullySent": "The upgrade request was successfully sent", "yourRequestIsBeingProcessed": "Your request is being processed and will be responded to within 24 hours", "requestForUpgrade": "Request for upgrade", "inProcess": "In process", "pleaseFillAllFields": "Please fill all fields", "cancelUpgradeRequest": "Cancel upgrade request", "updateTheRequest": "Update the request", "areYouSureYouWantToCancelTheUpgradeRequest": "Are you sure you want to cancel the upgrade request?", "cancelRequest": "Cancel request", "youCanRequestAnUpgradeAgain": "You can request an upgrade again", "percentage": "Percentage", "mainBalance": "Main Balance", "details": "Details", "graphInvestmentProfits": "A graph of investment profits on your investment wallets", "requestStatus": "Request Status", "conditions": "Conditions", "documents": "Documents", "approvalMessage": "Approval Message", "congratulations": "Congratulations", "yourUpgradeRequestHasBeenApproved": "Your upgrade request has been approved", "upgradeAccount": "Upgrade Account", "Support": "Support", "welcomeBack": "Welcome Back!", "signInMessage": "Sign in to your account and join us", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "createAccount": "Create Account", "login": "<PERSON><PERSON>", "pagination_info": "Showing {{start}} to {{end}} of {{total}} entries", "transactionId": "Transaction ID", "transactionDate": "Transaction Date", "amount": "Amount", "bank": "Bank", "myWallet": "My Wallet", "investments": "Investments", "deposits": "Deposits", "withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "latestInvestments": "Latest Investments", "depositsAndWithdrawals": "Deposits and Withdrawals", "sortBy": "Sort By", "last7Days": "Last 7 Days", "last1Month": "Last 1 Month", "last1Year": "Last 1 Year", "opportunityId": "Opportunity ID", "investmentDate": "Investment Date", "profits": "Profits", "profitDueDate": "Profit Due Date", "actualMaturityDate": "Actual Maturity Date", "accountDetails": "Account Details", "errorPageNotFound": "Oops... Something went wrong", "notfound": "Notfound", "persistentError": "An error has occurred. If the problem persists, please contact a system administrator or try again later.", "backToHome": "Back to Home", "quickTransfer": "Quick Transfer", "sendMoney": "Send Money", "profile": "Profile", "settings": "Settings", "logout": "Logout", "letsGetStarted": "Let's get started!", "enterPhoneNumber": "Please Enter your Phone Number to Start your Online Application", "agreeTo": "By clicking register, you agree to", "termsOfUse": "Terms of Use", "privacyPolicy": "Privacy Policy", "communicationAuthorization": "Communication Authorization", "phone": "phone", "password": "password", "invalidPhoneNumber": "Invalid phone number. It must start with 05 and have 8 digits.", "passwordRequirements": "Password must be at least 8 characters long, contain an uppercase letter, a lowercase letter, a digit, and a special character.", "fieldRequired": "Please Enter {{field}}", "letsCompleteStepsTogether": "Let's complete the steps together.", "verificationCodeSent": "We have sent you a verification code via SMS. Please enter this code below to complete your registration.", "verificationCode": "Verification Code", "verification": "Verification", "minLengthDetail": "This field must be at least {{requiredLength}} characters. You entered {{actualLength}} characters.", "maxLengthDetail": "This field cannot be more than {{requiredLength}} characters. You entered {{actualLength}} characters.", "verificationCodeMessage": "We have sent you a verification code via SMS to reset your password. Please enter this code below to proceed.", "setPassword": "Set Password", "mobilePhoneVerification": "Mobile Phone Verification", "enterVerificationCode": "Enter the 6-digit verification code that was sent to your phone number.", "verifyAccount": "Verify Account", "didNotReceiveCode": "Didn't receive code?", "resend": "Resend", "openNafathApp": "Please open the Nafath app and confirm the request by selecting the number above.", "nationalId": "National ID", "invalidNationalID": "Invalid National ID. It must be a 10-digit number.", "continueCreatingAccount": "Continue creating your account", "completeIdentityDetails": "Complete your identity details and date of birth to ensure secure verification through <PERSON><PERSON><PERSON>.", "selectCommercialRegistration": "Select your commercial registration", "selectCommercialRegistrationNowAndAchieveDreams": "Select your commercial registration now and start achieving your financial dreams by applying for funding!", "commercialRegistration": "Commercial Registration", "verifyIdentityThrough": "Let's verify your identity through ", "nafath": "<PERSON><PERSON><PERSON>", "verificationCodeIs": "The verification code is", "loanRequests": "Loan Requests", "requestNumber": "Request Number", "date": "Date", "customerName": "Customer Name", "invoiceNumber": "Invoice Number", "invoiceAmount": "Invoice Amount", "status": "Status", "createLoan": "Create Loan", "latestLoanRequests": "Latest Loan Requests", "invoiceRecipient": "Invoice Recipient", "attachments": "Attachments", "toast_error_0": "Sorry, there are updates running on the server at the moment. Please wait a few moments and service will resume shortly.", "addNewLoan": "Add New Loan", "invoiceVAT": "invoice VAT", "recipientName": "Recipient Name", "commercialRegistrationNumber": "Commercial Registration Number", "resetPassword": "Reset Password", "setNewPasswordMessage": "You are one step away from setting a new password! Choose a strong combination that includes uppercase and lowercase letters, numbers, and symbols, and make sure it is easy to remember for a pleasant and secure login experience.", "newPassword": "New Password", "confirmPassword": "Confirm Password", "confirmPasswordValidator": "Passwords do not match. Please ensure both passwords are the same for your account's security.", "forgotPasswordMessage": "No worries! Enter your phone number below, and we'll help you change your password and regain access quickly and easily.", "set": "Set", "financialData": "Financial Data", "currentPassword": "Current Password", "bankName": "Bank Name", "accountNumber": "Account Number", "ibanNumber": "IBAN Number", "emailVerification": "Email Verification", "enterEmailVerificationCode": "Enter the 6-digit verification code that was sent to your email.", "verifyCurrentPhoneNumber": "Let's verify your current mobile phone number", "verifyNewPhoneNumber": "Let's verify your new mobile phone number", "enterCurrentPhoneVerificationCode": "Enter the 6-digit verification code that was sent to your current phone number.", "enterNewPhoneVerificationCode": "Enter the 6-digit verification code that was sent to your new phone number.", "transactionTitle": "Title", "depositMethod": "Deposit Method", "openPersonalBankAccountPage": "Open your personal bank account page", "clickAddBeneficiaryIcon": "Click on the Add Beneficiary icon", "addInvestmentAccountMudad": "Add your investment account in Mudad as a beneficiary", "addPersonalNameBeneficiary": "Add your personal name as the beneficiary's name", "activateBeneficiary": "Activate the beneficiary", "transferToInvestmentAccount": "Transfer to your investment account", "arabBankPersonalAccount": "If your personal account is in Arab Bank, add your investment account number", "asShownInFile": "as shown in this file.", "noteDepositProcess24Hours": "Note: The deposit process in your investment account takes about 24 working hours.", "registerhere": "Register Here", "annualIncomeRange": "Annual Income Range", "asRequired": "Investment company", "asCoraporate": "borrower", "asInvestor": "as Investor", "nafathCheck": "Validation check", "Depositmethod": "Deposit Method", "accountstatement": "Account Statement", "nafathCheckTitle": "Check Id Number throw", "nafathCheckCode": "The verification code is", "nafathCheckCodeHint": " Please Open Nafath App and Confirm the number", "nafathComSubTitle": "To ensure secure verification, enter your ID number", "Nafath": "<PERSON><PERSON><PERSON>", "nafathCheckCodeExpired": "The waiting period has expired. Please enter the number again.", "idValidator": "The ID or residence number must start with 1 or 2, then 9 numbers", "letsGetStartedCompanyView": "Initial information", "choosewayTofoundCompany": "Please choose a way to find a company", "companyRegisteredOnYourNationality": "Company registered on your nationality", "addAnotherCompany": "Add another company?", "CreateSubAccount": "Create New Account", "noCompanyFound": "No company found", "IndivdualInvstor": "Indivdual Invstor", "e_Low": "Low", "Less_Than_72K": "Less Than 72K", "Between_72_And_120": "Between 72k And 20k", "Between_120_And_180": "Between 120k And 180k", "Between_240_And_300": "Between 240k And 300k", "More_Than_300K": "More than 300K", "e_Medium": "Medium", "e_High": "High", "r_Low": "Low", "r_Medium": "Medium", "r_High": "High", "GovernmentEmployee": "Government Employee", "PrivateSectorEmployee": "Private Sector Employee", "Retired": "Retired", "BusinessOwner": "Business Owner", "Student": "Student", "NotWorking": "Not Working", "educationLevel": "education Level", "annualIncome": "annual Income", "sourceOfIncome": "source Of Income", "pureOwnership": "pure Ownership", "nationality": "nationality", "therealbeneficiaryoftheaccount": "Are you the real beneficiary of the account?", "politicallyexposedperson": "Are you related to a politically exposed person?", "Areyoupoliticallyexposed": "Are you politically exposed?", "theboardofdirectorsofalistedcompany": "Are you a member of the board of directors of a listed company?", "practicalexperiencerelatedtothefinancialsector": "Do you have any practical experience related to the financial sector?", "BorrowerTitle": "Basic Infromation", "BorrowerSubTitle": "Enter the commercial registration number of th company you want to register with", "BorrowerCheck": "check", "Authorizationdate": "Authorization Date", "depositMethodside": "You can deposit the investment amount by adding the following account information as a beneficiary to your bank account", "InvestmentCompany": "Investment Company", "Upgradetoqualifiedinvestor": "Upgrade to qualified investor", "confirmprocess": "Confirm Process", "Accounts": "Accounts", "RequestLoanRequests": "Request Loan Requests From Here", "pleaseselectloantype": "Please Select Loan request Type", "NoloanRequests": "No loanRequests", "EnterrequiredAmount": "Enter Required Amount", "confirm": "Confirm", "loanrequestDetails": "Loan Request Details", "CompetitionNo": "Competition No./Commercial Register*", "Purchaseorderfile": "Tender file/Purchase order file*", "ReservedPay": "Payments due", "Financingagreement": "Financing Agreement", "Paymentschedule": "Payment Schedule", "Comments": "Comments", "MainInfo": "Main Information", "Draft": "Draft", "loantype": "Loan Type", "requiredAmount": "Required Amount", "EnterCRNationatnaumber": "Enter the unified national number of the company you want to register with", "45day": "45 Days", "Theamounttobewithdrawn": "The amount to be withdrawn", "60day": "60 Days", "120day": "120 Days", "90day": "90 Days", "Paymentdate": "Payment date", "Payment": "Payment", "Upload": "Upload", "Offer": "Offer", "payment": "payment", "uploadDate": "Upload Date", "allwoncebalance": "Premium value", "Installmentstobepaid": "Installments to be paid", "currentcredit": "Current Credit", "Makeadeposittothewalletnow": "Make a deposit to the wallet now", "onesteptofinishyourrequest": "One step left to complete your order!", "Youmustpaytheapplicationstudyfeeof": "You must pay the application study fee of", "Paymentfromthewalletbalance": "Payment from the wallet balance", "Youmustpaytheapplicationstudy": "You must pay the application study fee of", "Theamountpaidwillbedeductedfromtheadministrative": "The amount paid will be deducted from the administrative fees if your application is accepted.", "ConvertWithdrawtocredit": "You can transfer from your investment portfolio to your bank account by filling out the following form", "Youcannotsubmitatransferrequestfromyourinvestmentportfolio": "You cannot submit a transfer request from your investment portfolio until your bank account is verified. Please contact customer <NAME_EMAIL> to verify your account", "Youcannotrequesttowithdrawmoneyfromyourwallet": "You cannot request to withdraw money from your wallet because you do not have a bank account. You can add it from the following link: Edit bank account", "Amendingthebankaccount": "Amending the bank account", "PendingApproval": "Pending Approval", "GrantedFinancingAmount": "Granted Financing Amount", "CreditRating": "Credit Rating", "AdministrativeFees": "Administrative Fees", "requestDate": "Request Date", "MobileNumberUpdatedSuccessfully": "Mobile Number Updated Successfully", "SessionExpired": "Your session has expired. Please login again.", "Purchaseorderissuing": "Purchase order issuing party", "Invoicing": "Invoice financing", "PurchaseOrder": "Purchase order financing", "SupplyChain": "Suppliers", "PurchaseOrderDesc": "It allows your company to finance a specific purchase through a loan or financing from a financial institution, to cover the cost of purchasing products or services.", "pleaseattachmentfiles": " Please attach the required files", "pleaseselectpurchaseorder": "Please select the type of purchase order", "NoDataAvailable": "No Data Available", "Returnoninvestment": "Return on investment", "investmentper": "Coverage ratio", "Toinvestintheopportunity": "To invest in the Opportunity", "Notethattheminimuminvestment": "Note that the Minimum Investment", "Privatecompany": "Private Company", "totalinvoices": "total Invoices", "companyhasinvoices ": "Company has Invoices", "incomeCOmpany": "Income Company", "companyInfo": "Company Information", "finicialinfocompany": "Finicial info Company", "ownerDebtEquity": "Owner Debt Equity", "Cashcycleindays": "Cash cycle in days", "Salesturnoverrateindays": "Sales turnover rate in days", "Returnonequity": "Return on equity", "Currentassetscurrentliabilities": "Current assets/current liabilities", "Profitmarginbeforedepreciation": "Profit margin before depreciation/amortization, interest and zakat", "finianciallist": "Statement of financial position", "incomelist": "Income statement", "Cashflows": "Cash flows", "IncomeStatement": "Income Statement", "Grossprofit": "Gross Profit", "Otherrevenues": "Other revenues", "Earningsbeforeinteresttaxesandzakat": "Earnings before interest taxes and zakat", "Netprofit": "Net Profit", "CashFlowStatement": "Cash Flow Statement", "Cashflowsfromoperatingactivities": "Cashflows From Operating Activities", "Cashflowsfrominvestingactivities": "Cashflows From Investing Activities", "Cashflowsfromfinancingactivities": "Cashflows From Financing Activities", "Netcashflows": "Net Cashflows", "Lastperiodcashbalance": "Last Period cashbalance", "financiallistCenter": "Financial list Center", "Currentassets": "Current assets", "NOCurrentassets": "NO Current assets", "totalassets": "Total assets", "Propertyrights": "Property rights", "Currentliabilities": "Current liabilities", "Non-currentliabilities": "Non-current liabilities", "Totalliabilities": "Total liabilities", "nodatarecordedfound": "No Recorded Found", "Is the comment private": "Is the comment private ?", "sar": "SAR", "PeriodInDays": "Period In Days", "paymentsucessfully": "Payment SUCCESSFULLY", "Promissorynote": "Promissory note", "companyhasinvoices": "Company has Invoices", "Valueaddedtax": "Value added tax", "AnnualYield": "Annual Yield", "Expectedduedate": "Expected due date", "AvarageInvestmentsList": "Avarage Investments", "AvarageProfits": "Avarage Profits", "candepositinvestmentamount": "You can deposit the investment amount by adding the following account information as a beneficiary in your bank account", "Installments": "Installments", "DaysRemaining": "Days Remaining"}