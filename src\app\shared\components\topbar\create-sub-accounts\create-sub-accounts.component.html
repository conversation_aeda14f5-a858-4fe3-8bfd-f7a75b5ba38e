@if(Subaccounts?.data.length > 0){
<div class="relative font-cairo">
    <button (click)="toggleOpen()" id="notification-btn" class="relative h-10 w-10 rounded-full m-auto border border-n30 bg-primary/5 dark:border-n500 dark:bg-bg3 md:h-12 md:w-12">
      <img src="assets/images/newsubAccount.svg" alt="" class="p-3 text-n30" />

    </button>
    <div id="notification" [ngClass]="isOpen?'show':'hide'" class="absolute top-full z-20 origin-[60%_0] rounded-md bg-n0 shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)] duration-300 dark:bg-bg4 ltr:-right-[110px] sm:ltr:right-0 sm:ltr:origin-top-right rtl:-left-[120px] sm:rtl:left-0 sm:rtl:origin-top-left">
      <div class="flex items-center justify-between border-b p-3 dark:border-n500 lg:px-4">
        <h5 class="h5">{{"Accounts"| translate }}</h5>

      </div>


      <ul class="flex w-[300px]  flex-col p-4">
        <div class="h-72 overflow-auto">
          <div *ngFor="let subAccount of Subaccounts?.data; let i = index" 
        >
            <div (click)="changeAccountUser(subAccount.id)" class="flex cursor-pointer rounded-md  p-2 duration-300 hover:bg-primary/10 gap-2">
              <img src="assets/images/person.svg" width="33" height="33" class="shrink-0 rounded-full" alt="img" />
              <div class="text-base mt-2 font-semibold">
                {{ subAccount.name }}
                <p class="text-[#676767] font-normal text-sm">{{ subAccount?.typeName | translate }} </p>
              </div>
            </div>
          </div>
        </div>



        <div (click)="handleCreateSubAccounts()"  class="flex  cursor-pointer gap-2 rounded-md p-2 border-[#B9BBBD] border-dashed border-2 duration-300 hover:bg-primary/10">

          <img src="assets/images/addnewaccount.svg" width="22" height="22" class="shrink-0 rounded-full" alt="img" />
          <div class="text-xl  font-semibold  text-[#B4B4B4]">
            {{'CreateSubAccount'|translate}}
          </div>
        </div>


      </ul>
    </div>
  </div>
}
