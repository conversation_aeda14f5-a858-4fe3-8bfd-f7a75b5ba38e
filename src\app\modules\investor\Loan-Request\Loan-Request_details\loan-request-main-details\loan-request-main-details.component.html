<div class="container-fluid">
  <div class="box mb-4 xxxl:mb-6">
    <div class="mb-8 pb-6 bb-dashed items-center">
      <div class="grid grid-flow-row-dense grid-cols-3 grid-rows-1">
        <div class="col-span-2 h4 header text-xl font-bold">
          {{ "loanrequestDetails" | translate }}
        </div>

        <div>
          <span class="justify-end flex status">
            <!-- <span class="flex items-center mx-4">
            {{ investorData?.requestStatusName |translate }}
          </span> -->
            <a (click)="goBack()" class="btn ac-modal-btn">
              <i class="las la-arrow-alt-circle-left text-base md:text-lg"></i>
              {{ "back" | translate }}
            </a>
          </span>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-content">
        <form class="grid grid-cols-2 gap-4 xxxl:gap-6">
          <div class="col-span-2 md:col-span-1">
            <label>{{ "orderDate" | translate }}</label> :
            {{ loanRequestData?.requestDate | dateFormat : "date" }}
          </div>
          <div class="col-span-2 md:col-span-1">
            <label>{{ "Amount" | translate }}</label> :
            {{ loanRequestData?.requestedAmount | number : "1.0-0" }}
            {{ "SAR" | translate }}
          </div>
          <div class="col-span-2 md:col-span-1">
            <label>{{ "Peroid" | translate }}</label> :
            {{ loanRequestData?.periodInDays }} {{ "Days" | translate }}
          </div>
          <div class="col-span-2 md:col-span-1">
            <label>{{ "status" | translate }}</label> :
            {{ loanRequestData?.requestStatusName | translate }}
          </div>
        </form>

        <div class="bb-dashed mt-8 mb-2"></div>

        <div class="border-b border-gray-200 dark:border-gray-700">
          <ul
            class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400"
          >
            <li class="me-2" *ngFor="let tab of tabs">
              <a
                (click)="changeTab(tab)"
                [id]="tab"
                [ngClass]="{
                  'border-b-primary': activeTab.field === tab.field
                }"
                class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group transition ease-in-out duration-300"
              >
                {{ tab.title! | translate }}
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<!----------------------------------------------------------------------------->

@switch (activeTab.field) { @case("companyInformation") { @if(!loading) {
<app-loan-request-main-info
  [loanRequest]="loanRequestData"
></app-loan-request-main-info>
} } @case("Riskreport") { @if(!loading) {
<app-loan-request-scores [loanRequest]="loanRequestData"></app-loan-request-scores>
} }
<!-- @case("Operations") {
     <app-loan-request-paymentschedule [loanRequestId]="id"></app-loan-request-paymentschedule>
  } -->

@case("relatedAccounts") { @if(!loading) {
<app-loan-request-attachments
  [loanRequestId]="userId"
></app-loan-request-attachments>
} } @case("Comments") { @if(!loading) {
<app-loan-request-comments [loanRequestId]="userId"></app-loan-request-comments>
} } }
