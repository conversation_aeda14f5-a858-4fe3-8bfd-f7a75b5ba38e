import { Component, type OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonComponent } from '@component/form/button/button.component';
import { InputComponent } from '@component/form/input/input.component';
import { ModalService } from '@component/form/modals/modal.service';
import { NafathPopupComponent } from '@component/NafathPopup/NafathPopup.component';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../services/auth.service';
import { EncryptStorage } from 'encrypt-storage';
import { ISendNafathResponse, ISendNafathResponse2 } from '../models/login.model';

@Component({
  selector: 'app-nafath',
  standalone: true,
  imports: [
        ButtonComponent,
    InputComponent,
    ReactiveFormsModule,
    TranslateModule,
    NafathPopupComponent
  ],
  templateUrl: './nafath.component.html',
  styleUrl: './nafath.component.css',
})
export class NafathComponent implements OnInit {

 nafathForm!: FormGroup
 response:any|undefined=undefined
encryptStorage = new EncryptStorage('User_info_register');
   constructor(
    protected modalService: ModalService,
    private authService:AuthService,
     private fb: FormBuilder, private router: Router
   ) {
     this.nafathForm = this.fb.group({
      nationalId: [null, [Validators.required, Validators.pattern('^(1|2){1}[0-9]{9}$')]],
     });
   }

   ngOnInit() {
   }

   async onSubmit() {
     if (this.nafathForm.valid) {

      const response = localStorage.getItem('token')??''
      const infoUser = this.encryptStorage.getItem('mobile')
      const userType =this.encryptStorage.getItem('User_info_register')
     if (userType) {
             const body = {
               nationalId: +this.nafathForm.value.nationalId,
               password: infoUser.password,
               userType: userType,
               userAccountType: userType,
               token: response
             }
             this.response = await this.authService.sendNafathReqeust2(body) ;

             if(this.response?.data.random){
               this.modalService.open('nafath-modal')
             }
           }
      } else {
        this.nafathForm.markAllAsTouched()
        console.log('Error')
      }
    }
    submitEnteredValue(res:string){
      this.modalService.close('nafath-modal')
     this.nafathForm.reset()
    this.router.navigate(['/'])
   }
}
