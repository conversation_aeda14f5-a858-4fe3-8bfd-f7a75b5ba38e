import { EncryptStorage } from 'encrypt-storage';

import { Injectable, OnInit } from '@angular/core';

import { IUser } from '../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class EncryptStorageService implements OnInit {
  #encryptStorage: EncryptStorage;
  #encryptTokenStorage!:EncryptStorage
  constructor() {
    this.#encryptStorage = new EncryptStorage('MDD_Fintech_is_the_best__@2o2!');
  }

  ngOnInit() {
    this.#encryptTokenStorage = new EncryptStorage('MDD_Fintech_is_userToken');
  }

  get isTokenExpired(): boolean {
    const tokenExpiryDateStr = this.#encryptTokenStorage.getItem('TokenExpiryDate'); // Note: Typo in 'TokenExpiryDate'?
    if (!tokenExpiryDateStr) return true;
    
    try {
        const expiryDate = new Date(tokenExpiryDateStr);
        const currentTime = Date.now();
        const expiryTime = expiryDate.getTime();
        
        // Optional: Log for debugging
        // console.log(`Token check - Current: ${new Date(currentTime)}, Expiry: ${expiryDate}`);
        
        return currentTime >= expiryTime;
    } catch (e) {
        console.error('Error parsing token expiry date', e);
        return true;
    }
}

  // get isTokenExpired(): boolean {
  //   const tokenExpiryDateStr = localStorage.getItem('TokenExpiryDate');
  //   console.log(tokenExpiryDateStr,'tokenExpiryDateStr');
    
  //   if (tokenExpiryDateStr != undefined) {
  //     return Date.now() > Date.parse(tokenExpiryDateStr) ? true : false;
  //   }
  //   return true; // expired
  // }

  get getToken(): string | undefined {
    return decodeURIComponent(this.#encryptTokenStorage.getItem('token')??'');
  }

  get isLoggedIn(): boolean {
    const token = this.#encryptTokenStorage.getItem('token')??'';
    return token != undefined && token != '' ? true : false;
  }

  getCurrentUser() {
    return this.#encryptTokenStorage.getItem('currentUser');
  }

  cacheUser(user: IUser) {
    const tokenExpiryDate = new Date();

    this.#encryptTokenStorage.setItem('currentUser', user);
    this.#encryptTokenStorage.setItem('token', encodeURIComponent(user.token));
    this.#encryptTokenStorage.setItem(
      'TokenExpiryDate',
      tokenExpiryDate.setHours(tokenExpiryDate.getHours() + 1).toString()
    );
  }

  clear() {
    localStorage.clear();
    this.#encryptStorage.clear();
  }
}
