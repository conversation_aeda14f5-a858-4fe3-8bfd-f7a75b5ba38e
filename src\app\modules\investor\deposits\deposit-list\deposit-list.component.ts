import {Component, signal} from '@angular/core';
import {colDef, DataTableModule} from "@bhplugin/ng-datatable";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {DepositService} from "../services/deposit.service";
import {DepositRequestModel, DepositRequestSearch, PaymentRequestModel, PaymentRequestSearch} from "../models/deposit.model";
import {CommonModule, DatePipe, DecimalPipe, NgClass} from "@angular/common";

import {DropdownComponent} from "@component/shared/dropdown/dropdown.component";
import {OptionsVerticalComponent} from "@component/shared/options-vertical/options-vertical.component";

import {TopBannerComponent} from "@component/shared/top-banner/top-banner.component";
import {DepositCreateComponent} from "../deposit-create/deposit-create.component";
import {ModalService} from "@component/form/modals/modal.service";
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';
import { PaymentType } from '../../../../shared/enums/payment.enum';
import { DepositMethodComponent } from '@component/deposit-method/deposit-method.component';

@Component({
  selector: 'app-deposit-list',
  standalone: true,
  imports: [
    DataTableModule,
    TranslateModule,
    DateFormatPipe,
    CommonModule, 
    DecimalPipe,
    TopBannerComponent,
    DepositCreateComponent,
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './deposit-list.component.html',
  styleUrl: './deposit-list.component.css'
})
export class DepositListComponent {
  columns: Array<colDef> = [];
  search: DepositRequestSearch = {isServerMode: true} as DepositRequestSearch;
  // deposits: DepositRequestModel[] = [];
  deposits=signal<any[]>([])
  constructor(
    public translate: TranslateService,
    protected depositService: DepositService,
    private router: Router,
    private modalService: ModalService,
  ) {
    this.depositService.initPagination();
  }

  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }


  async handleSearch(e: any) {
    this.search = {find: (e.target as HTMLInputElement).value, isServerMode: false} as DepositRequestSearch;
    this.deposits()
    this.depositService.initPagination();
    await this.getList();
    this.search.isServerMode = true;
  }

  async ngOnInit() {
    this.columns = [
      {title: this.translate.instant('RefNumber'), field: 'transactionNo',},
      {title: this.translate.instant('transactionDate'), field: 'transactionDate',},
      {title: this.translate.instant('amount'), field: 'amount',},
      {title: this.translate.instant('sourceAccount'), field: 'sourceAccount',},
   
    ]
    await this.getList();
  }

  async getList() {
    try {
      this.search.paymentType = PaymentType.Deposit;
      this.deposits.set(await this.depositService.getList(this.search) ?? [])
  
      
    } catch (e: any) {
      console.log(e);
    }
  }

  openCreateDeposit() {
    this.modalService.open('deposit-create')
  }
}
