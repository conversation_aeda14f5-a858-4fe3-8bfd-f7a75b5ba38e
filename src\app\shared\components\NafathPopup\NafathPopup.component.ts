import { Ng<PERSON>orO<PERSON>, Ng<PERSON><PERSON>, AsyncPipe } from '@angular/common';
import { ChangeDetectorRef, Component, DestroyRef, ElementRef, EventEmitter, Input, Output, ViewChild, type OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonComponent } from '@component/form/button/button.component';
import { ModalComponent } from '@component/form/modal/modal.component';
import { MDModalModule } from '@component/form/modals';
import { ModalService } from '@component/form/modals/modal.service';
import { LoadingBarService } from '@ngx-loading-bar/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, interval, Subscription, switchMap } from 'rxjs';
import { phoneNumberValidator } from '../../validators/phone.validator';
import { AuthService } from '../../../modules/auth/services/auth.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EncryptStorage } from 'encrypt-storage';
@Component({
  selector: 'app-nafath-popup',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MDModalModule,
    TranslateModule,
  ],
  templateUrl: './NafathPopup.component.html',
  styleUrl: './NafathPopup.component.css',
})
export class NafathPopupComponent implements OnInit {
  @Input() id = 'nafath-modal';
  @Input() code:number=0;
  @Input() token:string='';
  @ViewChild('nextButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() submitEnteredValue = new EventEmitter<string>();
  @Input() title: string = 'nafathCheckTitle';
  @Input() subTitle: string = 'nafathCheckCode';
  @Input() hint: string = 'nafathCheckCodeHint';
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  countdown: number = 0;
  countdownSubscription: Subscription | null = null;
  readonly countdownStart = 60; // Countdown start time in seconds
  errorMessage: string = '';
  constructor(
    private destroyRef:DestroyRef,
    private authService: AuthService,
    private translateSerive: TranslateService,
    private fb: FormBuilder,
    protected modalService: ModalService,
    private cdr: ChangeDetectorRef,
    protected loadingBar: LoadingBarService,
  ) {
  }



  ngOnInit(): void {
// api to get new code from nafath
interval(10000).pipe(
  takeUntilDestroyed(this.destroyRef),
  switchMap(()=> this.authService.checkNafathStatus2({token:this.token}))).subscribe((res:any)=>{
    

    if(res.data.status == 'COMPLETED'){
      this.#encryptTokenStorage.setItem('currentSubInfo',
         JSON.stringify({userAccountStatus:res.data.userAccountStatus,userAccountStatusName:res.data.userAccountStatusName,
          userAccountType:res.data.userAccountType,userAccountTypeName:res.data.userAccountTypeName}));
          this.#encryptTokenStorage.setItem('token', encodeURIComponent(res.data.token));
              this.authService.setTokenUser(encodeURIComponent(res.data.token))
      this.#encryptTokenStorage.setItem('TokenExpiryDate', res.data?.tokenExpiryDate);
  
      this.verify()
    }else if(res.data.status === 'EXPIRED'){
      this.errorMessage =  this.translateSerive.instant('nafathCheckCodeExpired')
      setTimeout(() => {
        this.modalService.close('nafath-modal')
      },5000)
    }
 })
}

  ngAfterViewInit(): void {

  }



  verify(): void {
    // call api to check if user select code from nafath
    this.submitEnteredValue.emit("done")
  }


}
