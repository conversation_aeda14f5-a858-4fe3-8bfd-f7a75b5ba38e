import { Component, OnInit } from '@angular/core';
import { TranslateModule, TranslateService,  } from '@ngx-translate/core';
import { AuthService } from '../../../modules/auth/services/auth.service';
import { EncryptStorage } from 'encrypt-storage';
import { StorageService } from '../../../modules/investor/dashboard/services/collectionData.service';

@Component({
  selector: 'app-deposit-method',
  standalone: true,
  imports: [TranslateModule,],
  templateUrl: './deposit-method.component.html',
  styleUrl: './deposit-method.component.css'
})
export class DepositMethodComponent implements OnInit{
  CollectioncountData:any
  collectionData:any
  constructor( private translate: TranslateService,
     private authService:AuthService,
     private storageService: StorageService
  ){}


  async ngOnInit(): Promise<void> {
  await this.getStoredCollectionData();
}


#encryptTokenStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken');
  getStoredCollectionData() {
    this.collectionData = this.storageService.getCollectionData();
    
    if (!this.collectionData) {
      console.log('No collection data found in storage');
      // You might want to fetch from API here if not found
    }
  }

// async getCollectioncount() {
//   try {
//     this.Collectioncount = await this.authService.getCollectioncount();
//     this.CollectioncountData = this.Collectioncount.data;
    
//     // Store encrypted data
//     this.storageService.setCollectionData(this.CollectioncountData);
    
//   } catch(error) {
//     console.log('Error getting collection count:', error);
//   }
// }
// To remove the stored data
clearStoredCollectionData() {
  this.#encryptTokenStorage.removeItem('CollectioncountData');
}
}
