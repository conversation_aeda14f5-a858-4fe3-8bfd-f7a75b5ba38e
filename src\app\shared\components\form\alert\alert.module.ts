import {CommonModule} from '@angular/common';
import {ModuleWithProviders, NgModule} from '@angular/core';
import {MDAlertComponent} from './alert.component';
import {icons, LUCIDE_ICONS, LucideAngularModule, LucideIconProvider} from 'lucide-angular';

@NgModule({
  imports: [CommonModule, LucideAngularModule],
  declarations: [MDAlertComponent],
  exports: [MDAlertComponent],
  providers: [{provide: LUCIDE_ICONS, multi: true, useValue: new LucideIconProvider(icons)}]
})
export class MDAlertModule {
  static forRoot(): ModuleWithProviders<MDAlertModule> {
    return {ngModule: MDAlertModule, providers: []};
  }
}
