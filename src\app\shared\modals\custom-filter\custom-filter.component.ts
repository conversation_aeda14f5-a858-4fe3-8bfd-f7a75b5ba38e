import { Component, Input, OnInit, ElementRef, EventEmitter, Output, ViewChild, AfterViewInit, ViewChildren, Query<PERSON>ist, ChangeDetectorRef } from '@angular/core';

import { TranslateModule } from "@ngx-translate/core";
import { ModalComponent } from '@component/form/modal/modal.component';
import { AsyncPipe, NgClass, NgForOf } from '@angular/common';
import { FormBuilder, FormGroup, Validators, FormArray, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';


import { DropdownComponent } from '@component/shared/dropdown/dropdown.component';
import { SelectComponent } from '@component/form/select/select.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MDModalModule } from '@component/form/modals';
import { ModalService } from '../modal.service';
import { OtpService } from '../../otp/services/otp.service';

export interface FilterField {
  key: string;
  label: string;
  type: 'select' | 'text' | 'date'; // extend as needed
  options?: { label: string; value: any }[]; // for select
  validators?: any[]; // Angular validators
}
@Component({
  selector: 'app-custom-filter',
  standalone: true,
  imports: [
    MDModalModule,
    TranslateModule,
    NgForOf,
    ReactiveFormsModule,
    CommonModule,
    SelectComponent,
    MatDatepickerModule,
    MatNativeDateModule,
    MatInputModule
  ],
  templateUrl: './custom-filter.component.html',
  styleUrl: './custom-filter.component.css'
})
export class CustomFilterComponent implements OnInit {
  form!: FormGroup;
  otpForm!: FormGroup;
  isOtpVisible = false;
  selectedStatus: any;
@Output() filterApplied = new EventEmitter<any>();
@Output() closed = new EventEmitter<void>();
  @ViewChild('submitButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() otpConfirmed = new EventEmitter<string>();
  @Output() otpEntered = new EventEmitter<string>();
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef>;
  @Input() fields: any[] = [];
  @Input() isFiltered = false;
  @Input() result: any;

  fieldCount: number = 6; // Number of OTP fields
  countdown: number = 30; // Initial countdown value (in seconds)
  countdownInterval: any; // To store the interval reference
  constructor(private cdr: ChangeDetectorRef,
    private OTPService: OtpService, private fb: FormBuilder,  protected modalService: ModalService) { }
  ngOnInit(): void {
    console.log(this.result)
    this.form = this.fb.group({});
    this.fields.forEach(field => {
      if (field.type === 'amount-range') {
        this.form.addControl('amountFrom', this.fb.control(''));
        this.form.addControl('amountTo', this.fb.control(''));
      } else if (field.type === 'date-range') {
        this.form.addControl('fromDate', this.fb.control(''));
        this.form.addControl('toDate', this.fb.control(''));
      } else {
        this.form.addControl(field.key, this.fb.control(''));
      }
    });
    if (this.result) {
      this.form.patchValue(this.result);
    }
    /* this.fields.forEach(field => {
       this.form.addControl(field.key, this.fb.control(''));
     });
     */
  }

  submitForm(): void {
  if (this.form.invalid) return;
  
  if (this.isFiltered) {
    this.resetForm();
    this.filterApplied.emit(null); // Emit null to indicate reset
  } else {
    this.filterApplied.emit(this.form.value);
  }
  
  this.modalService.close("customFilterModal");
}

// Update closeModal method

  // submitForm(): void {
  //   if (this.isFiltered) this.resetForm()
  //   this.modalService.closeWithResult('customFilterModal', this.form.value)
  // }
  closeModal(): void {
    
    this.modalService.close('customFilterModal');
    this.closed.emit();
  }
  getInputType(key: string): string {
    if (this.isNumericField(key)) {
      return 'number';
    }
    return 'text';  // Default type
  }
  openDatepicker() {
  }
  isNumericField(key: string): boolean {
    return key === 'amountFrom' || key === 'amountTo';
  }
  resetForm(): void {
    this.form.reset();
    this.isFiltered = false;
  }

}
