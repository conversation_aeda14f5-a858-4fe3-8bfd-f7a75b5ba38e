import { Injectable } from '@angular/core';
import { Opportunity } from '../models/opportunity';
import {  IQuery } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';
import { BaseResponse } from '../../../auth/models/api-response.model';
import { EncryptStorage } from 'encrypt-storage';


export interface IPagination {
  pageSize: number;
  pageSizeOptions: number[];
  page: number;
  loading: boolean;
  totalRows: number;
}

export const PAGINATION: IPagination = {
  pageSize: 6, // Default page size
  pageSizeOptions: [6, 15, 30],
  page: 1,
  loading: false,
  totalRows: 0
};
@Injectable({
  providedIn: 'root'
})
export class OpportunitiesService {
 pagination = {...PAGINATION};
  private path = '/client';
  #encryptTokenStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken');

  constructor(private apiService: ApiService) {}

  initPagination() {
    this.pagination = {...PAGINATION};
    return this.pagination;
  }

  async getList(params: any,query?: any) {
    this.pagination.loading = true;
    query = {  ...query,
      limit: this.pagination.pageSize,
      page: this.pagination.page} as IQuery;
 

    try {
      const response: any = await this.apiService.post<any>(
        `${this.path}/opportunities/search`, 
          params
      );
      
      this.pagination.loading = false;
      this.pagination.totalRows = response.totalRecords || response.data?.length || 0;
      
      return response.data || [];
    } catch (error) {
      this.pagination.loading = false;
      throw error;
    }
  }

  changePage(page: number) {
    this.pagination.page = page;
  }

  changePageSize(size: number) {
    this.pagination.pageSize = size;
    this.pagination.page = 1; // Reset to first page when page size changes
  }
  
async getOpportunityById(OpportunityId: string): Promise<any> {
  try {
    // Add debug log to verify the URL
 
    
    const response = await this.apiService.get(`${this.path}/opportunities/${OpportunityId}`);
    

    
    if (!response) {
      console.error('Null response received');
      return null;
    }
    
    // Check for both possible response structures
    if (response.data) {
      return response.data;  // If response has data property
    }
    
    return response;  // If response is the data itself
  } catch (error) {
    console.error('API Error:', error);
    throw error; // Rethrow to handle in component
  }
}

  async getCompanyByOpportunityId(loanID: string): Promise<any> {
    const response = await this.apiService.get<any>(`${this.path}/opportunities/${loanID}/company`);
    if(response.list && response.list.length) {
      return response.list[0];
    }
    return;
  }

    async cancelInvestment(body: any) {
    return await this.apiService.put(`${this.path}/investments/cancel`, body);
  }
    async getInvestment(body: any) {
    return await this.apiService.post(`${this.path}/investments/search`, body);
  }

}
