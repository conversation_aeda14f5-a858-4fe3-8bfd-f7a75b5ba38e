import { NgClass } from '@angular/common';
import { Component, HostListener, inject, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import {TranslateModule} from "@ngx-translate/core";
import { AuthService } from '../../../../modules/auth/services/auth.service';
import { EncryptStorage } from 'encrypt-storage';

@Component({
  selector: 'app-profile-dropdown',
  standalone: true,
  imports: [NgClass, RouterLink, TranslateModule],
  templateUrl: './profile-dropdown.component.html'
})
export class ProfileDropdownComponent implements OnInit {
  isOpen = false
  loginuser:any
  router = inject(Router)
  profile:any
  profileData:any
  toggleOpen() {
    this.isOpen = !this.isOpen
  }
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if the click event target is not within the sidebar
    if (!document.querySelector('#profile')!.contains(event.target as Node) && !document.querySelector('#profile-btn')!.contains(event.target as Node)) {
      this.isOpen = false // Close the sidebar
    }
  }
   #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(private authService: AuthService){

  }
  async ngOnInit(): Promise<void> {
        await this.getProfile();
    // this.loginuser =  this.authService.currentUserValue?.fullName
  }
  // get currentUser(){
  //   return this.authService.currentUserValue?.fullName
  // }

  async getProfile(){
    if(this.#encryptTokenStorage.getItem('profile')){
      this.profileData= this.#encryptTokenStorage.getItem('profile')
      return
    }
    try{
      this.profile= await  this.authService.getprofileAccount();
      this.#encryptTokenStorage.setItem('profile',this.profile?.data)
      this.profileData= this.profile?.data
      this.authService.userBalance.set(this.profile?.data?.balance)
    }catch{
      console.log('error')
    }

  }
  logout(){
    this.authService.logout()
    this.router.navigate(['/auth'])
  }

}
