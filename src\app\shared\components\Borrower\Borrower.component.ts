import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, output, signal, type OnInit } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule, UntypedFormGroup, Validators } from '@angular/forms';
import { ButtonComponent } from '@component/form/button/button.component';
import { InputComponent } from '@component/form/input/input.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BorrowerService } from './Borrower.service';
import { ToastService } from '../../../core/services/toast.service';
import { DatePickerComponent } from '../../../shared/components/date-picker/date-picker.component';



import flatpickr from 'flatpickr';
import { UserAccountStatus } from '../../enums/user.enum';
import { AuthService } from '../../../modules/auth/services/auth.service';
import { validators } from 'tailwind-merge';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { EncryptStorage } from 'encrypt-storage';
import { FileUploadComponent } from '@component/form/file-upload/file-upload.component';


@Component({
  selector: 'app-borrower',
  standalone: true,
  imports: [
    TranslateModule,
    InputComponent,
    DatePickerComponent,
    FormsModule,
    ReactiveFormsModule,
    FileUploadComponent
  ],
  templateUrl: './Borrower.component.html',
  styleUrl: './Borrower.component.scss',
})
export class BorrowerComponent implements OnInit {
  borrowerForm!:UntypedFormGroup
  encryptStorageStep = new  EncryptStorage('User_info_step');
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')

  #borrowerService:BorrowerService = inject(BorrowerService)
  #toast: ToastService=inject(ToastService)
  #translateService:TranslateService=inject(TranslateService)
  closeStatuts=output<number>()
  //backstep=output<boolean>()
  brBackStep=output<boolean>()
  isInner=input<boolean>(false)
  reslut:any
    firstStep=signal<boolean>(JSON.parse(localStorage.getItem('firstStep')??'false'))

  constructor(  protected authService: AuthService,
       private router: Router,
         private toaster: ToastrService
  ){}
  ngOnInit(): void {

    this.intalForm()
  }
  ngAfterViewInit():void {
    flatpickr(".datepicker", {
      dateFormat: "Y-m-d",
      locale: "ar"
    });
  }
   intalForm(){
    this.borrowerForm = new UntypedFormGroup({
      // search:new FormControl(null,),
      cr:new FormControl(null,[Validators.required,Validators.minLength(10),  // Minimum length of 5 characters
        Validators.maxLength(10)]),
      crNationalNumber:new FormControl(null,[Validators.required,
        Validators.minLength(10),  // Minimum length of 5 characters
        Validators.maxLength(10)]
      ),
      // DelegationFile:new FormControl(null,[Validators.required]),
      type:new FormControl(this.encryptStorageStep.getItem(('selected_type')))
    })
   }
   previousStep(){
    this.brBackStep.emit(true)
     this.firstStep.update(v => !v)
   }
   async handleBorrowerCheck(){
    
    if(this.borrowerForm.valid){
      try{
        const res = await this.#borrowerService.apiGetCommercialRegistration(this.borrowerForm.value.cr,this.borrowerForm.value.crNationalNumber)
      
        
        this.borrowerForm.addControl('commercialRegistrationNumber',new FormControl(null,Validators.required))
        this.borrowerForm.addControl('commercialRegistrationDate',new FormControl(null,Validators.required))
        this.borrowerForm.addControl('DelegationFile',new FormControl(null,Validators.required))
       
        this.reslut = res
       
        this.borrowerForm.get('CR')?.setValue(this.reslut?.data.cr)
        this.borrowerForm.get('crNationalNumber')?.setValue(this.reslut?.data.crNationalNumber)

      }catch(e:any){
        this.#toast.error(
          this.#translateService.currentLang == 'ar' ? e.error.result['arabicMessage'] : e.error.result['englishMessage'])
      }
    }
   }
   async nextStep(){
    if (this.borrowerForm.valid) {
      try {
        const currentSubInfoRaw = this.#encryptTokenStorage.getItem('currentSubInfo') ;
        const currentAccountStatus = currentSubInfoRaw ? JSON.parse(currentSubInfoRaw) : null;
        const userType = this.encryptStorageStep.getItem('selected_type')? this.encryptStorageStep.getItem('selected_type') : currentAccountStatus?.userAccountType ;

        const data = await this.#borrowerService.apiPostCompanyDelegate(
          this.borrowerForm.value.cr,
          this.borrowerForm.value.crNationalNumber,
          this.borrowerForm.value['commercialRegistrationDate'],
          userType,
          this.borrowerForm.value['DelegationFile'] ? this.borrowerForm.value['DelegationFile'][0] : null
        );
    // return
        // Update the local storage with the new account status
        // currentAccountStatus.userAccountType = userType;
        // currentAccountStatus.userAccountStatusName = 'PendingApproval';
        // currentAccountStatus.userAccountStatus = UserAccountStatus.PendingApproval;
        // localStorage.setItem('currentSubInfo', JSON.stringify(currentAccountStatus));
        // Optionally, you can redirect the user or refresh the page    
        this.toaster.success(this.#translateService.currentLang == 'ar' ? ("تمت الإضافه بنجاح") : ("Added successfully"));
        window.location.reload()
        this.router.navigate(['/']);
     
        

        // Update account status locally
        // currentAccountStatus.userAccountStatusName = 'PendingApproval';
        // currentAccountStatus.userAccountStatus = 1;
        // localStorage.setItem('currentSubInfo', JSON.stringify(currentAccountStatus));
        this.closeStatuts.emit(+this.borrowerForm.get('C')?.value);
      

      } catch (error) {
        console.error('Something went wrong', error);
      }
    }

   }

  goBackStep() {
      this.authService.selectedSubAccount.set(undefined);

   }
}
