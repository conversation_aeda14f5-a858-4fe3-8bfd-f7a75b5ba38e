import { IQuery } from "../../../../core/models/pagination.model";
import { InvestmentStatus } from "../../../../shared/enums/investment-status.enum";


export interface InvestmentModel {
  investmentID: string;
  walletID: string;
  walletName?: string;
  userID: string;
  fullName?: string;
  opportunityID: string;
  opportunityNo: number;
  orderNo: number;
  amount: number;
  profits: number;
  profitsPer: number;
  fileName?: string;
  fileContent?: string;
  fileContentType?: string;
  investDate: Date;
  status: InvestmentStatus;
  readonly statusName: string;
  approvedOn: Date;
  dueDate: Date;
  closedOn?: Date;
  actualDueDate?: Date;
  expiryDate: Date;
  lastUpdateBy: string;
  lastUpdateByName?: string;
  lastUpdateOn: Date;
  actionName?: string;
  createWithdraw?: boolean; //TODO check if correct
  period: number;
  mfaCode: string;
}

export interface InvestmentSearch extends IQuery {
  userID?: string;
  walletID?: any;
  opportunityID?: string;
  sort?: string;
  itemsCount?: number;
  limit?: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
  includeStatus?: InvestmentStatus;
  excludeStatus?: InvestmentStatus;
  onlyDueDate?: boolean;
  investDateFrom?: Date;
  investDateTo?: Date;
  dueDateFrom?: Date;
  dueDateTo?: Date;
}

export interface InvestmentStatusCountModel {
  opened: number;
  closed: number;
  cancelled: number;
  approved: number;
}

export interface InvestmentStatusTotalModel {
  opened: number;
  closed: number;
  cancelled: number;
  approved: number;
}

export interface InvestmentCreateModel {
  opportunityID: string;
  amount: number;
  mfaCode: string;
  fileName: string;
  fileContentType: string;
  fileContent: string;
}

export interface InvestmentCancelCommand {
  investmentID: string;
  mfaCode: string;
}

export interface InvestmentCloseCommand {
  investmentID: string;
  mfaCode: string;
  createWithdraw: boolean;
}

export interface InvestmentExcelModel {
  opportunityNo: number;
  fullName: string;
  orderNo: number;
  investDate: string;
  amount: number;
  profits: number;
  walletName: string;
  actualDueDate: string;
  dueDate: string;
  statusName: string;
}
