import {Injectable} from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modals: { [key: string]: boolean } = {};
  private modalResolvers: { [key: string]: (value?: any) => void } = {};

  constructor() {
  }




  open(id: string): void {
    this.modals[id] = true;
    this.adjustZIndex('topbar', 'z-20', false);
    this.adjustZIndex('sidebar', 'z-[21]', false);
  }

  close(id: string): void {
    this.modals[id] = false;
    this.adjustZIndex('topbar', 'z-20', true);
    this.adjustZIndex('sidebar', 'z-[21]', true);
    if (this.modalResolvers[id]) {
      this.modalResolvers[id](undefined);
      delete this.modalResolvers[id];
    }
  }

  closeWithResult(id: string, result?: any): void {
    if (this.modalResolvers[id]) {
      this.modalResolvers[id](result);
      delete this.modalResolvers[id];
    }
    this.close(id);
  }

  openWithResult(id: string): Promise<any> {
    this.open(id);
    return new Promise((resolve) => {
      this.modalResolvers[id] = resolve;
    });
  }

  isOpen(id: string): boolean {
    return !!this.modals[id];
  }

  private adjustZIndex(elementId: string, className: string, add: boolean): void {
    const element = document.getElementById(elementId);
    if (element) {
      if (add) {
        element.classList.add(className);
      } else {
        element.classList.remove(className);
      }
    }
  }

  // open(id: string) {
  //   this.removeClassById('topbar', 'z-20')
  //   this.removeClassById('sidebar', 'z-[21]')
  //   this.modals[id] = true;
  // }

  // close(id: string) {
  //   this.modals[id] = false;
  //   this.addClassById('topbar', 'z-20')
  //   this.addClassById('sidebar', 'z-[21]')
  // }

  // closeWithResult(id: string, result?: any) {
  //   this.close(id);
  //   if (this.modalResolvers[id]) {
  //     this.modalResolvers[id](result);
  //     delete this.modalResolvers[id]; // Cleanup after resolving
  //   }
  // }

  // openWithResult(id: string): Promise<any> {
  //   return new Promise((resolve) => {
  //     this.modals[id] = true;
  //     this.modalResolvers[id] = resolve; // Store resolver for later
  //   });
  // }
  // isOpen(id: string) {
  //   return this.modals[id];
  // }

  removeClassById(elementId: string, className: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.remove(className);
    }
  }

  addClassById(elementId: string, className: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.add(className);
    }
  }
}
