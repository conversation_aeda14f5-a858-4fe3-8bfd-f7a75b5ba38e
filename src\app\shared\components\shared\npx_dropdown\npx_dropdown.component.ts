import { NgClass } from '@angular/common';
import { Component, EventEmitter, HostListener, Input, input, OnChanges, Output, output, SimpleChanges } from '@angular/core';
import { ClassNameValue, twMerge } from 'tailwind-merge';
import {TranslateModule, TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-npx-dropdown',
  standalone: true,
  imports: [NgClass,TranslateModule],
  templateUrl: './npx_dropdown.component.html'
})
export class NpxDropdownComponent  implements OnChanges{

  currentOptions:{key:any,value:number}[]=[]
  selected:{key:string,value:number}|undefined=undefined
  btnClass=''
  dropdownClass=''
  // props
  @Input() options?:{key:any,value:number}[]
  @Input() btnClassProps?:string
  @Input() dropdownClassProps?:string
  @Input() placeholder?:string
  @Input() selectModel?:number
  @Output() selectModelChange = new EventEmitter<number>()

  constructor(
    private translateService: TranslateService
  ) {
  }

  selectOption(option:{key:any,value:number}){
    this.selected=option
    this.selectModelChange.emit(option.value)
    this.isOpen=false
  }
  isOpen = false
  toggleOpen(event:MouseEvent) {
    const allDropdowns = document.querySelectorAll('.dropdown'); // Find all dropdowns
    allDropdowns.forEach(dropdown => {
        if (dropdown !== event.target) {
            dropdown.classList.remove('show');
            dropdown.classList.add('hide');
        }
    });
    this.isOpen = !this.isOpen
  }
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
  const targetElement = event.target as Element;

    // Check if event.target exists and if the click event target is not within the dropdown or the dropdown button
    if (targetElement && !targetElement.closest('.dropdown-btn')) {
      this.isOpen = false; // Close the dropdown
    }
  }


  ngOnInit(){
    
    this.btnClass=twMerge('border dropdown-btn select-none cursor-pointer bg-transparent dark:bg-bg3 border-n30  h-full text-sm md:text-base dark:border-n500 flex gap-2 justify-between items-center rounded-xl px-3 py-1.5 sm:px-4 sm:py-2 w-full ',this.btnClassProps)
    this.dropdownClass = twMerge('absolute dropdown flex-col z-20 top-full duration-300 origin-top min-w-max shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)] max-h-40 overflow-y-auto right-0 bg-n0 dark:bg-bg4 p-1 rounded-md',this.dropdownClassProps)
  }
  ngOnChanges(changes: SimpleChanges): void {
    this.selected =this.options?.find( el=> el.value == this.selectModel)
    this.currentOptions = this.options?.length?this.options: []
    if(this.selectModel == null) return
    this.btnClass=twMerge('border dropdown-btn select-none cursor-pointer bg-transparent dark:bg-bg3 border-n30  h-full text-sm md:text-base dark:border-n500 flex gap-2 justify-between items-center rounded-xl px-3 py-1.5 sm:px-4 sm:py-2 w-full ',this.btnClassProps)
    this.dropdownClass = twMerge('absolute dropdown flex-col z-20 top-full duration-300 origin-top min-w-max shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)] max-h-40 overflow-y-auto right-0 bg-n0 dark:bg-bg4 p-1 rounded-md',this.dropdownClassProps)
  
  }
}
