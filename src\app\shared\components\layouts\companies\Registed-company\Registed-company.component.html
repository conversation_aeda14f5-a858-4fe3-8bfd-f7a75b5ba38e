@if(!firstStep()){
    <div
      class="box xl:p-6  dark:bg-bg4 font-cairo grid grid-cols-12 gap-2 xxxl:gap-5 h-[700px] items-center -mt-[25px] shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
      <div class="col-span-11 lg:col-span-11 p-3">
        <h3 class="mb-3 text-start font-extrabold text-[34px] font-cairo">{{ 'letsGetStartedCompanyView' | translate }}
          <p class="text-end cursor-pointer font-normal text-gray-500" (click)="goBackStep()">رجوع</p>
        </h3>
        <div class="mt-[35px]">
            <h4 class="text-start font-normal text-xl dark:text-inherit ">{{'companyRegisteredOnYourNationality'|translate}}</h4>

        </div>

        <div class="selectedcount ">
             <div class="col-span-12 lg:col-span-12 my-2 flex items-center justify-center ">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 h-[15rem] overflow-hidden overflow-y-auto">
                    @for(cop of listOfOwnedCompanies(); track $index){
                        <div class="p-6 bg-white shadow rounded-lg dark:text-inherit  dark:bg-bg4 text-center w-[300px] h-[150px]"
                        [ngClass]="'dark:bg-bg4'"
                          [ngStyle]="{
                            'background-color': selectedCompany() === $index ? '#015C8D0D' : 'bg-inherit',
                            'border': selectedCompany() === $index ? '2px solid #015C8E' : '2px solid #E7E7E7'
                          }"
                           (click)="selectCompany($index,cop.crNumber,cop.crEntityNumber)"
                          >
                          <p class="font-bold">{{cop.crName}}</p>
                          <p class="mt-2">{{cop.crNumber}}</p>
                        </div>
                    }
                </div>

        </div>
        <h5 class="text-center font-normal text-md text-primary  cursor-pointer" (click)="addAnotherCompany()">{{'addAnotherCompany'|translate}}</h5>

        <div class="col-span-12 lg:col-span-12  my-12 mx-[24rem] items-center">
            <button class=" btn-primary px-36 py-3 rounded-[32px] text-center justify-center light:bg-gray-200 text-primary flex w-72 h-12 border border-[#015C8E]"
            (click)="nextStep()"
            >
           {{ 'next' |translate}}
                <img src="assets/images/arrownext.svg" class="w-6" />
            </button>

        </div>



      </div>

    </div>
    </div>

}@else{
    <div [formGroup]="companyForm" (ngSubmit)="onSubmit()" class="min-h-screen dark:bg-inherit bg-gray-50 flex justify-center items-center p-6">
      <div class="w-full flex flex-col justify-between max-w-4xl bg-white dark:bg-inherit shadow-lg rounded-lg p-8">
      <div class="w-full flex justify-between ">
          <!-- Title -->
           <div>
              <h2 class="text-2xl font-bold text-right">ملف الشركة</h2>
              <p class="text-lg font-semibold text-right text-gray-600  dark:text-inherit">المعلومات الرئيسية</p>
            </div>
            <button (click)="previousStep()" class="text-gray-500">رجوع</button>
         </div>
          <!-- Annual Business Size -->
          <div class="mt-6">
            <label class="block text-right text-gray-700 mb-2  dark:text-inherit">حجم الأعمال السنوية</label>

            <select formControlName="annualTurnover" class="w-full p-3  border rounded-xl text-right" placeholder="حجم الأعمال السنويةq">
              <option value="0">صغير</option>
              <option value="1">متوسط</option>
              <option value="2">كبير</option>
            </select>
          </div>

          <!-- National Address -->
          <div class="mt-6">
            <label class="block text-right text-gray-700 mb-2 dark:text-inherit">العنوان الوطني</label>
            <div class="w-full p-4 border rounded-lg bg-gray-100 dark:bg-inherit">
              <p class="text-right">📍{{CommercialRegisteraddress?.district?.name}}</p>
              <p class="text-right text-gray-600">{{CommercialRegisteraddress?.city}}</p>
              <p class="text-right text-gray-500">{{CommercialRegisteraddress?.buildingNumber}}{{CommercialRegisteraddress?.streetName}}</p>
            </div>
          </div>

          <!-- Upload Documents -->
          <div class="mt-6">
            <p class="text-lg font-semibold text-right text-gray-600">فضلًا أرفق جميع المستندات المذكورة أدناه</p>

            <!-- File Upload Fields -->
            <div class="mt-4">

                <app-file-upload [control]="companyForm.controls['ArticlesAssociation']" [isConvertToBase64]="false" label="عقد التأسيس" [placeholder]="'clickattachmenthere'" class="dark:bg-inherit"/>

            </div>

            <div class="mt-4">

              <app-file-upload [control]="companyForm.controls['ShareholdersResolution']" [isConvertToBase64]="false" label=" قرار صادر من جمعية المساهمين" [placeholder]="'clickattachmenthere'" class="dark:bg-inherit"/>

            </div>

            <div class="mt-4">
              <app-file-upload [control]="companyForm.controls['SupportingDocuments']" [isConvertToBase64]="false" label=" المستندات الداعمة" [placeholder]="'clickattachmenthere'" class="dark:bg-inherit"/>

          </div>
         </div>

          <!-- Buttons -->
          <div class="mt-8 flex justify-between">
            <button (click)="onSubmit()" type="submit" class="px-6 py-2 border border-blue-500 text-blue-500 rounded-xl flex items-end justify-end">
              التأكيد  ←
            </button>
          </div>
          </div>
        </div>

}
