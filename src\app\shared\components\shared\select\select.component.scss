label {
  @apply inline-block mb-2 text-base font-medium;
}


.ng-select {
  .ng-select-container {
    @apply bg-white p-0 form-input  dark:bg-zink-700 border-slate-200 dark:border-zink-500
    dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100
    placeholder:text-slate-400 dark:placeholder:text-zink-200;

    .ng-placeholder {
      @apply text-slate-500 dark:text-zink-200 #{!important};
    }

    .ng-value-label {
      @apply text-slate-500 dark:text-zink-200 #{!important};
    }
  }
}

.ng-dropdown-panel {
  @apply border-slate-200 dark:border-zink-500 #{!important};

  .ng-dropdown-panel-items {
    .ng-option {
      @apply bg-white dark:bg-zink-700 border-slate-200 text-slate-500 dark:text-zink-200 dark:border-zink-500;

      &.ng-option-marked {
        @apply text-slate-700 dark:text-zink-50 #{!important};
      }
    }
  }
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
  @apply text-slate-700 dark:text-zink-50 bg-white dark:bg-zink-700 #{!important};
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input > input {
  @apply text-slate-700 dark:text-zink-50 #{!important};
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  @apply bg-slate-100 dark:bg-zink-600 #{!important};
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  @apply bg-slate-100 dark:bg-zink-600 text-current #{!important};
}


.ng-select.ng-select-opened > .ng-select-container {
  @apply bg-white dark:bg-zink-700 border-slate-200 dark:border-zink-500 #{!important};
}

.ng-select.ng-select-disabled > .ng-select-container {
  @apply bg-slate-100 dark:bg-zink-600 #{!important};
}


.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
  @apply bg-primary-500 text-white border-primary-500 #{!important};

  .ng-value-label {
    @apply bg-primary-500 text-white border-primary-500 #{!important};
  }
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon:hover {
  @apply bg-primary-500 #{!important};
}

.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  @apply top-1/2 -translate-y-1/2 #{!important};
}

.ng-select.ng-select-focused > .ng-select-container {
  @apply border-primary-500 #{!important};
}

.ng-select .ng-select-container {
  @apply h-[38.33px] min-h-[38.33px] #{!important};
}


.ng-select.error .ng-select-container {
  @apply border-danger bg-danger/[0.08]  placeholder-danger/70 focus:border-danger;
}

.ng-select.error .ng-select-container .ng-placeholder {
  @apply text-danger #{!important};
}

.ng-select.ng-select-focused .ng-select-container .ng-placeholder {
  @apply text-slate-500 dark:text-zink-200 #{!important};
}

