<div class="col-span-12">
  <div class="mb-6 flex flex-wrap items-center justify-between gap-4 lg:mb-8">
    <h2>{{ 'ReservedPay' | translate }}</h2>
  </div>

  <div class="box col-span-12 lg:col-span-6">
    <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6">
      <h4 class="h4">{{ 'installments' | translate }}</h4>
      <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
        <form class="bg-primary/5 dark:bg-bg3 border border-n30 dark:border-n500 flex gap-3 rounded-[30px] 
          focus-within:border-primary p-1 items-center justify-between min-w-[200px] xxl:max-w-[319px] w-full">
          <input (input)="onInputChange($event)"
            class="bg-transparent border-none text-sm ltr:pl-4 rtl:pr-4 py-1 w-full"
            [placeholder]="'Search' | translate" type="text" />
          <button title="search" type="button"
            class="bg-primary shrink-0 rounded-full w-7 h-7 lg:w-8 lg:h-8 flex justify-center items-center text-n0">
            <i class="las la-search text-lg"></i>
          </button>
        </form>
        <button (click)="openModal()">
          <i class="la la-filter text-xl text-gray-800"></i>
        </button>
      </div>
    </div>

    <div class="mt-5 overflow-x-auto">
      <div class="datatable">
        <ng-datatable 
          (changeServer)="handePageChange($event.current_page)" 
          [columns]="columns"
          [isServerMode]="search.isServerMode" 
          [loading]="loanRequestService.pagination.loading"
          [page]="loanRequestService.pagination.page" 
          [rows]="loanRequests" 
          [showPageSize]="false"
          [totalRows]="loanRequestService.pagination.totalRows" 
          class="whitespace-nowrap table-hover"
          firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> 
            <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> 
            <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>'
          lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> 
            <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> 
            <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>'
          nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> 
            <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> 
          </svg>'
          paginationInfo=" {{ 'pagination_info' | translate:{ start: '{0}', end: '{1}', total: '{2}' } }}"
          previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> 
            <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> 
          </svg>'>

          <ng-template let-value="data" slot="loanRequestNo">
            <span class="cursor-pointer">
              {{ value.loanRequestNo }}
            </span>
          </ng-template>

          <ng-template let-value="data" slot="dueDate">
            {{ value.dueDate | date: 'yyyy/MM/dd' }}
          </ng-template>

          <ng-template let-value="data" slot="amount">
            {{ value.amount | number }}
          </ng-template>

          <ng-template let-value="data" slot="dueDays">
            {{ calculateRemainingDays(value.dueDate) | number }}
          </ng-template>

          <ng-template let-value="data" slot="status">
            <span [ngClass]="{
              'bg-gray-200 border-gray-400 text-gray-700 dark:bg-gray-800 dark:border-gray-900 dark:text-gray-300': value.status == 0, 
              'bg-yellow-200 border-yellow-400 text-yellow-700 dark:bg-yellow-800 dark:border-yellow-900 dark:text-yellow-300': value.status == 1,
              'bg-green-200 border-green-400 text-green-700 dark:bg-green-800 dark:border-green-900 dark:text-green-400': value.status == 2,
              'bg-orange-200 border-orange-400 text-orange-700  dark:bg-orange-800  dark:border-orange-900 dark:text-orange-400': value.requestStatus == 3,
              'bg-green-200 border-green-400 text-green-700 dark:bg-green-800 dark:border-green-900 dark:text-green-300': value.requestStatus == 4
            }"
              class="block w-28 rounded-[30px] border border-n30  py-2 text-center text-xs dark:border-n500 dark:bg-bg3 xxl:w-26">
              {{ StatusMap[value.status] || value.status | translate }}
            </span>
          </ng-template>

          <ng-template let-value="data" slot="Action">
            <button class="btn btn-primary" (click)="payInstallment(value.id)">
              {{ "PayNow" | translate }}
            </button>
          </ng-template>

        </ng-datatable> <!-- ✅ Correct closing -->
      </div>
    </div>
  </div>
</div>

<!-- ✅ Correct Angular conditional rendering -->
<ng-container *ngIf="modalService.isOpen('customFilterModal')">
  <app-custom-filter 
    id="customFilterModal" 
    [fields]="fields"
    [isFiltered]="isFiltered"
    [result]="filterResult">
  </app-custom-filter>
</ng-container>
