<div class=" box  p-3 md:p-4 xl:p-6 grid grid-cols-12 gap-6 items-center">
  <form [formGroup]="loginForm" class="col-span-12 lg:col-span-7" id="loginForm">
    <div class="box bg-primary/5 dark:bg-bg3 lg:p-6 xl:p-8 border border-n30 dark:border-n500">
      <h3 class="h3 mb-4 text-center">{{ 'welcomeBack'   |  translate }}</h3>
      <p class="md:mb-6 md:pb-6 mb-4 pb-4 bb-dashed text-sm md:text-base text-center"> {{ 'noAccount' | translate }}
        <a class="text-primary" routerLink="/auth/accountType"> {{ 'createAccount' | translate }}
        </a></p>
          <app-input
              [control]="loginForm.get('nationalId')"
              [label]="'nationalId'"
              [type]="'text'"
              [autofocus]="true"
            />
            <app-input
              (endIconClick)="togglePasswordVisibility()" [control]="loginForm.get('password')"
              [endIcon]="isPasswordHidden ? 'icon-[solar--eye-line-duotone]': 'icon-[solar--eye-closed-line-duotone]'"
              [label]="'password'"
              [type]="isPasswordHidden ? 'password': 'text'"
            />
     
      <!-- <p class="mt-3">
        {{ 'noAccount' | translate }}
        <a class="text-primary" routerLink="/auth/register"> {{ 'createAccount' | translate }}
        </a>
      </p> -->
      <div class="mt-8 flex justify-center gap-6">
        <app-button
          (onClick)="onSubmit()"
          [text]="'login'"
        ></app-button>
      </div>
      <a class="flex justify-center text-primary mt-1"
      routerLink="/auth/forgot-password"> {{ 'forgotPassword' | translate }} </a>
    </div>
  </form>
  <div class="col-span-12 lg:col-span-5">
    <img alt="img" height="560" src="assets/images/Illustration.svg" width="533"/>
  </div>
</div>
@if (modalService.isOpen('otp-modal')) {
  <app-otp-modal
    (submitOtp)="verifyOtp($event)"
  />
}
