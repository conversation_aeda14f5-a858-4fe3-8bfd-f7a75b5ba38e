@if (label) {
  <label [for]="name+ '' + id">
    {{ label | translate }}
    @if (this.required) {
      <span class="text-red-500"> *</span>
    }
  </label>
}
<div class="relative">
  @if (control) {
    <input
      [id]="name+ '' + id"
      [name]="name"
      (keydown.space)="onSpace.emit($event);"
      (keyup)="onKeyup.emit($event)"
      (change)="handleOnChange($event)"
      [formControl]="control"
      [ngClass]="{
        'readonly': readonly,
          'startIcon': startIcon,
        'endIcon': endIcon,
                  'error': control?.invalid && (control?.dirty || control?.touched)
      }"
      [placeholder]="placeholder ? (placeholder | translate) : label ? (label | translate) : ''"
      [required]="required"
      [type]="type"
      [autocomplete]="autocomplete"
      [autofocus]="autofocus"


    />
  } @else {
    <input
      [id]="name+ '' + id"
      [name]="name ?? 'unknown'"
      (keydown.space)="onSpace.emit($event);"
      (keyup)="onKeyup.emit($event)"
      (change)="handleOnChange($event)"
      [ngClass]="{
          'readonly': readonly,
          'startIcon': startIcon,
          'endIcon': endIcon,
          'error': control?.invalid && (control?.dirty || control?.touched),
      }"
      [placeholder]="placeholder ? (placeholder | translate) : label ? (label | translate) : ''"
      (ngModelChange)="inputModelChange.emit(inputModel)"
      [(ngModel)]="inputModel"
      [type]="type"
      [required]="required"
      [autocomplete]="autocomplete"
      [autofocus]="autofocus"
    />
  }
  @if (startIcon) {
    <div class="absolute inset-y-0 flex items-center w-8 justify-center">
      <!--      <lucide-angular-->
      <!--        (click)="endIconClick.emit()"-->
      <!--        [class]="'w-4 h-4 text-slate-500 dark:text-zink-200 justify-center fill-slate-100 dark:fill-zink-600'"-->
      <!--        [name]="startIcon"-->
      <!--      />-->
    </div>
  }
  @if (endIcon) {
    <div class="absolute inset-y-0 flex end-0 items-center w-8 justify-center">
      <span [ngClass]="endIcon"
            (click)="endIconClick.emit()"
            [class]="'w-5 h-5 me-3.5 hover:cursor-pointer text-slate-500 dark:text-zink-200 justify-center fill-slate-100 dark:fill-zink-600' "
      ></span>
      <!--      <lucide-angular-->
      <!--        (click)="endIconClick.emit()"-->
      <!--        [class]="'w-4 h-4 hover:cursor-pointer text-slate-500 dark:text-zink-200 justify-center fill-slate-100 dark:fill-zink-600'"-->
      <!--        [name]="endIcon"-->
      <!--      />-->
    </div>
  }
</div>
<ng-content></ng-content>

@if (control?.invalid && (control?.dirty || control?.touched) && label) {
  <div id="username-error" class="mt-1 text-sm text-red-500">
    {{ inputHelperService.getErrorMessage(control, label) }}
  </div>
}
