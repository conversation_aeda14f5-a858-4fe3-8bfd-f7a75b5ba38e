import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root'
})
export class SweetAlertHelper {
  showSuccess(title: string, text: string) {
    return Swal.fire({
      icon: 'success',
      title,
      text
    });
  }

  showError(title: string, text: string) {
    return Swal.fire({
      icon: 'error',
      title,
      text
    });
  }

  showWarning(title: string, text: string) {
    return Swal.fire({
      icon: 'warning',
      title,
      text
    });
  }

  showConfirm(title: string, text: string) {
    return Swal.fire({
      title,
      text,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes',
      cancelButtonText: 'Cancel'
    });
  }
}