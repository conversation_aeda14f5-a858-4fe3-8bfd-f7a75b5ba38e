import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';

enum ToastStatus {
    Success = 'success',
    Error = 'danger',
    Warning = 'warning,'
}

@Injectable({
    providedIn: 'root'
})
export class ToastService {
    constructor() {
    }

    error(message: string) {
        this.coloredToast(ToastStatus.Error, message);
    }

     success(message: string) {
        this.coloredToast(ToastStatus.Success, message);
    }

    private coloredToast(status: ToastStatus, message: string) {
        const toast = Swal.mixin({
            toast: true,
            position: 'top-start',
            showConfirmButton: false,
            timer: 3000,
            showCloseButton: false,
            customClass: {
                popup: `popup color-${status}`
            },
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutLeft'
            },
            target: document.getElementById(status + '-toast')
        });
        message && toast.fire({
            title: message
        });
    };
}
