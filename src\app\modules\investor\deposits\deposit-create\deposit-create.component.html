<ng-modals [id]="id" [placement]="'modal-top'"
           className="fixed flex flex-col   transition-all md:w-[30rem] duration-300 ease-in-out left-2/4 z-50 -translate-x-2/4 translate-y-8">

   <div class="relative bg-white dark:bg-slate-800 border-2 dark:text-inherit  p-8 rounded-lg shadow-2xl text-[#015C8E] ">
        <!-- Close Icon -->
        <button (click)="closeModal()" class="absolute top-4 end-4 text-[#015C8E] focus:outline-none hover:text-gray-300 transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </button>

      <h1 class="text-4xl font-extrabold mb-6 text-justify">{{ 'depositMethod' | translate }}</h1>
        <ol class="list-decimal list-inside rtl:text-right ltr:text-left text-lg leading-relaxed space-y-3">
            <li>{{ 'openPersonalBankAccountPage' | translate }}</li>
            <li>{{ 'clickAddBeneficiaryIcon' | translate }}</li>
            <li>
                {{ 'addInvestmentAccountMudad' | translate }}
                <span class="font-bold text-cyan-800 cursor-pointer" [cdkCopyToClipboard]="CollectioncountData?.accountNumber" matTooltip="Click to copy">{{CollectioncountData?.accountNumber}}</span>
            </li>
            <li>{{ 'addPersonalNameBeneficiary' | translate }}</li>
            <li>{{ 'activateBeneficiary' | translate }}</li>
            <li>{{ 'transferToInvestmentAccount' | translate }}</li>
        </ol>
        <p class="mt-6 rtl:text-right ltr:text-left text-lg">
            {{ 'arabBankPersonalAccount' | translate }}
            <span class="font-bold text-cyan-800 cursor-pointer" [cdkCopyToClipboard]="CollectioncountData?.iban" matTooltip="Click to copy">{{CollectioncountData?.iban}}</span>
            {{ 'asShownInFile' | translate }}
        </p>
        <p class="mt-6 rtl:text-right ltr:text-left text-cyan-800">{{ 'noteDepositProcess24Hours' | translate }}</p>
    </div>
</ng-modals>
