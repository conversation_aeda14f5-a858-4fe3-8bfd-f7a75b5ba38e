@if (isOpen) {
  @if (icon !== '') {
    <i class="{{icon}} text-xl ltr:mr-2 rtl:ml-2"></i>
  }
  <ng-content></ng-content>
  @if (dismissible) {
    <button (click)="close($event)" [ngClass]="closeButtonStyle">
      <lucide-angular [class]="'h-5'" name="x"></lucide-angular>
    </button>
    @if (closeButtonText) {
      {{ closeButtonText }}
    }
    <!-- <button *ngIf="!closeButtonStyle" class="absolute top-0 bottom-0 right-0 p-3 transition text-primary-200 hover:text-primary-500 dark:text-primary-400/50 dark:hover:text-primary-500"><lucide-angular name="variable" class="h-5"></lucide-angular></button> -->
    <!-- <button *ngIf="!closeButtonStyle" class="alert-close ltr:ml-auto rtl:mr-auto text-violet-400 text-lg" > <lucide-angular name="variable" *ngIf="closeButtonIcon" [class]="closeButtonIcon" class="h-5"></lucide-angular></button> -->
  }
}
