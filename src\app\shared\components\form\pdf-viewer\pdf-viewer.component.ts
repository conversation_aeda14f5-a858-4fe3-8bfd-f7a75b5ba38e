import {Component, ElementRef, Input, ViewChild} from '@angular/core';
import * as pdfjs from 'pdfjs-dist';

@Component({
  selector: 'app-pdf-viewer',
  standalone: true,
  imports: [],
  templateUrl: './pdf-viewer.component.html',
  styleUrl: './pdf-viewer.component.scss'
})
export class PdfViewerComponent {
  @ViewChild('pdfViewer') pdfViewer!: ElementRef;
  @Input() path!: string;
  pdfDoc: any;
  pageNum = 1;
  pageRendering = false;
  pageNumPending = null;

  constructor() {
    // pdfjsLib.GlobalWorkerOptions.workerSrc = '//mozilla.github.io/pdf.js/build/pdf.worker.js';
  }

  ngOnInit(): void {
    pdfjs.getDocument('path_to_your_pdf.pdf').promise.then((pdf: any) => {
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        pdf.getPage(pageNum).then((page: any) => {
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({ scale: 1.0 });

          canvas.width = viewport.width;
          canvas.height = viewport.height;

          const renderContext = {
            canvasContext: context!,
            viewport: viewport
          };

          page.render(renderContext);
          this.pdfViewer.nativeElement.appendChild(canvas);
        });
      }
    });
  }



}
