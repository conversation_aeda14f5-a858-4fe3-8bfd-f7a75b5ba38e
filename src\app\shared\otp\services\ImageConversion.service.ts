import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ImageConversionService {

  constructor(private http: HttpClient) {}

  async imageUrlToBlob(url: string): Promise<Blob> {
    
    return await firstValueFrom(this.http.get(url, { responseType: 'blob' }));
  }

  async imageUrlToFile(url: string, filename: string): Promise<File> {
    debugger
    const blob = await this.imageUrlToBlob(url);
    return new File([blob], filename, { type: blob.type });
  }

  async  urlToBlob(imageUrl: string): Promise<Blob> {
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.blob();
  }
}
