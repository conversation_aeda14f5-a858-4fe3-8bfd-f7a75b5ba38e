interface SubmenuItem {
  title: string;
  url: string;
}

export interface SidebarItem {
  id: number;
  name: string;
  icon: string;
  url: string;
  submenus?: SubmenuItem[];
}

export const sidebarData: SidebarItem[] = [
  {
    id: 1,
    name: 'Dashboard',
    icon: 'las la-home',
    url: '/'
  },
  {
    id: 2,
    name: 'OpenOpportunities',
    icon: 'las la-coins',
    url: '/opportunities',

  },
  {
    id: 3,
    name: 'MyInvestments',
    icon: 'las la-exchange-alt',
    url: '/investments'
  },
  {
    id: 4,
    name: 'loanRequests',
    icon: 'las la-file-invoice',
    url: '/loanRequest'
  },
  {
    id: 5,
    name: 'DepositsList',
    icon: 'las la-wallet',
    url: '/deposits'
  },
  {
    id: 6,
    name: 'WithdrawsList',
    icon: 'las la-coins',
    url: '/withdrawals'
  },

  {
    id: 7,
    name: 'Reports',
    icon: 'las la-chart-pie',
    url: '/reports'
  },
  {
    id: 9,
    name: 'ReservedPay',
    icon: 'las la-coins',
    url: '/ReservedPay'
  },
  // {
  //   id: 10,
  //   name: 'Operations',
  //   icon: 'las la-handshake',
  //   url: '/dashboard'
  // },
  {
    id: 11,
    name: 'bankAccountstatment',
    icon: 'las la-handshake',
    url: '/bankstatment'
  },
  {
    id: 8,
    name: 'Settings',
    icon: 'las la-cog',
    url: '/settings'
  }



]
