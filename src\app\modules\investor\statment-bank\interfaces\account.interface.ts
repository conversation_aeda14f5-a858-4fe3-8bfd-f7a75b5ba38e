export interface JournalLine {
    id: string;
    notes: string;
    lastUpdateBy: string;
    lastUpdateById: string;
    lastUpdateDate: Date;
    deletedAt: Date;
    isDeleted: boolean;
    title: string;
    titleEn: string;
    journalEntryId: string;
    ledgerAccountId: string;
    journalLineType: number;
    debit: number;
    credit: number;
    account: string;
}

export interface AccountModel {
    id: string;
    notes: string;
    lastUpdateBy: string;
    lastUpdateById: string;
    lastUpdateDate: Date;
    deletedAt: Date;
    isDeleted: boolean;
    userAccountId: string;
    userAccountName: string;
    loanRequestId: string;
    loanRequestNo: string;
    name: string;
    code: string;
    type: number;
    balance: number;
    createdDate: Date;
    parentId: string;
    isReservable: boolean;
    parent?: AccountModel;
    children: any[];
    journalLines: JournalLine[];
}
