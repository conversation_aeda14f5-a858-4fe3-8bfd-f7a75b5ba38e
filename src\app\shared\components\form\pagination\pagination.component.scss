*, ::backdrop, :after, :before {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia:
}

*, :after, :before {
  border: 0 solid #0000;
  box-sizing: border-box
}

.bh-datatable .bh-table-responsive {
  position: relative;
  width: 100%;
  overflow: auto;
  border-radius: .25rem
}

.bh-datatable .bh-table-responsive table {
  width: 100%;
  max-width: 100%;
  border-collapse: collapse !important
}

.bh-datatable .bh-table-responsive table tfoot tr, .bh-datatable .bh-table-responsive table thead tr {
  --tw-bg-opacity: 1;
  background-color: rgb(246 247 250/var(--tw-bg-opacity))
}

.bh-datatable .bh-table-responsive table tbody tr td, .bh-datatable .bh-table-responsive table tfoot tr th, .bh-datatable .bh-table-responsive table thead tr th {
  padding: .75rem 1rem;
  text-align: left
}

.bh-datatable .bh-table-responsive table tfoot tr th, .bh-datatable .bh-table-responsive table thead tr th {
  vertical-align: top;
  font-weight: 700
}

.bh-datatable .bh-table-responsive table tbody tr {
  border-bottom-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(246 247 250/var(--tw-border-opacity))
}

.bh-datatable .bh-table-responsive table.bh-table-striped tbody tr:nth-child(odd) {
  background-color: #e0e6ed26
}

.bh-datatable .bh-table-responsive table.bh-table-hover tbody tr:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.bh-datatable .bh-table-responsive table.bh-table-compact tbody tr td, .bh-datatable .bh-table-responsive table.bh-table-compact thead tr th {
  padding: .5rem .75rem
}

.bh-datatable .bh-table-responsive table.bh-table-bordered tbody tr td, .bh-datatable .bh-table-responsive table.bh-table-bordered thead tr th {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(246 247 250/var(--tw-border-opacity))
}

.bh-datatable .bh-pagination .bh-page-item {
  display: grid;
  height: 2rem;
  width: 2rem;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  place-content: center;
  border-radius: 9999px;
  border: 1px solid #0e17264d;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255/var(--tw-bg-opacity));
  padding: .375rem .625rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99/var(--tw-text-opacity));
  outline-width: 0
}

.bh-datatable .bh-pagination .bh-page-item:hover {
  --tw-border-opacity: 1;
  border-color: rgb(67 97 238/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(67 97 238/var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255/var(--tw-text-opacity))
}

.bh-datatable .bh-pagination .bh-page-item {
  -webkit-appearance: button;
  background-image: none
}

.bh-datatable .bh-pagination .bh-page-item.disabled:not(.bh-active) {
  pointer-events: none;
  opacity: .5
}

.bh-datatable .bh-pagination .bh-page-item.bh-active {
  --tw-border-opacity: 1;
  border-color: rgb(67 97 238/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(67 97 238/var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255/var(--tw-text-opacity))
}

.bh-datatable .bh-table-responsive button, .bh-datatable .bh-table-responsive input {
  outline: 2px solid #0000;
  outline-offset: 2px
}

.bh-datatable .bh-pagination-info .bh-pagesize {
  box-sizing: border-box;
  border-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(224 230 237/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255/var(--tw-bg-opacity));
  padding: .375rem .5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(14 23 38/var(--tw-text-opacity));
  outline-width: 0
}

.bh-datatable .bh-pagination-info .bh-pagesize:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: #e0e6ed66
}

.bh-datatable .bh-table-responsive table th .bh-filter {
  margin-top: .125rem;
  display: flex;
  height: 30px;
  width: 100%;
  align-items: center
}

.bh-datatable .bh-table-responsive table th .bh-filter > .bh-form-control {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  min-width: 60px;
  border-top-left-radius: .25rem;
  border-bottom-left-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(224 230 237/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255/var(--tw-bg-opacity));
  padding: .25rem .75rem;
  font-size: .875rem;
  line-height: 1.25rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(14 23 38/var(--tw-text-opacity));
  outline-width: 0
}

.bh-datatable .bh-table-responsive table th .bh-filter > .bh-form-control:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: #e0e6ed66
}

.bh-datatable .bh-table-responsive table th .bh-filter > select {
  border-radius: .25rem
}

.bh-datatable .bh-table-responsive table th .bh-filter > button {
  display: grid;
  height: 30px;
  width: 30px;
  flex-shrink: 0;
  cursor: pointer;
  place-content: center;
  border-top-right-radius: .25rem;
  border-bottom-right-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(224 230 237/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(224 230 237/var(--tw-bg-opacity));
  color: #0e1726b3
}

.bh-datatable .bh-table-responsive table th .bh-filter > button:hover {
  color: #0e1726e6
}

.bh-datatable .bh-table-responsive table th .bh-filter > button {
  -webkit-appearance: button;
  background-image: none
}

.bh-datatable .bh-filter-menu button {
  display: flex;
  width: 100%;
  cursor: pointer;
  border: 1px solid #0000;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255/var(--tw-bg-opacity));
  padding: .375rem 1rem;
  text-align: left
}

.bh-datatable .bh-filter-menu button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246/var(--tw-bg-opacity));
  font-weight: 700
}

.bh-datatable .bh-filter-menu button {
  -webkit-appearance: button;
  background-image: none
}

.bh-datatable .bh-filter-menu button.active {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246/var(--tw-bg-opacity));
  font-weight: 700
}

.bh-datatable .bh-table-responsive input[type=checkbox] {
  position: absolute;
  height: 1.25rem;
  width: 1.25rem;
  opacity: 0
}

.bh-datatable .bh-table-responsive input[type=checkbox] + div {
  display: grid;
  height: 1.25rem;
  width: 1.25rem;
  place-content: center;
  border-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(224 230 237/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.bh-datatable .bh-table-responsive input[type=checkbox] + div svg {
  pointer-events: none;
  display: none;
  height: .75rem;
  width: .75rem;
  fill: currentColor;
  --tw-text-opacity: 1;
  color: rgb(67 97 238/var(--tw-text-opacity))
}

.bh-datatable .bh-table-responsive input[type=checkbox]:checked + div, .bh-datatable .bh-table-responsive input[type=checkbox]:indeterminate + div {
  --tw-border-opacity: 1;
  border-color: rgb(67 97 238/var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(67 97 238/var(--tw-bg-opacity))
}

.bh-datatable .bh-table-responsive input[type=checkbox]:checked + div svg.check, .bh-datatable .bh-table-responsive input[type=checkbox]:indeterminate + div svg.intermediate {
  display: flex;
  --tw-text-opacity: 1;
  color: rgb(255 255 255/var(--tw-text-opacity))
}

.bh-absolute {
  position: absolute
}

.bh-relative {
  position: relative
}

.bh-sticky {
  position: sticky
}

.bh-inset-0 {
  top: 0;
  right: 0;
  left: 0
}

.bh-bottom-0, .bh-inset-0 {
  bottom: 0
}

.bh-left-0 {
  left: 0
}

.bh-left-\[52px\] {
  left: 52px
}

.bh-right-0 {
  right: 0
}

.bh-top-0 {
  top: 0
}

.bh-top-full {
  top: 100%
}

.bh-z-10 {
  z-index: 10
}

.bh-z-\[1\] {
  z-index: 1
}

.bh-mb-2 {
  margin-bottom: .5rem
}

.bh-ml-3 {
  margin-left: .75rem
}

.bh-mr-2 {
  margin-right: .5rem
}

.bh-mt-1 {
  margin-top: .25rem
}

.bh-flex {
  display: flex
}

.bh-inline-flex {
  display: inline-flex
}

.bh-grid {
  display: grid
}

.bh-hidden {
  display: none
}

.bh-h-11 {
  height: 2.75rem
}

.bh-h-8 {
  height: 2rem
}

.bh-min-h-\[300px\] {
  min-height: 300px
}

.bh-w-32 {
  width: 8rem
}

.bh-w-4 {
  width: 1rem
}

.bh-w-px {
  width: 1px
}

.bh-cursor-pointer {
  cursor: pointer
}

.bh-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.bh-flex-col {
  flex-direction: column
}

.bh-flex-wrap {
  flex-wrap: wrap
}

.bh-place-content-center {
  place-content: center
}

.bh-items-center {
  align-items: center
}

.bh-gap-4 {
  gap: 1rem
}

.bh-space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * (1 - var(--tw-space-x-reverse)))
}

.bh-overflow-hidden {
  overflow: hidden
}

.bh-rounded {
  border-radius: .25rem
}

.bh-rounded-md {
  border-radius: .375rem
}

.bh-border {
  border-width: 1px
}

.bh-border-solid {
  border-style: solid
}

.\!bh-border-transparent {
  border-color: #0000 !important
}

.bh-border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.bh-border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.\!bh-bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255/var(--tw-bg-opacity)) !important
}

.bh-bg-blue-light {
  --tw-bg-opacity: 1;
  background-color: rgb(246 247 250/var(--tw-bg-opacity))
}

.bh-bg-blue-light\/50 {
  background-color: #f6f7fa80
}

.bh-bg-gray-200 {
  background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.bh-bg-gray-200, .bh-bg-white {
  --tw-bg-opacity: 1
}

.bh-bg-white {
  background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.\!bh-p-0 {
  padding: 0 !important
}

.bh-p-10 {
  padding: 2.5rem
}

.bh-p-2 {
  padding: .5rem
}

.bh-py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem
}

.bh-text-\[13px\] {
  font-size: 13px
}

.bh-text-sm {
  font-size: .875rem;
  line-height: 1.25rem
}

.bh-font-normal {
  font-weight: 400
}

.\!bh-text-primary {
  --tw-text-opacity: 1 !important;
  color: rgb(67 97 238/var(--tw-text-opacity)) !important
}

.bh-text-black {
  --tw-text-opacity: 1;
  color: rgb(14 23 38/var(--tw-text-opacity))
}

.bh-text-black\/20 {
  color: #0e172633
}

.bh-text-primary {
  --tw-text-opacity: 1;
  color: rgb(67 97 238/var(--tw-text-opacity))
}

.bh-antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.bh-shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.bh-outline-0 {
  outline-width: 0
}

.bh-filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.bh-skeleton-box {
  position: relative;
  width: 100%;
  overflow: hidden;
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.bh-skeleton-box:after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  animation: bhshimmer 2s infinite;
  background-image: linear-gradient(90deg, #0000, rgba(0, 0, 0, .025) 20%, #0000000d 50%, #0000);
  --tw-content: "";
  content: var(--tw-content)
}

@keyframes bhshimmer {
  to {
    transform: translateX(100%)
  }
}

.hover\:bh-opacity-80:hover {
  opacity: .8
}

.focus\:bh-border-gray-200:focus {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235/var(--tw-border-opacity))
}

@media (min-width: 640px) {
  .sm\:bh-ml-auto {
    margin-left: auto
  }
  .sm\:bh-flex-row {
    flex-direction: row
  }
}

;



