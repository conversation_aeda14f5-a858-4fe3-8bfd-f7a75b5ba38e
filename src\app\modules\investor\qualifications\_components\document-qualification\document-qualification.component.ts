import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { RouterLink } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { DropzoneModule } from 'ngx-dropzone-wrapper';
import { QualificationService } from '../../Services/qualification.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-document-qualification',
  standalone: true,
  imports: [
   
    TranslateModule,
    CommonModule,
    DropzoneModule,
    NgxDropzoneModule,
  ],
  providers: [TranslateService, ToastrService],
  templateUrl: './document-qualification.component.html',
  styleUrl: './document-qualification.component.css',
})
export class DocumentQualificationComponent {
  // files: File[] = [];
  files: Array<{ name: string; type: string; dataUrl?: string }> = [];
  @Output() filesChange = new EventEmitter<
    Array<{ name: string; type: string; dataUrl?: string }>
  >();

  constructor(
    private qualificationService: QualificationService,
    private toastr: ToastrService,

    public translate: TranslateService
  ) {}
  onRemove(event: any) {
    this.files.splice(this.files.indexOf(event), 1);
    this.filesChange.emit(this.files);
  }

  onSelect(event: any) {
    this.files = [];
    event.addedFiles.forEach((file: File) => {
      if (!['image/png', 'image/jpeg', 'application/pdf'].includes(file.type)) {
        this.toastr.error(
          this.translate.instant('ErrorTitle'),
          this.translate.instant('TypeError')
        );
      } else {
        const reader = new FileReader();

        reader.onload = () => {
          this.files.push({
            name: file.name,
            type: file.type,
            dataUrl: reader.result as string, // For image previews
          });
          this.filesChange.emit(this.files);
        };

        if (file.type.startsWith('image/')) {
          reader.readAsDataURL(file); // Read image files as data URLs
        } else {
          this.files.push({
            name: file.name,
            type: file.type,
          }); // For non-image files, no preview needed
        }
        this.filesChange.emit(this.files);
      }
    });

    this.filesChange.emit(this.files);
  }
  // onSelect(event:any) {
  //   event.addedFiles.forEach((file:File) => {
  //     if (!["image/png", "image/jpeg", "application/pdf"].includes(file.type)) {
  //       this.toastr.error(
  //         this.translate.instant("ErrorTitle"),
  //         this.translate.instant("TypeError"),

  //       );
  //     } else this.files.push(file);
  //   });
  // }
}
