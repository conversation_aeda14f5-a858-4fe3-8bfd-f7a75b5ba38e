import {
  ApexAnnotations,
  ApexAxisChartSeries,
  ApexChart,
  ApexDataLabels,
  ApexFill,
  ApexGrid,
  ApexLegend,
  ApexMarkers,
  ApexNonAxisChartSeries,
  ApexPlotOptions,
  ApexStroke,
  ApexTooltip,
  ApexXAxis,
  ApexYAxis
} from 'ng-apexcharts'

export type ChartOptions = {
  series: ApexAxisChartSeries | ApexNonAxisChartSeries
  chart: ApexChart
  xaxis?: ApexXAxis
   yaxis?: ApexYAxis | ApexYAxis[]
  dataLabels?: ApexDataLabels
  grid?: ApexGrid
  stroke?: ApexStroke
  markers?: ApexMarkers
  colors?: string[]
  tooltip?: ApexTooltip
  fill?: ApexFill
  responsive?: any
  legend?: ApexLegend
  plotOptions?: ApexPlotOptions
  labels?: string[]
  annotations?: ApexAnnotations
}
