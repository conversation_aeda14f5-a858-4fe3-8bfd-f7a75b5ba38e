import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { QualificationModel } from '../../interface/qualification.model';
import { ToastrService } from 'ngx-toastr';
import { QualificationService } from '../../Services/qualification.service';

import { QualificationReason, QualificationStatus } from '../../../../../shared/enums/qualification.enum';
import { EnumSelectPipe } from '../../../../../token-mask.pipe';

@Component({
  selector: 'app-upgrade-qualificattion',
  standalone: true,
  imports: [EnumSelectPipe, TranslateModule, CommonModule, EnumSelectPipe],
  templateUrl: './upgrade-qualificattion.component.html',
  styleUrl: './upgrade-qualificattion.component.css',
})
export class UpgradeQualificattionComponent implements OnInit {
  qualification: QualificationModel = {};
  @Output() qualificationChange = new EventEmitter<QualificationModel | null>();

  @Output() isQualificationSelected = new EventEmitter<boolean>();

  constructor(
    private qualificationService: QualificationService,
    private toastr: ToastrService,
    private router: Router,
    public translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.fetchQualifications();
  }

  onQualificationChange() {
    this.qualificationChange.emit(this.qualification);
    this.isQualificationSelected.emit(true);
  }

  updateQualificationReason(reason: any) {
    if (this.qualification) {
      this.qualification.qualificationReason = reason;
      this.onQualificationChange();
    }
  }

  fetchQualifications(): void {
    this.qualificationService.investorQualifications().subscribe((res) => {
     

      // if (res.list.length !== 0) {
      //   this.qualification = res.list[0];
      // } else
      //   this.qualification = {
      //     qualificationID: null,
      //     userID: null,
      //     userName: null,
      //     qualificationReason: null,
      //     status: null,
      //     createdOn: null,
      //     attachments: null,
      //   };
    });
  }

  public get QualificationStatus(): typeof QualificationStatus {
    return QualificationStatus;
  }

  getLabel(key: string): string {
    return this.translate.instant(key);
  }

  public get QualificationReason(): typeof QualificationReason {
    return QualificationReason;
  }
}
