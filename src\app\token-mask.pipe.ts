import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'tokenMask',
})
export class TokenMaskPipe implements PipeTransform {
  transform(value: string): string {
    if (value.length <= 5) {
      return value;
    } else {
      return value.slice(0, 5) + '*'.repeat(30 - 5);
    }
  }
}
@Pipe({
  name: 'enum',
  standalone: true,
})
export class EnumSelectPipe implements PipeTransform {
  transform(value: any): [number, string][] {
    return Object.keys(value)
      .filter((t) => isNaN(+t))
      .map((t) => [value[t], t]);
  }
}
