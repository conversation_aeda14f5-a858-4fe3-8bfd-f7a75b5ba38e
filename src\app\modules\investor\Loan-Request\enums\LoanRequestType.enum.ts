export enum LoanRequestType{
    Invoicing = 1,
    PurchaseOrder = 2,
    SupplyChain= 3
}

export enum AttachmentFileType
{
    DelegationLetter=1,
    Cashflow=2,
    FinancialStatements = 3,
    Other=4
}

export enum LoanRequestDetailSource
{
    Governmental,
     Private
}

export interface IlistDetails{
 id:number,

clientCRNumber :string;
type :number;
amount :number;
invoiceVAT :number;
file:any;
invoicePONumber?:number
typeOfLoan?:any

  }

  export enum LoanRequestStatus
{
    Draft = 0,
    New = 1,
    PaymentPending = 2,
    InProgress = 3,
    Rejected = 4,
    Scoring = 5,
    AwaitingInternalApproval=6,
    ScoreRejected = 7,
    ScoringApproved = 8,
    AwaitingClientApproval = 9,
    DeniedByClient = 10,
    CollectingFunds = 11, // Action: Collect funds from the client (Close opportunity and change status to AwaitingContract)
    AwaitingContract=12, // Action: Generate contract
    ContractGenerated=13, //Action: Send to SignIt for signature
    ContractSignaturePending = 14, // Action: Sign the contract by the client and the lender
    AwaitingCollateral=15, //Action: Generate collateral Request
    CollateralAcceptancePending=16, // Action: Accept collateral by the client
    CollateralAccepted=17, // Action: Accept collateral by the lender
    CollateralRejected=18, // Action: can re-send collateral request or change status to cancelled and refund the investors.
    AwaitingFundTransfer =19 , // Action: Transfer funds to the client
    FundsDisbursed = 20,
    CollectionInProgress = 21,
 
    OverDue = 22,
    PartiallyPaid = 23, 
    Paid = 24,
    Completed = 25
}