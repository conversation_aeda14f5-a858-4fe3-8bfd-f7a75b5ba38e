import { IQuery } from "../../../../core/models/pagination.model";


export interface WalletModel {
  walletID: string;
  name: string;
  createdOn: Date;
  userID: string;
  userFullName: string;
  balance: number;
  isDeleted: boolean;
  isSelected: boolean;
  notes: string;
  lastUpdateBy: string;
  lastUpdateByName: string;
  lastUpdateOn: Date;
  actionName: string;
}

export interface WalletSearch extends IQuery {
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: string;
  itemsCount?: number;
  limit: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
}

export interface WalletCreateCommand {
  name: string;
  notes?: string;
}

export interface WalletTransferCommand {
  fromWalletID: string;
  toWalletID: string;
  amount: number;
  mfacode: string;
}

export interface WalletUpdateCommand {
  walletID: string;
  name?: string;
  notes?: string;
}

export interface UserUpdateWalletCommand {
  walletID: string;
  userID: string;
  mfacode?: number;
}

export interface WalletSummaryModel {
  walletID: string;
  walletName: string;
  balance: number;
  upCommingBalance: number;
  totalInvestments: number;
  totalInvestmentsCount: number;
  totalProfits: number;
  profitsPerAverge: number;
  totalProfitsCount: number;
  totalDays: number;
  currentInvestments: number;
  currentInvestmentsCount: number;
  currentProfits: number;
  currentProfitsCount: number;
}
