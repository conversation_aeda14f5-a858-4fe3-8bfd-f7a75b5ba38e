import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {FormControl, ReactiveFormsModule} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { InputHelperService } from '../../services/input-helper.service';

@Component({
  selector: 'app-checkbox',
  standalone: true,
  imports: [
    TranslateModule,
    ReactiveFormsModule,
  ],
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss']
})
export class CheckboxComponent implements OnInit {
  @Input() label: string = '';
  @Input() description?: string;
  @Input() control: any;
  @Input() id: number | string = '';
  @Input() value: boolean = false;
  @Input() disabled: boolean = false;
  @Output() onChange = new EventEmitter<boolean>();
  name: string = 'text';

  constructor(private inputHelperService: InputHelperService) { }

  ngOnInit(): void {
    this.name = this.inputHelperService.getFormControlName(this.control) || this.label || this.description || this.name;
  }

  handleOnChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    this.onChange.emit(checked);
  }
}
