import {Component, ComponentRef} from '@angular/core';
import {ModalComponent} from "../modal/modal.component";
import {ModalService} from "../modals/modal.service";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {ButtonComponent} from "../button/button.component";
// import {LucideAngularModule} from "lucide-angular";
import {MDModalModule} from "../modals";
import {TranslateModule} from "@ngx-translate/core";
import {NgClass, NgOptimizedImage} from "@angular/common";
import {HttpClient} from "@angular/common/http";

@Component({
  selector: 'app-file-preview',
  standalone: true,
  imports: [
    ModalComponent,
    ButtonComponent,
    // LucideAngularModule,
    MDModalModule,
    TranslateModule,
    NgClass,
    NgOptimizedImage
  ],
  templateUrl: './file-preview.component.html',
  styleUrl: './file-preview.component.scss'
})
export class FilePreviewComponent {
  public componentRef!: ComponentRef<FilePreviewComponent>;
  file?: string | undefined;
  fileName?: string | undefined;
  safeFileUrl: SafeResourceUrl | undefined | string;
  isPdf = false;

  constructor(
    private modalService: ModalService,
    private sanitizer: DomSanitizer,
    private http: HttpClient
  ) {
  }

  handleCloseModal() {
    this.file && this.modalService.close(this.file);
    this.file = undefined;
    this.componentRef.destroy();
  }

  download() {
    const downloadLink = document.createElement('a');
    downloadLink.href = this.file!;
    if (this.isPdf) {
      downloadLink.download = this.fileName ?? 'download';
    } else {
      downloadLink.target = '_blank';
    }
    downloadLink.click();
    this.handleCloseModal();
  }

  open(file: {
    name?: string,
    content?: string,
    contentType?: string,
    url?: string,
  }) {
    file.content = file.url || `data:${file.contentType || 'application/pdf'};base64,${file.content}`
    this.safeFileUrl = this.getSafeFileUrl(file.content);
    this.file = file.content;
    this.fileName = file.name;
    this.modalService.open(this.file);
  }

  private getSafeFileUrl(file: any): SafeResourceUrl {
    if (!file) {
      this.isPdf = false;
      return this.sanitizer.bypassSecurityTrustResourceUrl('/assets/images/file-preview.svg');
    }
    if (typeof file === 'string') {
      if (
        // file.startsWith('http') ||
        // file.startsWith('https') ||
        // !file.includes('data:application/pdf;base64,') ||
        file.endsWith('png') ||
        file.endsWith('jpg') ||
        file.endsWith('jpeg')
      ) {
        this.isPdf = false;
        return file;
      }
      this.isPdf = true;
      return this.sanitizer.bypassSecurityTrustResourceUrl(file);
    }
    // Handle cases where 'file' is not a string, e.g., a Blob or other data
    // const localUrl = URL.createObjectURL(file);
    return file;
  }
}
