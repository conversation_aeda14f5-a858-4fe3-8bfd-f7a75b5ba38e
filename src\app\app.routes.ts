import {Routes} from '@angular/router';


import {ErrorComponent} from "@pages/error/error.component";
import {DefaultLayoutComponent} from "@component/layouts/default-layout/default-layout.component";
import {AuthLayoutComponent} from "@component/layouts/auth-layout/auth-layout.component";
import {authenticatedGuard} from "./core/guards/auth.guard";
import { DashboardComponent } from './modules/investor/dashboard/dashboard/dashboard.component';
import { ChangePasswordComponent } from './modules/auth/change-password/change-password.component';

export const routes: Routes = [
  {
    path: '',
    component: DefaultLayoutComponent,
    canActivate: [authenticatedGuard],
    children: [
      {
        path: '',
        pathMatch: 'full',
        component: DashboardComponent
      },
      {
        path: 'opportunities',
        loadChildren: () => import('./modules/investor/opportunities/opportunities-routing.module').then(m => m.OpportunitiesRoutingModule)
      },
      {
        path: 'investments',
        loadChildren: () => import('./modules/investor/investments/investments-routing.module').then(m => m.InvestmentsRoutingModule)
      },
      {
        path: 'deposits',
        loadChildren: () => import('./modules/investor/deposits/deposits-routing.module').then(m => m.DepositsRoutingModule)
      },
      {
        path: 'withdrawals',
        loadChildren: () => import('./modules/investor//withdrawals/withdrawals-routing.module').then(m => m.WithdrawalsRoutingModule)
      },
      {
        path: 'transactions',
        loadChildren: () => import('./modules/investor/transactions/transactions-routing.module').then(m => m.TransactionsRoutingModule)
      },
      {
        path: 'reports',
        loadChildren: () => import('./modules/investor/reports/reports-routing.module').then(m => m.ReportsRoutingModule)
      },
      {
        path: 'bankstatment',
        loadChildren: () => import('./modules/investor/statment-bank/statment-routing.module').then(m => m.StatmentRoutingModule)
      },
      {
        path: 'loanRequest',
        loadChildren: () => import('./modules/investor/Loan-Request/loan-request-routing.module').then(m => m.LoanRequestRoutingModule),
        
      },
      {
        path: 'settings',
        loadChildren: () => import('./modules/settings/settings-routing.module').then(m => m.SettingsRoutingModule)
      },
      
      {
        path: 'ReservedPay',
        loadChildren: () => import('./modules/installments-list/installments.module').then(m => m.installmentsModule)
      }
    ]
  },
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: '',
        loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)
      }
    ]
  },
    { 
    path: 'change-password', 
    component: ChangePasswordComponent,
    // Choose one:
    canActivate: [] // No guard (accessible with token)

  },
  {
    path: 'not-found',
    pathMatch: 'full',
    component: ErrorComponent
  },
  {
    path: '**',
    redirectTo: 'not-found',
    pathMatch: 'full',
  }
];
