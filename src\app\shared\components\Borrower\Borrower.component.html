<div
class="box xl:p-6   dark:bg-bg4 font-cairo grid grid-cols-12 gap-2 xxxl:gap-5 h-[1000px] items-start -mt-[25px] shadow-[0px_6px_30px_0px_rgba(0,0,0,0.04)]">
<div class="col-span-11 lg:col-span-11 p-3">

  <div class="w-full flex justify-between ">
    <!-- Title -->
     <div>
        <h3 class="mb-3 text-start font-extrabold text-[34px] font-cairo">{{ 'BorrowerTitle' | translate }}</h3>

        
      </div>
      @if(isInner()){
        <button (click)="previousStep()" class="text-gray-500">{{"back"| translate }}</button>
      }@else {
        <p class="text-base cursor-pointer" (click)="goBackStep()">{{'back'|translate}}</p>
      }
   </div>
  <div class="mt-[35px]">
      <h4 class="text-start font-normal text-xl  ">{{'BorrowerSubTitle'|translate}}</h4>
      <div class="flex justify-start  my-5">
        <app-input
        [control]="borrowerForm.get('cr')"
        [type]="'text'"
        [autofocus]="true"
        class="w-704px"
        [maxLength]="10"
      />
    
      </div>
  </div>

  <div class="col-span-12 lg:col-span-12 my-2  items-start justify-start block">
    <h4 class="text-start font-normal text-xl  ">{{'EnterCRNationatnaumber'|translate}}</h4>
    <div class="flex justify-start  my-7">
     
      <app-input
      [control]="borrowerForm.get('crNationalNumber')"
      [type]="'text'"
      [autofocus]="true"
      class="w-[40rem]"
      [maxLength]="10"
    />
  
    </div>
  
  
  </div>
  <div class="flex w-full  my-3 justify-end items-center">
    <button class=" btn-primary px-36 py-3 hover:bg-primary hover:text-white rounded-[32px] text-center justify-center bg-inherit text-primary flex w-96 h-12 border  border-[#015C8E]"
    (click)="handleBorrowerCheck()"
    >
   {{ 'BorrowerCheck' |translate}}
        <img src="assets/images/arrownext.svg" class="w-6" />
    </button>
  </div>

<div class="col-span-12 lg:col-span-12  flex items-start justify-start">




@if(reslut){
  <form [formGroup]="borrowerForm" class="col-span-12 lg:col-span-12  items-start p-3 space-y-2 overflow-auto w-full">
    <div class="w-full max-w-md bg-inherit text-inherit p-4 rounded-lg text-right dark:border-2 dark:border-slate-100">
      <p class="text-gray-700 font-medium dark:text-inherit">{{reslut?.data?.name	 ||("notfound"|translate)}}</p>
      <p class="text-gray-500 dark:text-inherit">{{reslut?.data.crNumber}}</p>
    </div>
      <div class="w-full max-w-md" >
      <label class="block text-right text-gray-700 font-medium mb-1 dark:text-inherit">رقم التفويض</label>
      <input
        type="text"
        formControlName="commercialRegistrationNumber"
        placeholder="أدخل رقم التفويض ..."
        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 dark:text-inherit focus:ring-blue-500"
      />
    </div>
      <div class="w-full max-w-md">

       <app-date-picker
      [label]="'Authorizationdate'"
      [placeholder]="'Authorizationdate'"
      [control]="borrowerForm.get('commercialRegistrationDate')"
    >
    </app-date-picker>
   <div class="mt-4">

                <app-file-upload [control]="borrowerForm.controls['DelegationFile']" [isConvertToBase64]="false" label="" [placeholder]="'clickattachmenthere'" class="dark:bg-inherit"/>

            </div>
    </div>

    <!-- Next Button -->
    <div class="w-full flex justify-end">
      <button
    
        (click)="nextStep()"
        class="items-center px-6 py-2 border  hover:bg-primary hover:text-white rounded-[32px] text-center justify-center bg-gray-200 text-primary flex w-96 h-12  border-[#015C8E] transition"
      >
        <span class="ml-2">{{"next" |translate }}</span>
        <img src="assets/images/arrownext.svg"  class="w-6"/>
      </button>
    </div>
  </form>

}

</div>
</div>
