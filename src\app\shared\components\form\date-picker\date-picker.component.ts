import {Component, Injectable, Input, OnInit} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {NgSelectModule} from "@ng-select/ng-select";
import {NG_VALUE_ACCESSOR, ReactiveFormsModule} from "@angular/forms";
import { FlatpickrModule } from './flatpickr.module';
import {InputHelperService} from "../../../../core/services/input-helper.service";
import { DateType } from 'ngx-hijri-gregorian-datepicker';
import { NgbCalendar, NgbCalendarIslamicUmalqura, NgbDate, NgbDateStruct, NgbDatepickerI18n, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { distinctUntilChanged } from 'rxjs';


const WEEKDAYS = ["ن", "ث", "ر", "خ", "ج", "س", "ح"];
const MONTHS = [
  "محرم",
  "صفر",
  "ربيع الأول",
  "ربيع الآخر",
  "جمادى الأولى",
  "جمادى الآخرة",
  "رجب",
  "شعبان",
  "رمضان",
  "شوال",
  "ذو القعدة",
  "ذو الحجة",
];

@Injectable()
export class IslamicI18n extends NgbDatepickerI18n {
  getMonthShortName(month: number) {
    return MONTHS[month - 1];
  }

  getMonthFullName(month: number) {
    return MONTHS[month - 1];
  }

  getWeekdayLabel(weekday: number) {
    return WEEKDAYS[weekday - 1];
  }

  getDayAriaLabel(date: NgbDateStruct): string {
    return `${date.day}-${date.month}-${date.year}`;
  }
}
@Component({
  selector: 'app-date-picker',
  standalone: true,
  imports: [
    FlatpickrModule,
    TranslateModule,
    NgSelectModule,
    ReactiveFormsModule,
    NgbDatepickerModule
  ],
  templateUrl: './date-picker.component.html',
  styleUrl: './date-picker.component.scss',
  providers: [
    { provide: NgbCalendar, useClass: NgbCalendarIslamicUmalqura },
    { provide: NgbDatepickerI18n, useClass: IslamicI18n },
    { provide: NG_VALUE_ACCESSOR, useExisting: DatePickerComponent, multi: true }
  ]
})
export class DatePickerComponent implements OnInit {
  @Input() label?: string;
  @Input() placeholder!: string;
  @Input() readonly?: boolean;
  required: boolean = true;
  @Input() control: any;
  name: string = 'text';
  @Input() type: any;
 // selectedDateType = DateType.Hijri;
 date!:  NgbDate;
 selectedDateType  =  DateType.Hijri;
  constructor(
    protected inputHelperService: InputHelperService,
  ) {
  }

  ngOnInit() {


    this.required = this.inputHelperService.isRequired(this.control);
    this.name = this.inputHelperService.getFormControlName(this.control) ?? this.label ?? this.placeholder ?? this.name;
    this.control?.valueChanges.pipe(
      distinctUntilChanged() // This ensures the value has actually changed before processing.
    ).subscribe((result?: string) => {
      // this.control?.setValue(this.datePipe.transform(result ?? Date.now(), 'yyyy-MM-dd'));
    })
  }

}
