<div class="box" appHasPermission="Ledger.JournalEntry.View">
  <div class="border-b border-dashed border-primary-500  pb-6">
<div class="header flex flex-col md:flex-row md:items-center md:justify-between gap-4">
  <!-- Title -->
  <h2 class="text-xl md:text-2xl">
    {{ 'bankAccountstatment' | translate }}
  </h2>

  <!-- Search Form -->
  <div class="search-container w-full md:w-auto border-prims my-4 md:my-6">
    <form class="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full">
      <app-date-picker
        class="flex-1"
        [control]="filtersForm.controls['startDate']"
        [placeholder]="'From' | translate">
      </app-date-picker>

      <app-date-picker
        class="flex-1"
        [control]="filtersForm.controls['endDate']"
        [placeholder]="'To' | translate">
      </app-date-picker>

      <app-button
        class="w-full sm:w-auto"
        [text]="'Search' | translate"
        (click)="loadData()"
        type="button">
      </app-button>
    </form>
  </div>
   <div>
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4">
    
    <!-- Left Section -->
    <div class="sm:col-span-2 md:col-span-3">
      <!-- Your content -->
    </div>

    <!-- Right Section -->
    <div class="z-50 col-span-1 md:col-span-2 flex flex-wrap items-center justify-start gap-2 text-nowrap top-0 sticky mx-4">
      <ng-container *ngFor="let col of columns">
        <div class="px-4 py-2 font-semibold text-center flex-1 md:flex-none"
             [style.flex]="col.field === 'title' || col.field === 'titleEn' ? 2 : 1"
             [ngClass]="{
               'border border-gray-200 bg-gray-50 dark:bg-inherit rounded-md': col.field === 'runningBalance'
             }">
          
          <ng-container *ngIf="col.field === 'runningBalance'; else emptyHeader">
            <span class="flex flex-wrap items-center gap-2">
              <span class="font-bold">{{ 'initialBalance' | translate }}:</span>
              <span>{{ (statmentList?.initialBalance | number:'1.2-2') || 0 }}</span>
            </span>
          </ng-container>

          <ng-template #emptyHeader>
            <!-- Empty cell for alignment -->
          </ng-template>
        </div>
      </ng-container>
    </div>
  </div>
</div>

</div>

   
 </div>
</div>






  <!-- Table -->

  <div class="my-6">
    @if(statmentList !=null){

  <ng-datatable
  [columns]="columns"
  [loading]="search.loading"
  [rows]="tableRows"
  [showPageSize]="true"
  [isServerMode]="true"
  [pageSize]="search.pageSize"
  [totalRows]="search.totalRows"
  [page]="search.page"
  [pageSizeOptions]="search.pageSizeOptions"
  (changeServer)="handlePageChange($event.current_page)"
  (pageSizeChange)="handlePageSizeChange($event)"
  class="whitespace-nowrap table-hover"
  firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
  lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
  nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
  previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
  [paginationInfo]="'pagination_info' | translate : { start: '{0}', end: '{1}', total: '{2}' }">
  <ng-template let-value="data" slot="entryDate">
        <span *ngIf="value.isFooter" class="font-bold"></span>
        <span *ngIf="!value.isFooter"> {{ formatDate(value.entryDate) |dateFormat }}</span>


      </ng-template>

      <ng-template let-value="data" slot="debit">
        <div class="flex gap-2 ">
          <span *ngIf="value.isFooter" class="font-bold">{{ 'totalDebit' | translate}}:</span>
          <span>{{ value.debit }}</span>
        </div>
      </ng-template>
      <ng-template let-value="data" slot="credit">
        <div class="flex gap-2 ">
          <span *ngIf="value.isFooter" class="font-bold">{{ 'totalCredit' | translate}}:</span>
          <span>{{ value.credit }}</span>
        </div>
      </ng-template>
      <ng-template let-value="data" slot="runningBalance">
        <div class="flex gap-2 ">
          <span *ngIf="value.isFooter" class="font-bold">{{ 'finalBalance' | translate}}:</span>
          <span>{{ value.runningBalance }}</span>
        </div>
      </ng-template>
      <ng-template let-value="data" slot="title">
  <div
    class="flex gap-2 items-center relative">
    <span *ngIf="value.isFooter" class="font-bold">
      {{ 'Total' | translate }}
    </span>

    <span
      *ngIf="!value.isFooter"
     class="w-[250px] max-w-[250px] text-wrap ltr:text-wrap">
      {{ value.title |translate }}
    </span>
  </div>
</ng-template>

      <!-- <ng-template let-value="data" slot="title">

        <div class="flex gap-2 ">
          <span *ngIf="value.isFooter" class="font-bold">{{ 'Total' | translate}}</span>
          <span *ngIf="!value.isFooter">{{ value.title }}</span>
        </div>
      </ng-template> -->
      <ng-template let-value="data" slot="titleEn">
        <div class="flex gap-2 ">
          <span *ngIf="value.isFooter" class="font-bold">{{ 'Total' | translate}}</span>
          <span *ngIf="!value.isFooter" class="w-[250px] max-w-[250px] text-wrap ltr:text-wrap">{{ value.titleEn }}</span>
        </div>
      </ng-template>
</ng-datatable>



  }

    <!-- Custom footer row -->
  <div *ngIf="statmentList==null" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <h3 class="mt-2 text-lg font-medium text-gray-900">{{ 'No transactions found' | translate }}</h3>
    <!-- <p class="mt-1 text-sm text-gray-500">{{ 'Try adjusting your search or filter criteria' | translate }}</p> -->
  </div>
  </div>
