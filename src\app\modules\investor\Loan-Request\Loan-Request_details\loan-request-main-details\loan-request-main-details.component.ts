import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../../core/services/toast.service';
import { LoanRequestService } from '../../Service/loan-request.service';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { DateFormatPipe } from '../../../../../shared/pipe/date-format.pipe';
import { LoanRequestMainInfoComponent } from '../loan-request-main-info/loan-request-main-info.component';
import { LoanRequestPaymentscheduleComponent } from '../loan-request-paymentschedule/loan-request-paymentschedule.component';
import { LoanRequestAttachmentsComponent } from '../loan-request-attachments/loan-request-attachments.component';
import { LoanRequestScoresComponent } from '../loan-request-scores/loan-request-scores.component';
import { LoanRequestCommentsComponent } from '../loan-request-comments/loan-request-comments.component';

@Component({
  selector: 'app-loan-request-main-details',
  standalone: true,
  imports: [
    DataTableModule,
    CalendarModule,
    FormsModule,
    CommonModule,
    TranslateModule,
    DateFormatPipe,
    LoanRequestMainInfoComponent,
    LoanRequestScoresComponent,

    LoanRequestAttachmentsComponent,
    LoanRequestCommentsComponent,
  ],
  providers: [DatePipe],
  templateUrl: './loan-request-main-details.component.html',
  styleUrl: './loan-request-main-details.component.css',
})
export class LoanRequestMainDetailsComponent {
  activeTab!: colDef;
  tabs!: Array<colDef>;
  WaletBalance: any[] = [];
  userId: any;
  id: any;
  loading = true;

  loanRequestData: any;

  constructor(
    public translate: TranslateService,
    private route: ActivatedRoute,
    protected loanRequestService: LoanRequestService,
    private router: Router
  ) {}

  async ngOnInit() {
    this.tabs = [
      {
        title: this.translate.instant('MainInfo'),
        field: 'companyInformation',
      },
      {
        title: this.translate.instant('Financingagreement'),
        field: 'Riskreport',
      },

      {
        title: this.translate.instant('attachments'),
        field: 'relatedAccounts',
      },
      {
        title: this.translate.instant('Comments'),
        field: 'Comments',
      },
    ];

    this.activeTab = this.tabs[0];
    this.loanRequestService.initPagination();
    this.route.params.subscribe((params) => {
      this.id = params['id'];
      this.loanRequestService.getLoanRequest(this.id).then((res: any) => {
        this.loading = false;
        this.loanRequestData = res?.data;
        this.userId = this.loanRequestData.userId;
      });
    });
  }

  goBack() {
    this.router.navigate(['/loanRequest']);
  }

  changeTab(selectedTab: colDef) {
    this.activeTab = selectedTab;
  }

  // async getWaletCountdata() {
  //   try {
  //     this.UsersService.getwalletBalance(this.userId).then(
  //       (res: any) => {

  //         if (Object.keys(res).length != 0) {
  //           if (res.list.length) {
  //             this.UsersService.pagination.loading = true;
  //             this.WaletBalance = res?.list;

  //             res?.list.forEach((element: any) => {
  //               this.WaletBalance = [];
  //               this.WaletBalance.push({
  //               walletname: element.walletname,
  //               totalRecords: element.totalRecords || 0,
  //               });
  //             });
  //           } else {
  //             this.UsersService.pagination.loading = false;
  //           }
  //         } else {
  //           this.UsersService.pagination.loading = false;
  //         }
  //       }
  //     );
  //   } catch (e: any) {
  //     this.toastr.error(e);
  //   }
  // }
  getLabel(key: string): string {
    return this.translate.instant(key);
  }
}
