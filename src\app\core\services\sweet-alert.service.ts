import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';
import { TranslateService } from "@ngx-translate/core";

@Injectable({
  providedIn: 'root'
})
export class SweetAlertService {
  constructor(
    public translate: TranslateService,
  ) {
  }

  async delete() {
    return Swal.fire({
      title: this.translate.instant('AreYouSure'),
      text: this.translate.instant('DeleteConfirmedBody'),
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: 'text-white btn m-2 bg-primary-500 border-primary-500 hover:text-white hover:bg-primary-600 hover:border-primary-600 focus:text-white focus:bg-primary-600 focus:border-primary-600 focus:ring focus:ring-primary-100 active:text-white active:bg-primary-600 active:border-primary-600 active:ring active:ring-primary-100 dark:ring-primary-400/20 ltr:mr-1 rtl:ml-1',
        cancelButton: 'text-white m-2 bg-danger-500 border-danger-500 btn hover:text-white hover:bg-danger-600 hover:border-danger-600 focus:text-white focus:bg-danger-600 focus:border-danger-600 focus:ring focus:ring-danger-100 active:text-white active:bg-danger-600 active:border-danger-600 active:ring active:ring-danger-100 dark:ring-primary-400/20',
      },
      confirmButtonText: this.translate.instant('YesDelete'),
      cancelButtonText: this.translate.instant('cancel'),
      buttonsStyling: false,
      showCloseButton: true
    });
  }
  async confirm(confirmTitle?: string, confirmText?: string) {
    return Swal.fire({
      title: this.translate.instant(confirmTitle ?? 'AreYouSure'),
      text: this.translate.instant(confirmText ?? 'confirmAction'),
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: 'text-white btn m-2 bg-primary-500 border-primary-500 hover:text-white hover:bg-primary-600 hover:border-primary-600 focus:text-white focus:bg-primary-600 focus:border-primary-600 focus:ring focus:ring-primary-100 active:text-white active:bg-primary-600 active:border-primary-600 active:ring active:ring-primary-100 dark:ring-primary-400/20 ltr:mr-1 rtl:ml-1',
        cancelButton: 'text-white m-2 bg-danger-500 border-danger-500 btn hover:text-white hover:bg-danger-600 hover:border-danger-600 focus:text-white focus:bg-danger-600 focus:border-danger-600 focus:ring focus:ring-danger-100 active:text-white active:bg-danger-600 active:border-danger-600 active:ring active:ring-danger-100 dark:ring-primary-400/20',
      },
      confirmButtonText: this.translate.instant('Yes'),
      cancelButtonText: this.translate.instant('cancel'),
      buttonsStyling: false,
      showCloseButton: true
    });
  }

  deleted() {
    Swal.fire({
      title: this.translate.instant('Deleted'),
      text: this.translate.instant('theDeletionWasCompletedSuccessfully'),
      icon: 'success',
      customClass: {
        confirmButton: 'text-white btn bg-primary-500 border-primary-500 hover:text-white hover:bg-primary-600 hover:border-primary-600 focus:text-white focus:bg-primary-600 focus:border-primary-600 focus:ring focus:ring-primary-100 active:text-white active:bg-primary-600 active:border-primary-600 active:ring active:ring-primary-100 dark:ring-primary-400/20',
      },
      confirmButtonText: this.translate.instant('Ok'),
      buttonsStyling: false
    })
  }
}
