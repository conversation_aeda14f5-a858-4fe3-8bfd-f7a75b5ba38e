@if (file) {
  <ng-modals [id]="file" [placement]="'modal-top'"
             className="fixed   transition-all duration-300 ease-in-out left-2/4 z-drawer -translate-x-2/4 translate-y-8">
    <div
      class="lg:w-[64rem]  ">
      <div class="bg-white shadow rounded-md dark:bg-zink-600   flex justify-between p-4 ">
        @if (!isPdf) {
<!--          <button (click)="download()"-->
<!--                  class="transition-all duration-200 ease-linear text-slate-500 hover:text-info-500 dark:text-zink-200 dark:hover:text-info-500">-->
<!--            <lucide-angular [class]="'size-5'" name="download"></lucide-angular>-->
<!--          </button>-->
        } @else {
          <div></div>
        }
<!--        <button (click)="handleCloseModal()"-->
<!--                class="transition-all duration-200 ease-linear text-slate-500 hover:text-red-500 dark:text-zink-200 dark:hover:text-red-500">-->
<!--          <lucide-angular [class]="'size-5'" name="x"></lucide-angular>-->
<!--        </button-->
<!--        >-->
      </div>
      <!--    <div class="max-h-[calc(theme('height.screen')_-_180px)] p-4 overflow-y-auto"> -->
      <div class="modal-content w-full">
        @if (isPdf) {
          <embed
            [src]="safeFileUrl"
            class="object-fit-contain embed-responsive embed-responsive-21by9 modal-content w-full h-full"
          />
        }
        @if (!isPdf) {
          <img [src]="safeFileUrl!.toString()" class="img-fluid object-fit-contain embed-responsive embed-responsive-21by9 modal-content w-full h-full" alt="" />
        }
      </div>
    </div>
  </ng-modals>
}
