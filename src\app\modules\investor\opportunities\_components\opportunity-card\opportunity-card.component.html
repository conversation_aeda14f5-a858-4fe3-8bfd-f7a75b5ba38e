<div class="cursor-pointer  bg-white dark:bg-primary-800 mx-3 rounded-lg shadow-lg mt-5 dark:shadow-primary-900">
  <div class="flex flex-col items-center md:items-start px-6 py-4">
    <div class="flex flex-col w-full">
      <div class="flex flex-row w-full pb-2 justify-between items-center">
        <div class="flex flex-col">
          <h2 class="text-xl dark:text-gray-100 mb-2">{{opportunity.title}}</h2>
          <h6 class="text-xs  text-gray-500 dark:text-gray-100">{{'opportunityNo'| translate}} {{': '}} <span
              class="text-gray-500 text-xs dark:text-gray-100">{{opportunity.opportunityNo}}</span></h6>
        </div>
        <span [ngClass]="opportunity.statusName"
          class="block w-24 rounded-[30px] border border-n30 bg-primary/10 py-2 text-center text-xs text-primary dark:border-n500 dark:bg-bg3 xxl:w-36">
          {{ opportunity.statusName | translate }}
        </span>
      </div>
      <div class="border border-primary mb-3 text"></div>
    </div>
    <div class="text-gray-500 dark:text-gray-200 text-center md:text-start">
      <ul>
        <li>{{'ProfitsPer' | translate}}: {{opportunity.profitsPer}}</li>

        <li>{{'period' | translate}}: {{opportunity.periodDays | duration}}</li>

        <li>{{'expiryDate' | translate}}: {{opportunity.expiryDate | date}}</li>

        <li>{{'ProfitsPer' | translate}}: {{opportunity.profitsPer}} </li>
      </ul>
    </div>
  </div>
  <div class="flex items-center justify-between bg-gray-50 px-6 py-3 rounded-lg dark:bg-primary-700">
    <h4 class="font-bold text-xl">{{'opportunityAmount' | translate}}: {{opportunity.amount | number}}</h4>
    <app-button [loading]="false" [text]="'invest' | translate" [routerLink]="opportunity.opportunityID" />
  </div>
</div>
