@media (max-width: 639px) {
    .min-h-screen-sm-down {
      min-height: 100vh;
    }
  }
  @keyframes wave {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(2deg);
    }
    50% {
        transform: translateY(0) rotate(0deg);
    }
    75% {
        transform: translateY(10px) rotate(-2deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

.wave-animation {
    animation: wave 3s infinite ease-in-out;
    transform-origin: center;
}
.hidden-horizental{
  overflow-x: hidden;
}
svg {
  position: absolute;
  /* top: 0;
  left: 0;
  width: 100%;
  height: 100%; */
}
.shap_top{
  bottom: -8rem;
}

.shap_bottom{
  left: -14rem;
  top: -7rem;
}
.curve {
  fill: url(#gradient);
}

.box {
  position: fixed;
  top: 0;
  transform: rotate(80deg);
  left: 0;
}

.wave {
  position: absolute;
  opacity: .4;
  width: 1500px;
  height: 1300px;
  margin-left: -150px;
  margin-top: -250px;
  border-radius: 43%;
}

