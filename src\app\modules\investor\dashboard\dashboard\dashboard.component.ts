import { CommonModule, DatePipe, NgClass } from '@angular/common'
import { ChangeDetectorRef, Component, OnInit } from '@angular/core'
import { TopBannerComponent } from '@component/shared/top-banner/top-banner.component'
import { NgApexchartsModule } from 'ng-apexcharts'
import { ChartOptions } from "../models/chart-options.model";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ModalService } from "@component/form/modals/modal.service";
import { ToastService } from '../../../../core/services/toast.service'
import { AcceptLegalDocumentComponent } from '../../../accept-legal-document/accept-legal-document.component'
import { acceptLegalDocument } from '../../../accept-legal-document/accept-legal-document.service';
import { DashboardService } from '../services/dashboard.service';
import { EncryptStorage } from 'encrypt-storage'
import { AuthService } from '../../../auth/services/auth.service'
import { StorageService } from '../services/collectionData.service'
import { colDef, DataTableModule } from '@bhplugin/ng-datatable'
import { InvestmentSearch, InvestmentModel } from '../../investments/models/investment.model'
import { InvestmentService } from '../../investments/services/investment.service'
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe'
import { CalendarModule } from 'primeng/calendar';
import { LoanRequestAttachmentsComponent } from '../../Loan-Request/Loan-Request_details/loan-request-attachments/loan-request-attachments.component';
import { LoanRequestCommentsComponent } from '../../Loan-Request/Loan-Request_details/loan-request-comments/loan-request-comments.component';
import { LoanRequestMainInfoComponent } from '../../Loan-Request/Loan-Request_details/loan-request-main-info/loan-request-main-info.component';
import { LoanRequestScoresComponent } from '../../Loan-Request/Loan-Request_details/loan-request-scores/loan-request-scores.component';
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    NgApexchartsModule,
    TopBannerComponent,
    NgClass,
    AcceptLegalDocumentComponent,
    DataTableModule,
    DateFormatPipe,
    TranslateModule,
  DataTableModule,
    CalendarModule,
    FormsModule,
    CommonModule,
    TranslateModule,
    DateFormatPipe,    
    ReactiveFormsModule
  ],
  providers: [
    DatePipe
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit {
incomeChartOptions: ChartOptions = {
  series: [],
  chart: {
    type: 'line',
    height: 350,
    toolbar: { show: false }
  },
  xaxis: { categories: [] }
};
  withdrawlChartOptions: ChartOptions= {
  series: [],
  chart: {
    type: 'line',
    height: 350,
    toolbar: { show: false }
  },
  xaxis: { categories: [] }
};
  depositsChartOptions: ChartOptions = {
  series: [],
  chart: {
    type: 'line',
    height: 350,
    toolbar: { show: false }
  },
  xaxis: { categories: [] }
};
  statchartOptions!: ChartOptions;
  currentUserObj: any
  columns: Array<colDef> = [];
  search: InvestmentSearch = { isServerMode: true } as InvestmentSearch;
  investments: InvestmentModel[] = [];
activeTab!: colDef;
  tabs!: Array<colDef>;
  currencies: string[] = ['$', '€', '£', '¥']
    dashboardwithdrawData: any
  statisticsData: any[] = [];
  monthlySummarychart:any
monthlyDepositchart:any
DepositData:any
monthlyWithdrawchart:any
WithdrawData:any
  // tableService
  activeIndex = 0
  responseData: any
  dashboardData: any
  profileData: any
  BalanceCount: any
  CollectioncountData: any
  Collectioncount: any
  #encryptTokenStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(
    public translate: TranslateService,
    private cdRef: ChangeDetectorRef,
    private DashboardService: DashboardService,
    protected modalService: ModalService,
    public authService: AuthService,
    private toast: ToastService,
    private acceptLegalDocumentService: acceptLegalDocument,
    private storageService: StorageService,
    protected investmentService: InvestmentService,
  ) {
    this.investmentService.initPagination();
    // this.tableService = new TableService()
    // this.tableService.initialize(transactionsData)

  }
id:any
  private _searchVal = ''

  get searchVal(): string {
    return this._searchVal
  }

  set searchVal(value: string) {
    this._searchVal = value
    // this.tableService.search(value)
    this.cdRef.detectChanges()
  }

  getLocale(number: number) {
    return number.toLocaleString()
  }

 

  async ngOnInit() {
    this.statchartOptions = {
      chart: {
        height: 60,
        width: '100%',
        type: 'area',
        sparkline: {
          enabled: true
        },
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      grid: {
        show: false
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        width: 2,
        curve: 'smooth'
      },
      series: [
        {
          name: 'Series 1',
          data: [24, 26, 32, 36, 37, 44, 50, 49, 44, 40, 32, 28, 32, 34, 28, 23, 22, 28, 34, 35]
        }
      ],
      tooltip: {
        enabled: false
      },
      colors: ['#20B757'],
      fill: {
        colors: ['#20B757'],
        opacity: 1,
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'vertical',
          shadeIntensity: 0.3,
          gradientToColors: undefined,
          inverseColors: false,
          opacityFrom: 0.4,
          opacityTo: 0,
          colorStops: []
        }
      },
      xaxis: {
        tooltip: {
          enabled: false
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          show: false
        }
      },
      yaxis: {
        min: 0,
        max: 50,
        tooltip: {
          enabled: false
          // followCursor: true
        },
        labels: {
          show: false
        }
      }
    }

    this.columns = [
      { title: this.translate.instant('opportunityId'), field: 'opportunityTitle', },
      { title: this.translate.instant('walletName'), field: 'fullName', },
      { title: this.translate.instant('investmentDate'), field: 'investDate', },
      { title: this.translate.instant('amount'), field: 'amount', },
      { title: this.translate.instant('profits'), field: 'profits', },
      { title: this.translate.instant('profitDueDate'), field: 'dueDate', },
      { title: this.translate.instant('actualMaturityDate'), field: 'actualDueDate', },
      { title: this.translate.instant('status'), field: 'statusName', },
    ]
       this.tabs = [
   
      {
        title:this.translate.instant("Investments"),
        field:"Investments"
      },
      {
        title:this.translate.instant("DepositsList"),
        field:"DepositsList"
      },
    
      {
        title:this.translate.instant("WithdrowsList"),
        field:"WithdrowsList"
      },
  
    ];

    this.activeTab= this.tabs[0]
    await this.getList();
    this.openModal()
    this.getProfileFromStorageOnly();
    this.getCollectioncount();
    await this.getdashboard()
    await this.getDepositDetails()
    await this.getWithdrawlDetails()

    // Initialize statisticsData after dashboardData is set
    this.statisticsData = [
      {
        title: this.translate.instant('InvestmentsList'),
        amount: this.dashboardData?.data?.totalAmount,
        avarage: this.translate.instant('AvarageInvestmentsList'),
        growth: this.dashboardData?.data?.averageInvestmentPer + "%"
      },
      {
        title: this.translate.instant('profits'),
        amount: this.dashboardData?.data?.totalProfits,
        avarage: this.translate.instant('AvarageProfits'),
        growth: this.dashboardData?.data?.averageProfitsPer + "%"
      },
      {
        title: this.translate.instant('available Opprtunity'),
        amount: this.dashboardData?.data?.approvedAmount,

      }
    ];
  }

  async openModal() {
    this.responseData = await this.acceptLegalDocumentService.getLegalDocuments()
    if (this.responseData.data.length != 0)
      try {
        const result = await this.modalService.openWithResult('acceptLegalDocument');
      } catch {
        console.log('Modal closed without saving');
      }
  }

  changeTab(selectedTab : colDef)
  {
    this.activeTab= selectedTab;
  }



  async getCollectioncount() {
    try {
      this.Collectioncount = await this.authService.getCollectioncount();
      this.CollectioncountData = this.Collectioncount.data;

console.log( this.CollectioncountData,' this.CollectioncountData');

      // Store encrypted data
      this.storageService.setCollectionData(this.CollectioncountData);

    } catch (error) {
      console.log('Error getting collection count:', error);
    }
  }


  // To remove the stored data
  clearStoredCollectionData() {
    this.#encryptTokenStorage.removeItem('CollectioncountData');
  }

  async getList() {
    try {
      this.investments = await this.investmentService.getList() ?? []
    } catch (e: any) {
      console.log(e);
    }
  }

  async handleSearch(e: any) {
    this.search = { find: (e.target as HTMLInputElement).value, isServerMode: false } as InvestmentSearch;
    this.investments = [];
    this.investmentService.initPagination();
    await this.getList();
    this.search.isServerMode = true;
  }
  async getStoredBalance() {
    try {
      const storedBalance = await this.#encryptTokenStorage.getItem('userBalance');
      if (storedBalance) {
        this.BalanceCount = JSON.parse(storedBalance);


        this.authService.userBalance.set(this.BalanceCount);
      }
    } catch (error) {
      console.log('Error retrieving stored balance:', error);
    }
  }

  getProfileFromStorageOnly() {
    const profile = this.authService.getCachedProfile();
    if (profile) {
      this.profileData = profile;


      this.authService.userBalance.set(profile?.balance || '');
    } else {
      console.warn('No profile in localStorage, fallback to login or fetch manually if needed');
    }
  }

  async getdashboard() {
    try {
      this.dashboardData = await this.DashboardService.getDashboardDetails({});
      this.monthlySummarychart=this.dashboardData?.data?.monthlySummary
      this.updateChartOptions();
    } catch (error) {
      console.error('Error getting bank account:', error);
    }
  }
async getWithdrawlDetails() {
  try {
    this.WithdrawData = await this.DashboardService.getWithdrawstatistics({});
    this.monthlyWithdrawchart = this.WithdrawData?.data?.monthlySummary || [];
    this.updateWithdrawlChartOptions();
  } catch (error) {
    console.error('Error getting withdrawal statistics:', error);
  }
}
async getDepositDetails() {
  try {
    this.DepositData = await this.DashboardService.getDepositstatistics({});
    this.monthlyDepositchart = this.DepositData?.data?.monthlySummary;
    this.updateDepositChartOptions();
  } catch (error) {
    console.error('Error getting deposit statistics:', error);
  }
}


  updateChartOptions() {
  // Extract data from monthlySummary
  const categories = this.monthlySummarychart.map((item:any) => item?.monthName);
  const amounts = this.monthlySummarychart.map((item:any) => item?.amount);
  const counts = this.monthlySummarychart.map((item :any) => item?.count);

  // Find the maximum value for the y-axis
  const maxAmount = Math.max(...amounts, 100); // Use at least 100 as minimum max

  this.incomeChartOptions = {
    series: [
      {
        name: 'Amount',
        type: 'line',
        data: amounts
      },
      {
        name: 'Count',
        type: 'line',
        data: counts
      }
    ],
    chart: {
      height: 300,
      type: 'line',
      toolbar: {
        show: false
      }
    },
    legend: {
      show: true,
      position: 'top'
    },
    colors: ['#63CC8A', '#FFC861'],
    stroke: {
      width: [3, 3],
      curve: 'smooth',
      lineCap: 'round',
      dashArray: [0, 5]
    },
    xaxis: {
      type: 'category',
      categories: categories,
      tickAmount: 12,
      axisTicks: {
        show: false
      },
      axisBorder: {
        show: false
      },
      labels: {
        style: {
          fontSize: '10px'
        }
      }
    },
    yaxis: {
      min: 0,
      max: maxAmount * 1.2, // Add 20% padding
      tickAmount: 5,
      labels: {
        offsetX: -17,
        formatter: (value) => {
          return value.toFixed(0); // Format without decimals
        }
      }
    },
    fill: {
      opacity: 1
    },
    grid: {
      padding: {
        left: -10,
        bottom: -10
      },
      show: true,
      xaxis: {
        lines: {
          show: true
        }
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (value) => {
          return value ? value.toFixed(2) : '0';
        }
      }
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300
          }
        }
      },
      {
        breakpoint: 570,
        options: {
          chart: {
            height: 240
          },
          legend: {
            position: 'bottom'
          }
        }
      }
    ]
  };
}

updateDepositChartOptions() {
  if (!this.monthlyDepositchart || this.monthlyDepositchart.length === 0) return;

  const categories = this.monthlyDepositchart.map((item:any) => item.monthName);
  const amounts = this.monthlyDepositchart.map((item:any) => Math.abs(item.amount));
  const counts = this.monthlyDepositchart.map((item:any) => item.count);

  // Calculate max values with padding
  const maxAmount = Math.max(...amounts, 10) * 1.2; // Ensure minimum of 10
  const maxCount = Math.max(...counts, 10) * 1.2;   // Ensure minimum of 10

  this.depositsChartOptions = {
    series: [
      {
        name: 'Deposit Amount',
        type: 'line',  // Changed from 'column' to 'line'
        data: amounts
      },
      {
        name: 'Transaction Count',
        type: 'line',
        data: counts
      }
    ],
   chart: {
      height: 300,
      type: 'line',
      toolbar: {
        show: false
      }
    },
    legend: {
      show: true,
      position: 'top'
    },
    colors: ['#6366F1', '#10B981'], // Purple for amount, green for count
  stroke: {
      width: [3, 3],
      curve: 'smooth',
      lineCap: 'round',
      dashArray: [0, 5]
    },
    markers: {
      size: [5, 5],    // Equal marker size for both lines
      hover: {
        size: 7
      }
    },
 xaxis: {
      type: 'category',
      categories: categories,
      tickAmount: 12,
      axisTicks: {
        show: false
      },
      axisBorder: {
        show: false
      },
      labels: {
        style: {
          fontSize: '10px'
        }
      }
    },
    yaxis: [
      {
        title: { 
          text: 'Amount',
          style: {
            color: '#6366F1'
          }
        },
        min: 0,
        max: maxAmount,
        labels: {

          style: {
            colors: ['#6366F1']
          },
          offsetX: -47,
          formatter: (val) => val.toLocaleString()
        }
      },
      {
        opposite: true,
        title: { 
          text: 'Count',
          style: {
            color: '#10B981'
          }
        },
        min: 0,
        max: maxCount,
        labels: {
          style: {
            colors: ['#10B981']
          }
        }
      }
    ],
   fill: {
      opacity: 1
    },
    grid: {
      padding: {
        left: -10,
        bottom: -10
      },
      show: true,
      xaxis: {
        lines: {
          show: true
        }
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (val) => val ? val.toLocaleString() : '0'
      }
    },
   
      responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 300
          }
        }
      },
      {
        breakpoint: 570,
        options: {
          chart: {
            height: 240
          },
          legend: {
            position: 'bottom'
          }
        }
      }
    ]
  };
}




// Create new method for updating withdrawal chart
updateWithdrawlChartOptions() {
  if (!this.monthlyWithdrawchart || this.monthlyWithdrawchart.length === 0) return;

  const categories = this.monthlyWithdrawchart.map((item: any) => item.monthName);
  const amounts = this.monthlyWithdrawchart.map((item: any) => Math.abs(item.amount));
  const counts = this.monthlyWithdrawchart.map((item: any) => item.count);

  // Calculate max values with padding
  const maxAmount = Math.max(...amounts, 10) * 1.2; // Ensure minimum of 10
  const maxCount = Math.max(...counts, 10) * 1.2;   // Ensure minimum of 10

  this.withdrawlChartOptions = {
    series: [
      {
        name: 'Withdrawal Amount',
        type: 'line',
        data: amounts
      },
      {
        name: 'Withdrawal Count',
        type: 'line',
        data: counts
      }
    ],
    chart: {
      type: 'line',
      height: 350,
      toolbar: { show: false },
      animations: { enabled: true }
    },
    colors: ['#EF4444', '#F59E0B'], // Red for amount, amber for count
    stroke: {
      width: [3, 3],
      curve: 'smooth',
      dashArray: [0, 0]
    },
    markers: {
      size: [5, 5],
      hover: {
        size: 7
      }
    },
    xaxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: '10px',
          fontFamily: 'inherit'
        }
      }
    },
    yaxis: [
      {
        title: { 
          text: 'Amount',
          style: {
            color: '#EF4444'
          }
        },
        min: 0,
        max: maxAmount,
        labels: {
          style: {
            colors: ['#EF4444']
          },
          formatter: (val) => val.toLocaleString()
        }
      },
      {
        opposite: true,
        title: { 
          text: 'Count',
          style: {
            color: '#F59E0B'
          }
        },
        min: 0,
        max: maxCount,
        labels: {
          style: {
            colors: ['#F59E0B']
          }
        }
      }
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (val) => val ? val.toLocaleString() : '0'
      }
    },
    legend: {
      position: 'top'
    },
    dataLabels: {
      enabled: false
    }
  };
}

  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }
}





