import { IQuery } from "../../../../core/models/pagination.model";


export interface Opportunity {
  opportunityID: string;
  companyID: string;
  creditLimitID: string;
  companyName: string;
  orderID: string;
  orderNo: number;
  opportunityNo: number;
  title: string;
  summary: string;
  details: string;
  expiryDate: string; // Assuming it's always a valid ISO 8601 date string
  amount: number;
  minAmount: number;
  investableAmount: number;
  investmentAmount: number;
  investmentPer: number;
  calculatedProfits: number;
  calculatedProfitsPer: number;
  isCustomProfits: boolean;
  customProfits: number;
  customProfitsPer: number;
  profits: number;
  profitsPer: number;
  periodDays: number;
  isMultiShare: boolean;
  status: number;
  statusName: string;
  isDeleted: boolean;
  createdOn: string; // Assuming it's always a valid ISO 8601 date string
  lastUpdateBy: string;
  lastUpdateByName: string;
  lastUpdateOn: string; // Assuming it's always a valid ISO 8601 date string
  actionName: string;
  createWithdrawRequests: boolean;
  closeInvesments: boolean;
  isSyncdWithMDD: boolean;
}
export interface OpportunitySearch extends IQuery {

}

export enum OpportunityStatus
   {
       Published = 1,
       Open = 2,
       Closed = 3,
       Expired = 4,
       Cancelled = 6,
   }