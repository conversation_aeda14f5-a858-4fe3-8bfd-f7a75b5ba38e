<div class="modal-overlay">
  <div class="modal-container">
  
    <div class="modal-header box mb-4 xxxl:mb-6">
      <h4></h4>
      <button (click)="closeModal()" class="close-button">×</button>
    </div>
    <div class="w-full border-t border-dashed border-gray-400 mb-4"></div>

    <form [formGroup]="form" (ngSubmit)="submitForm()"  dir="rtl" class="modal-body">
      <div class="flex justify-center items-center">
        <div class="w-full max-w-md space-y-4">
          
          <div *ngFor="let field of fields " class="mb-4">
            <label class="block mb-2 text-right">{{ field.label | translate }}</label>
            <app-select
              *ngIf="field.type === 'select'"
              [control]="form.get(field.key)"
              [items]="field.options || []"
              [multiple]="false"
              [bindLabel]="'label'"
              [bindValue]="'value'"
              class="form-input rounded-full w-full bg-white border-gray-300 text-black custom-select"
            ></app-select>
            <div *ngIf="field.type === 'amount-range'" class="flex items-center space-x-4">
              <div class="flex items-center space-x-2 w-1/2">
                <label class="w-16">{{ 'From' | translate }}</label>
                <input
                  [formControlName]="'amountFrom'"
                  type="number"
                  class="form-input rounded-full border-gray-300 w-full"
                />
              </div>
              <div class="flex items-center space-x-2 w-1/2">
                <label class="w-16">{{ 'To' | translate }}</label>
                <input
                  [formControlName]="'amountTo'"
                  type="number"
                  class="form-input rounded-full border-gray-300 w-full"
                />
              </div>
            </div>
            <div *ngIf="field.type === 'date-range'" class="flex items-center space-x-4">
              <div class="flex items-center space-x-2 w-1/2">
                <label class="w-16">{{ 'From' | translate }}</label>
                <div class="relative w-full">
                  <input
                    [formControlName]="'fromDate'"
                    type="date"
                    class="form-input rounded-full border-gray-300 pr-10 w-full"
                  />
                  <svg xmlns="http://www.w3.org/2000/svg" class="absolute right-2 top-2 w-5 h-5 text-gray-500 cursor-pointer" fill="none" viewBox="0 0 24 24" stroke="currentColor" (click)="openDatepicker()">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3M16 7V3M3 8h18M4 11v6a1 1 0 001 1h14a1 1 0 001-1v-6m-16 0h16"></path>
                  </svg>
                </div>
              </div>
              <div class="flex items-center space-x-2 w-1/2">
                <label class="w-16">{{ 'To' | translate }}</label>
                <div class="relative w-full">
                  <input
                    [formControlName]="'toDate'"
                    type="date"
                    class="form-input rounded-full border-gray-300 pr-10 w-full"
                  />
                  <svg xmlns="http://www.w3.org/2000/svg" class="absolute right-2 top-2 w-5 h-5 text-gray-500 cursor-pointer" fill="none" viewBox="0 0 24 24" stroke="currentColor" (click)="openDatepicker()">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3M16 7V3M3 8h18M4 11v6a1 1 0 001 1h14a1 1 0 001-1v-6m-16 0h16"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div *ngIf="field.type === 'text'">
              <input
                [formControlName]="field.key"
                type="text"
                class="form-input rounded-full border-gray-300 w-full"
              />
            </div>
          </div>
            <div class="col-span-1 flex justify-end w-full gap-2 md:gap-6 mt-4">
            <button 
              type="submit" 
              class="btn btn-primary text-white !rounded-full px-6 py-2"
              [class.hidden]="isFiltered">
              {{ "Search" | translate }}
            </button>
              <button type="button" (click)="closeModal()">{{"Cancel" |translate}}</button>
            <button 
              type="button" 
              class="btn btn-secondary !rounded-full px-6 py-2"
              (click)="resetForm()"
              [class.hidden]="!isFiltered">
              {{ "reset" | translate }}
            </button>
          </div>

        </div>
      </div>
    </form>
  </div>
</div>
