import { ChangeDetector<PERSON>ef, Component, inject, <PERSON><PERSON><PERSON>roy, OnInit, output, signal } from '@angular/core';
import { FormGroup, FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BorrowerService } from '@component/Borrower/Borrower.service';
import { ICompanyInfo } from '@component/layouts/companies/CompayList';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../core/services/toast.service';
import { AuthService } from '../../../auth/services/auth.service';
import { CommonModule, JsonPipe, NgClass, NgStyle } from '@angular/common';
import { FileUploadComponent } from '@component/form/file-upload/file-upload.component';

import { InputComponent } from '@component/form/input/input.component';
import { EncryptStorage } from 'encrypt-storage';
import moment from 'moment';
import { Router } from '@angular/router';
import { IlistDetails } from '../enums/LoanRequestType.enum';
import { ModalService } from '@component/form/modals/modal.service';
import { AddLoanrequestPopupComponent } from '../Add-loanrequest-popup/Add-loanrequest-popup.component';
import { LoanRequestService } from '../Service/loan-request.service';
import { NpxDropdownComponent } from '@component/shared/npx_dropdown/npx_dropdown.component';
import { ImageConversionService } from '../../../../shared/otp/services/ImageConversion.service';


@Component({
  selector: 'app-main-info-loanrequest',
  standalone: true,
  imports: [TranslateModule,ReactiveFormsModule,FileUploadComponent,FormsModule,CommonModule,
    InputComponent,NpxDropdownComponent,NgClass,AddLoanrequestPopupComponent],
  templateUrl: './main-info-loanrequest.component.html',
  styleUrl: './main-info-loanrequest.component.css'
})
export class MainInfoLoanrequestComponent implements OnInit,OnDestroy {
 #ImageConversionService:ImageConversionService = inject(ImageConversionService)
  #loanRequestService: LoanRequestService = inject(LoanRequestService)
  #changeDetectorRef: ChangeDetectorRef = inject(ChangeDetectorRef);
  encryptStorage = new  EncryptStorage('App_LoanRequest');
 #authService:AuthService =inject(AuthService)
 #router:Router=inject(Router)
  #toast: ToastService = inject(ToastService)
  listOfOwnedCompanies=signal<ICompanyInfo[]>([])
  #borrowerService:BorrowerService = inject(BorrowerService)
  translate:TranslateService=inject(TranslateService)

  protected modalService: ModalService = inject(ModalService)
  loanrequestForm!:FormGroup
  reqId!:string
  selectedCompany=signal<number>(JSON.parse(localStorage.getItem('selectedCompany')??'0'))
  firstStep=signal<boolean>(JSON.parse(this.encryptStorage.getItem('stepSelected')??'false'))
  closeStatuts=output<boolean>()
  addAnotherCompanyChange = output<boolean>()
  goBack =output<boolean>()
  currentStep = 1;
  CommercialRegisteraddress:any
  CommercialRegister:any
  listOfSelectedItems:IlistDetails[]=[]

  loanRequestCreateCommand: { Details: IlistDetails[] } = { Details: [] };
  isConsentChecked = signal<boolean>(false)
  itemDetails:any

  incestExperienceList =  [



     {key: '45day', value: 45},
     {key: '60day', value: 60},
     {key: '90day', value: 90},
     {key: '120day', value: 120},


    ]

   
  ngOnInit(): void {
      // this.getListOfOwnedCompanies()
    this.initalForm()

    this.itemDetails = this.encryptStorage.getItem('LoanRequetDraft')
    if(this.itemDetails){
      this.listOfSelectedItems = this.itemDetails?.details.map((element:any) => {
        element.type = element.type == 0 ?(this.translate.instant('Governmental'))  : (this.translate.instant('Private'))
        return element
      });

      // Patch values from itemDetails if available (handles edit mode)
      this.loanrequestForm.patchValue({
        PeriodInDays: this.itemDetails.periodInDays,
        RequestedAmount: this.itemDetails.requestedAmount
        // File controls are not patched here; their validation relies on checkItAttchmentType
      });

      // Update validity manually after patching, especially for file validators
      this.loanrequestForm.updateValueAndValidity();

      this.isConsentChecked.set(true)
      // Update file form controls if draft has attachments
      if (this.itemDetails.attachments) {
          const delegationLetter = this.checkItAttchmentType(1);
          if (delegationLetter) {
              // We set a dummy object or value here to make the form control valid
              // The actual file sending logic in onSubmit will use itemDetails.attachments
              this.loanrequestForm.get('DelegationLetter')?.patchValue(delegationLetter);
          }
          const financialStatements = this.checkItAttchmentType(2);
          if (financialStatements) {
              this.loanrequestForm.get('FinancialStatements')?.patchValue(financialStatements);
          }
          const cashflow = this.checkItAttchmentType(3);
          if (cashflow) {
              this.loanrequestForm.get('Cashflow')?.patchValue(cashflow);
          }
           // Re-validate the form after setting values
           this.loanrequestForm.updateValueAndValidity();
      }
    }
    this.listOfTemp=[]


  }

   initalForm(){
    const draft = this.encryptStorage.getItem('LoanRequetDraft')
    this.loanrequestForm = new FormGroup({
      PeriodInDays: new FormControl(this.itemDetails?.periodInDays || (draft && draft.periodInDays ? draft.periodInDays:null),Validators.required),
      RequestedAmount: new FormControl(this.itemDetails?.requestedAmount || (draft &&draft.requestedAmount?draft.requestedAmount:''),Validators.required),
      type: new FormControl(null,Validators.required),
      Other: new FormControl(null),
      //draft && draft.attachments[2]?.filePath? draft.attachments[2]?.filePath:
      FinancialStatements: new FormControl(null,Validators.required),
      //draft && draft.attachments[1]?.filePath?draft.attachments[1]?.filePath:
      Cashflow: new FormControl(null,Validators.required),
      //draft && draft.attachments[0]?.filePath? draft.attachments[0]?.filePath:
       DelegationLetter: new FormControl(null,Validators.required),
      //draft && draft.attachments[2]?.filePath? draft.attachments[2]?.filePath:
      // FinancialStatements: new FormControl(null,Validators.required),
      // //draft && draft.attachments[1]?.filePath?draft.attachments[1]?.filePath:
      // Cashflow: new FormControl(null,Validators.required),
      // //draft && draft.attachments[0]?.filePath? draft.attachments[0]?.filePath:
      // DelegationLetter: new FormControl(null,Validators.required),
      clientCRNumber: new FormControl(null,Validators.required),
      invoiceVAT: new FormControl(null,Validators.required),
      amount: new FormControl(draft &&draft.requestedAmount?draft.requestedAmount:null),

    })
   }


    selectCompany(index:number,cr:number){

    this.selectedCompany.set(index)
   }
   async nextStep(){
    if (this.currentStep === 1 && this.loanrequestForm.valid) {
      this.currentStep++;
    }else{
      this.#toast.error(this.translate.instant('Pleasereplaceattachments'))
    }
    // try {

    //   this.CommercialRegister= await this.#authService.companySubmitRegister(this.listOfOwnedCompanies()[this.selectedCompany()].crNumber)
    //   this.CommercialRegisteraddress=this.CommercialRegister.data
    //   localStorage.setItem('selectedCompany',this.selectedCompany().toString())
    //   localStorage.setItem('firstStep',this.firstStep().toString())
    //   this.firstStep.update(v=>!v)

    // } catch (error:any) {
    //   this.#toast.error(error.message)
    // }

   }

// Determine file type
getFileType(filePath: string): string {
  if (!filePath) return 'unknown';

  const extension = filePath.split('.').pop()?.toLowerCase() || '';

  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  } else if (extension === 'pdf') {
    return 'pdf';
  } else if (['doc', 'docx', 'txt', 'odt'].includes(extension)) {
    return 'document';
  } else if (['xls', 'xlsx', 'csv'].includes(extension)) {
    return 'spreadsheet';
  } else {
    return 'file';
  }
}

// Extract filename
getFileName(filePath: string): string {
  return filePath?.split(/[\\/]/).pop() || 'Unknown file';
}

// Format file size
getFileSize(bytes: number): string {
  if (!bytes) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Preview file
previewFile(filePath: string) {
  // Implement your preview logic here
  // This could open a new tab, show a modal, etc.
  window.open(filePath, '_blank');
}
   handleSelectType(value:any){
    this.loanrequestForm.get('type')?.setValue(value)
   }
  isUUID(id: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

private handleInvalidForm() {
  if (!this.isConsentChecked()) {
    this.#toast.error(this.translate.instant('Please accept the consent'));
  } else if (this.listOfSelectedItems.length === 0) {
    this.#toast.error('Please add at least one item');
  } else {
    this.#toast.error('Please fill all the required fields');
  }
}


private async prepareCreateFormData(typeOfRequest: number): Promise<FormData> {
  const data = new FormData();
  data.append('loanRequestCreateCommand.RequestType', this.encryptStorage.getItem('LoanRequestType')?.toString());
  data.append('loanRequestCreateCommand.RequestStatus', String(typeOfRequest));
  data.append('loanRequestCreateCommand.PeriodInDays', this.loanrequestForm.get('PeriodInDays')?.value);
  data.append('loanRequestCreateCommand.RequestedAmount', this.loanrequestForm.get('RequestedAmount')?.value);

  this.appendAttachments(data, false);

  this.appendDetails(data, false);

  return data;
}
private async prepareEditFormData(typeOfRequest: number): Promise<FormData> {
  const data = new FormData();
  data.append('Id', this.itemDetails.id);
  data.append('RequestType', this.encryptStorage.getItem('LoanRequestType')?.toString());
  data.append('RequestStatus', String(typeOfRequest));
  data.append('PeriodInDays', this.loanrequestForm.get('PeriodInDays')?.value);
  data.append('RequestedAmount', this.itemDetails.requestedAmount.toString());
  data.append('Description', this.itemDetails.description);

  await this.appendAttachments(data, true);
  this.appendDetails(data, true);

  return data;
}
private async appendAttachments(data: FormData, isEdit = false): Promise<void> {
  const files = [
    { field: 'DelegationLetter', typeId: 1, index: 0 },
    { field: 'Cashflow', typeId: 2, index: 1 },
    { field: 'FinancialStatements', typeId: 3, index: 2 }
  ];
  for (let { field, typeId, index } of files) {
    const fileControl = this.loanrequestForm.get(field)?.value;
    const attachmentId = this.checkItAttchmentType(typeId)?.id;
    const filePath = this.checkItAttchmentType(typeId)?.filePath;

    if (fileControl instanceof File) {
      data.append(`Attachments[${index}].file`, fileControl);
      if (isEdit && attachmentId) {
        data.append(`Attachments[${index}].Id`, attachmentId);
      }
      data.append(`Attachments[${index}].FileType`, typeId.toString());

    } else if (isEdit && attachmentId && filePath) {
      data.append(`Attachments[${index}].Id`, attachmentId);
      data.append(`Attachments[${index}].FileType`, typeId.toString());

    } else {
    }
    // If no file (new or existing) and not in edit mode, nothing is appended for this attachment type.
    // If no file (new or existing) and in edit mode, and no attachmentId, nothing is appended.

  }
}
private appendDetails(data: FormData, isEdit = false): void {
  this.listOfSelectedItems.forEach((detail: any, index: number) => {
    const baseKey = isEdit ? `Details[${index}]` : `loanRequestCreateCommand.Details[${index}]`;

    if (isEdit && detail.id && this.isUUID(detail.id)) {
      data.append(`${baseKey}.Id`, detail.id);
    }

    data.append(`${baseKey}.clientCRNumber`, detail.clientCRNumber != null ? detail.clientCRNumber : '');
    if (isEdit) {
      data.append(`${baseKey}.clientName`, detail.clientName != null ? detail.clientName : '');
      data.append(`${baseKey}.invoicePONumber`, detail.invoicePONumber != null ? detail.invoicePONumber : '');
    }
    data.append(`${baseKey}.Type`, detail.type?.toString() === 'Governmental' ? '0' : '1');
    data.append(`${baseKey}.Amount`, detail.amount != null ? String(detail.amount) : '0');
    data.append(`${baseKey}.InvoiceVAT`, detail.invoiceVAT != null ? detail.invoiceVAT : '');

    if (detail.file instanceof File) {
      data.append(`${baseKey}.File`, detail.file);
    } else if (isEdit && detail.filePath) {
      data.append(`${baseKey}.FilePath`, detail.filePath);
    }
  });
}
// private handlePostSubmit(typeOfRequest: number): void {
//   if (typeOfRequest === 1) {
//     this.modalService.open('loan-request-popup');
//   } else {
//     this.#router.navigate(['/loanRequest']);
//     this.modalService.close('otp-modal');
//   }
// }
public handlePostSubmit(typeOfRequest: number): void {
  if (typeOfRequest === 1) {
    this.modalService.open('loan-request-popup'); 
  } else {
    this.#router.navigate(['/loanRequest']);
    this.modalService.close('otp-modal');
  }
}


   getDaysDifference(){
      const today = moment(); // current date
      const targetDate = moment(this.loanrequestForm.get('PeriodInDays')?.value, 'YYYY-MM-DD');

      const diff = targetDate.diff(today, 'days'); // positive if future, negative if past

      return diff;

   }

   getDayFromDifference(diff:number){
    const today = moment(); // Current date and time
    const targetDate = moment(today).add(diff, 'days'); // Add days to current date
    return targetDate.format('YYYY-MM-DD');
   }

   addAnotherCompany(){
    this.addAnotherCompanyChange.emit(true)
   }
   prevStep() {
    this.currentStep--;
  }

  goBackStep() {
    // Your existing back functionality
  }


   previousStep(){

    this.firstStep.update(v=>!v)
   }
   goToNextStep(){
     if(!this.firstStep() &&
    ((this.loanrequestForm.get('Cashflow')?.value !== null || this.checkItAttchmentType(2)) &&
     (this.loanrequestForm.get('FinancialStatements')?.value !== null || this.checkItAttchmentType(3)) &&
     (this.loanrequestForm.get('DelegationLetter')?.value !== null || this.checkItAttchmentType(1))))
    {   this.encryptStorage.setItem('stepSelectedForm',this.loanrequestForm.getRawValue())
      this.firstStep.update(v=>!v)
      this.encryptStorage.setItem('stepSelected',this.firstStep().toString())
    }else{
      this.#toast.error('Please fill all the required fields')
    }
   }
   goToBackStep(){
    if(this.firstStep()){

      this.firstStep.update(v=>!v)
      this.encryptStorage.setItem('stepSelected',this.firstStep().toString())
      // this.loanrequestForm.patchValue({...this.encryptStorage.getItem('stepSelectedForm')})
    }else{
      this.#router.navigate(['/loanRequest']);
    }
   }



   reslut:any
   listOfTemp:any[]=[]

addListOfItems() {
  if (this.loanrequestForm.invalid) {
    // Mark all controls as touched to trigger validation UI
    Object.keys(this.loanrequestForm.controls).forEach(key => {
      const control = this.loanrequestForm.get(key);
      control?.markAsTouched();
      control?.updateValueAndValidity();
    });

    // Show toastr for the first invalid field
    const firstInvalidKey = Object.keys(this.loanrequestForm.controls).find(
      key => this.loanrequestForm.get(key)?.invalid
    );

    if (firstInvalidKey) {
      this.#toast.error(`${firstInvalidKey} is required`);
    } else {
      this.#toast.error("Please fill all required fields");
    }

   
  } else {
    // ✅ If valid → continue your save logic
    let current: IlistDetails[] = [{
      id: this.listOfSelectedItems.length + 1,
      amount: this.loanrequestForm.get('amount')?.value,
      invoiceVAT: this.loanrequestForm.get('invoiceVAT')?.value,
      clientCRNumber: this.loanrequestForm.get('clientCRNumber')?.value,
      type: this.translate.instant(this.loanrequestForm.get('type')?.value),
      file: this.loanrequestForm.get('Other')?.value,
      typeOfLoan: this.loanrequestForm.get('typeOfLoan')?.value
    }];

    if (this.listOfSelectedItems.length > 0) {
      this.listOfTemp = [...this.listOfTemp, this.loanrequestForm.get('Other')?.value];
      this.listOfSelectedItems.push(...current);
    } else {
      this.listOfTemp = [this.loanrequestForm.get('Other')?.value];
      this.listOfSelectedItems = [...current];
    }

    // reset fields
    this.loanrequestForm.get('Other')?.reset();
    this.loanrequestForm.get('clientCRNumber')?.reset();
    this.loanrequestForm.get('amount')?.reset();
    this.loanrequestForm.get('type')?.reset();
    this.loanrequestForm.get('type')?.removeValidators(Validators.required);
    this.loanrequestForm.updateValueAndValidity();

    // 🎉 success toast only when valid
    this.#toast.success("Form submitted successfully!");
  }
}


//   addListOfItems(){
// if (this.loanrequestForm.invalid) {
//     // Mark all controls as touched to trigger validation UI
//     Object.keys(this.loanrequestForm.controls).forEach(key => {
//       const control = this.loanrequestForm.get(key);
//       control?.markAsTouched();
//       control?.updateValueAndValidity();
//     });

//     // Show toastr for the first invalid field
//     const firstInvalidKey = Object.keys(this.loanrequestForm.controls).find(
//       key => this.loanrequestForm.get(key)?.invalid
//     );

//     if (firstInvalidKey) {
//       this.#toast.error(`${firstInvalidKey} is required`);
//     } else {
//       this.#toast.error("Please fill all required fields");
//     }

//     return; // stop save
//   }else{

//     let current:IlistDetails[]=[{

//       id:this.listOfSelectedItems.length+1,
//       amount:this.loanrequestForm.get('amount')?.value,
//       invoiceVAT:this.loanrequestForm.get('invoiceVAT')?.value,
//       clientCRNumber:this.loanrequestForm.get('clientCRNumber')?.value,
//       type:this.translate.instant( this.loanrequestForm.get('type')?.value),
//       file:this.loanrequestForm.get('Other')?.value,
//       typeOfLoan: this.loanrequestForm.get('typeOfLoan')?.value // Add this
//     }]
//     if(this.listOfSelectedItems.length>0){
//       this.listOfTemp = [...this.listOfTemp,this.loanrequestForm.get('Other')?.value]
//       this.listOfSelectedItems.push(...current)
//     }else{
//       this.listOfTemp = [this.loanrequestForm.get('Other')?.value]
//       this.listOfSelectedItems=[...current]
//     }

//     this.loanrequestForm.get('Other')?.reset()
//     this.loanrequestForm.get('clientCRNumber')?.reset()
//     this.loanrequestForm.get('amount')?.reset()
//     this.loanrequestForm.get('type')?.reset()
//     this.loanrequestForm.get('type')?.removeValidators(Validators.required)
//     this.loanrequestForm.updateValueAndValidity()
//   }

//   // ✅ If valid → continue your save logic
//   console.log(this.loanrequestForm.value);
//   this.#toast.success("Form submitted successfully!");


//   }
  deleteItem(id:number){
    this.listOfSelectedItems = this.listOfSelectedItems.filter(item => item.id !== id);
  }
deleteAttachment(attachmentId: string) {
    if (!this.itemDetails?.attachments) {
      this.#toast.error(this.translate.instant('No attachments found'));
      return;
    }

    // Find the index of the attachment with the matching ID
    const attachmentIndex = this.itemDetails.attachments.findIndex((item: any) => item.id === attachmentId);

    if (attachmentIndex === -1) {
      this.#toast.error(this.translate.instant('Attachment not found'));
      return;
    }

    // Get the fileType before removing the attachment
    const fileType = this.itemDetails.attachments[attachmentIndex]?.fileType;

    // Remove the found attachment from itemDetails.attachments
    this.itemDetails.attachments.splice(attachmentIndex, 1);

    // Clear the corresponding form control based on fileType
    switch(Number(fileType)) {
      case 1: // DelegationLetter
        this.loanrequestForm.get('DelegationLetter')?.setValue(null);
        break;
      case 2: // Cashflow
        this.loanrequestForm.get('Cashflow')?.setValue(null);
        break;
      case 3: // FinancialStatements
        this.loanrequestForm.get('FinancialStatements')?.setValue(null);
        break;
    }

    // Force change detection to update the UI
    this.#changeDetectorRef.detectChanges();
  }
  // goBackStep(){

  //   this.goBack.emit(false);

  // }

 checkItAttchmentType(type: number) {
  if (!this.itemDetails?.attachments || this.itemDetails.attachments.length === 0) return null;

  const foundAttachment = this.itemDetails.attachments.find((item: any) => Number(item.fileType) === type);
  return foundAttachment || null;
}
  ngOnDestroy(): void {
      this.encryptStorage.removeItem('stepSelected')
  }

  downloadFile(id:number){
    const attachments:any = this.listOfSelectedItems.find((item:any) => item.id === id);
    if(!attachments){
      this.#toast.error('File not found')
      return
    }
     const aElement = document.createElement('a');
     if(!attachments.hasOwnProperty('filePath')){
      const file = this.listOfTemp.find((item:any) => item.fileContent  === attachments.file);
      //convert base64 to file
      if(file){
        const byteString = atob(file.fileContent);
        const mimeString = file.fileType;
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab]);
      aElement.href = URL.createObjectURL(blob);
      aElement.download = file.fileName;
      aElement.click();
     }else{
      if(attachments.file){
        aElement.href = attachments.filePath;
        const fileName = `${attachments.clientCRNumber}`;
        aElement.download = attachments.file;
        aElement.click();
      }else{
        if(typeof attachments.file === 'object'){
          this.convertBlobToBase64(attachments.file).then((base64String) => {
            const byteString = atob(base64String);
            const mimeString = base64String.split(',')[0].split(':')[1].split(';')[0];
            const ab = new ArrayBuffer(byteString.length);
            const ia = new Uint8Array(ab);
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          const blob = new Blob([ab]);
          aElement.href = URL.createObjectURL(blob);
          aElement.download = attachments.fileName;
          aElement.click();
        });
        }
        this.#toast.error('File not found')
      }
     }
     }
  }
  openOnNewTab(id:number){
    const attachments:any = this.listOfSelectedItems.find((item:any) => item.id === id);
    if(!attachments){
      this.#toast.error('File not found')
      return
    }
    if(!attachments.hasOwnProperty('filePath')){
      //convert base64 to file
      const byteString = atob(attachments.file);
      // const mimeString = attachments.file.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab]);
      window.open(URL.createObjectURL(blob), '_blank');
    }else{
      window.open(attachments.filePath, '_blank');
    }
  }

  handlePeriodInDaysEvent(event:any){
    this.loanrequestForm.get('PeriodInDays')?.setValue(event)
    this.loanrequestForm.get('PeriodInDays')?.updateValueAndValidity()
  }

  convertBlobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  handleClosePopup(event:boolean){
    if(event){
      
      this.modalService.close('loan-request-popup')
      this.#router.navigate(['/loanRequest'])
    }
  }

  private async fetchFileAsBlob(filePath: string): Promise<Blob> {
    try {
      const response = await fetch(filePath);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const blob = await response.blob();
      return blob;
    } catch (error) {
      console.error(`Error fetching file as Blob from ${filePath}:`, error);
      throw error;
    }
  }

   async onSubmit(typeOfRequest: number) {
  if (!this.isConsentChecked() || this.listOfSelectedItems.length === 0) {
    this.handleInvalidForm();
    return;
  }

  try {

    const formData = this.itemDetails && typeOfRequest === 0
      ? await this.prepareEditFormData(typeOfRequest)
      : await this.prepareCreateFormData(typeOfRequest);


    let res: any;
   
    if (this.itemDetails && typeOfRequest === 0) {
      
      res = await this.#loanRequestService.updateLoanRequest(formData);
       
    } else {       
      res = await this.#loanRequestService.createrequest(formData);
      if(res){
         this.modalService.open('loan-request-popup');
      }else{
this.#toast.error(this.translate.instant("res.message"))
      }
      
    }

    this.reqId = this.itemDetails && typeOfRequest === 0 ? this.itemDetails.id : res?.data?.id;

 
  } catch (e) {
    this.closeStatuts.emit(false);
    this.modalService.close('otp-modal');
  }
}
}
