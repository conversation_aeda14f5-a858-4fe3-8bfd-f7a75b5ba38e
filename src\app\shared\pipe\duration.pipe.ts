import { Pipe, PipeTransform, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'duration',
  standalone: true
})
export class DurationPipe implements PipeTransform {
  private translate = inject(TranslateService);
  transform(value: any, ...args: unknown[]): unknown {
    if (isNaN(value)) {
      return value;
    }
    return this.getFormattedStringFromDays(value);
  }

  getFormattedStringFromDays(numberOfDays: number) {
    const years = Math.floor(numberOfDays / 365);
    const months = Math.floor(numberOfDays % 365 / 30);
    const days = Math.floor(numberOfDays % 365 % 30);

    const yearsDisplay = years > 0 ? years + ' ' + (years == 1 ? this.translate.instant('year') : this.translate.instant('years')) : "";
    const monthsDisplay = months > 0 ? months + ' ' + (months == 1 ? this.translate.instant('month') : this.translate.instant('months')) : "";
    const daysDisplay = days > 0 ? days + ' ' + (days == 1 ? this.translate.instant('day') : this.translate.instant('days')) : "";
    return `${yearsDisplay} ${monthsDisplay} ${daysDisplay}`;
  }
}
