import { <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { Component, HostListener, Input, OnInit } from '@angular/core';
import { AuthService } from '../../../../modules/auth/services/auth.service';
import { TranslateModule } from '@ngx-translate/core';
import { AccountApiService } from '../../../service/account-api.service';
import { ToastService } from '../../../../core/services/toast.service';
import { EncryptStorage } from 'encrypt-storage';
import { Router } from '@angular/router';

@Component({
  selector: 'app-create-sub-accounts',
  standalone: true,
  imports: [NgClass,TranslateModule,NgFor],
  templateUrl: './create-sub-accounts.component.html',
  styleUrl: './create-sub-accounts.component.css'
})
export class CreateSubAccountsComponent implements OnInit{
  @Input() isComplete: boolean = false
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
 isOpen = false
 isActiveStatus = this.#encryptTokenStorage.getItem('currentUser')?.userAccountStatusName !=='KYCPending'
 Subaccounts:any
  toggleOpen() {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.getaccounts();
    }
  }
  encryptStorage = new  EncryptStorage('User_info_login');
  constructor(  protected authService: AuthService,private changeAccount:AccountApiService,private toast: ToastService,private router:Router){

  }

  async ngOnInit(): Promise<void> {
    await this.getaccounts();
  }


  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if the click event target is not within the sidebar
    if (!document.querySelector('#notification')!.contains(event.target as Node) && !document.querySelector('#notification-btn')!.contains(event.target as Node)) {
      this.isOpen = false // Close the sidebar
    }
  }

  handleCreateSubAccounts() {
    this.authService.addSubAccount.set(true)
    this.authService.selectedSubAccount.set(undefined)
    // setTimeout(()=>{
    //   this.authService.addSubAccount.set(false)
    // },500)
  }
  async getaccounts(){
    try{
      const response = await this.authService.getAccounts();
      this.Subaccounts = response // Extract the data array from the response
      // console.log(this.Subaccounts, 'this.accounts');


    }catch{
      console.log('error')
    }

  }


  async changeAccountUser(user: string) {
  try {
    const res = await this.changeAccount.apiPostChangeAccountByUserId(user);
    
    // Clear previous auth data
    this.#encryptTokenStorage.removeItem('token');
    this.#encryptTokenStorage.removeItem('tokenExpiry');
    this.#encryptTokenStorage.removeItem('profile')
    // Store old user data
    const oldUser = this.#encryptTokenStorage.getItem('currentUser');
    this.#encryptTokenStorage.setItem('oldUser', oldUser ?? '');
      console.log('API Response:', res.data);
    // Store new auth data
    if (res.data?.token) {
      const encodedToken = encodeURIComponent(res.data.token)
      this.#encryptTokenStorage.setItem('token', encodedToken);
      
      if (res.data.tokenExpiryDate) {
        this.#encryptTokenStorage.setItem('tokenExpiry', res.data.tokenExpiryDate);
      }
    }
    
    // Store user info
    
    this.#encryptTokenStorage.setItem('userInfo', res.data);
    console.log( this.#encryptTokenStorage.setItem('userInfo', res.data));
    
    this.#encryptTokenStorage.setItem('currentUser', res.data);
    console.log(this.#encryptTokenStorage.setItem('currentUser', res.data),"GGGGG");
    
    
    // Redirect and refresh
    this.router.navigate(['/']);
    setTimeout(() => {
      window.location.reload();
    }, 500);
    
  } catch (error: any) {
    this.toast.error(error.error?.message || 'Failed to change account');
    // Consider reverting to old user data if the change failed
  }
}
}
