import {TransactionStatus, TransactionType} from "../../../../shared/enums/transaction.enum";
import {IQuery} from "../../../../core/models/pagination.model";

export interface TransactionModel {
  transactionID: string;
  walletID: string;
  walletName?: string;
  transactionNo: number;
  userID: string;
  userFullName?: string;
  orderNo?: number;
  title?: string;
  titleAr?: string;
  investmentID?: string;
  opportunityID?: string;
  opportunityNo?: number;
  createdOn: Date;
  type: TransactionType;
  readonly typeName?: string;
  status: TransactionStatus;
  readonly statusName: string;
  amount: number;
  dueDate: Date;
  isDeleted: boolean;
  opportubityTitle: string;
  lastUpdateBy: string;
  lastUpdateOn: Date;
  actionName?: string;
  mfaCode: string;
  actualDueDate?: Date;
  newBalance: number;
  newUpComingBalance: number;
  balance: number;
  upComingBalance: number;
}

export interface TransactionSearch extends IQuery{
  page: any;
  userID?: string;
  walletID?: string;
  opportunityID?: string;
  sort?: string;
  itemsCount?: number;
  limit?: number;
  readonly offset?: number;
  readonly pages?: number;
  readonly next?: number;
  readonly previous?: number;
  includeStatus?: TransactionStatus;
  excludeStatus?: TransactionStatus;
  paymentRequestID?: string;
  investmentID?: string;
  dueDateStart?: string;
  dueDateEnd?: string;
  transactionDateStart?: string;
  transactionDateEnd?: string;
  includedStatus?: TransactionStatus[];
  includedTypes?: TransactionType[];
}

export interface TransactionExcelModel {
  transactionNo: number;
  userFullName: string;
  title: string;
  amount: number;
  statusName: string;
  createdOn: string;
  dueDate: string;
}

export interface InvestorTransactionExcelModel {
  transactionNo: number;
  title: string;
  amount: number;
  newBalance?: number;
  ActualDueDate?: string;
  createdOn: string;
  dueDate: string;
  walletName: string;
}
