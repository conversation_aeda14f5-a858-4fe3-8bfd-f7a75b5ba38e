import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CalendarModule } from 'primeng/calendar';
import { ToastService } from '../../../../../core/services/toast.service';
import { LoanRequestService } from '../../../Loan-Request/Service/loan-request.service';
import { OpportunitiesService } from '../../services/opportunities.service';

@Component({
  selector: 'app-opportunity-company-details',
  standalone: true,
 imports: [
    DataTableModule,
     
      CalendarModule,
      FormsModule,
     
      CommonModule,
      TranslateModule,
  
    ],
    providers: [
      DatePipe
    ],
  templateUrl: './opportunity-company-details.component.html',
  styleUrl: './opportunity-company-details.component.css'
})
export class OpportunityCompanyDetailsComponent {

 @Input() OpportunityId!: string;
     loanrequestData: any;
   OpportunityData:any;
   loanRequestDetailData:any;
     listOfSelectedItems:any
companyInfoData:any;
     constructor(
       private toastr: ToastService,
       public translate: TranslateService,
       protected opportunitiesService: OpportunitiesService,
       private route: ActivatedRoute,
        private router: Router,
     ) {
    
  
     }
    
   
     async ngOnInit() {
     
         await this.getmaininfodata();
 
        this.route.params.subscribe((params:any) => {
          this.OpportunityId=params['id'];
          ;})
         
          
     }
  
     

async getmaininfodata() {
  try {
    this.route.params.subscribe(async (params) => {
      this.OpportunityId = params['id'];
    
      
      const res = await this.opportunitiesService.getOpportunityById(this.OpportunityId);
      
      
      if (!res) {
        this.toastr.error('No data received from API');
        return;
      }
      
      // Access data based on your actual API structure
      this.OpportunityData = res.opportunity || res;
      this.loanRequestDetailData = res.loanRequestDetail ;
      this.companyInfoData	 = res.companyInfo	 ;
      
   
    });
  } catch (e: any) {
    console.error('Error:', e);
    this.toastr.error(e.message || 'Failed to load opportunity data');
  }
}
    
async previewFile(filePath: string) {
  try {
    const file = this.listOfSelectedItems.find((item: any) => item.id === filePath);
    if (file && file.filePath) {
      window.open(file.filePath, '_blank'); // This will open the file in a new tab
    }
  } catch (e: any) {
    this.toastr.error(e);  
  }
}



       goBack() {
    this.router.navigate(['/loanRequest']);
  }
   
     get currentLang() {
      return this.translate.currentLang;
    }
   }
   