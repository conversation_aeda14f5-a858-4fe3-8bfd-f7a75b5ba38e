import {Component, inject} from '@angular/core';

import {Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {InputComponent} from "@component/form/input/input.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {ModalService} from "@component/form/modals/modal.service";
import {SelectComponent} from "@component/form/select/select.component";
import {IUserAccounts} from "../models/user-accounts.model";
import {SettingsService} from "../services/settings.service";


import {ToastService} from "../../../core/services/toast.service";
import {OperationType} from "../../../core/enums/operation-type.enum";
import { OtpModalComponent } from '../../../shared/otp/otp-modal/otp-modal.component';
import { OtpService } from '../../../shared/otp/services/otp.service';
import { CommonModule } from '@angular/common';
import { EncryptStorage } from 'encrypt-storage';
import { AuthService } from '../../auth/services/auth.service';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    // FileUploadComponent,
    FormsModule,
    InputComponent,
    ReactiveFormsModule,
    TranslateModule,
    SelectComponent,
    OtpModalComponent,
    CommonModule
    // ChangePasswordComponent
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css'
})
export class SettingsComponent {
  userAccountsForm!: FormGroup
  phoneForm!: FormGroup
  emailForm!: FormGroup
  commercialRegistrationForm!: FormGroup
  userAccount?: IUserAccounts;
  banks: any
bankslist:any
userBanks: any
  public AuthService:AuthService = inject(AuthService)

 #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')
  constructor(
    private fb: FormBuilder,
    protected modalService: ModalService,
    private otpService: OtpService,
    private toast: ToastService,
    private settingsService: SettingsService,
   private translate: TranslateService,

  ) {

  }

  async ngOnInit() {
    this.userAccountsForm = this.fb.group({
      bankId: [null, [Validators.required]],
      accountNo: [null, [Validators.required]],
      iban: [null, [Validators.required]],
    })
    this.phoneForm = this.fb.group({
      newMobileNumber: [null, [Validators.required]],
    })
    this.emailForm = this.fb.group({
      newEmailAddress: [null, [Validators.required]],
    })
    await this.getBanks();
    await this.getUserAccounts();
    await this.getProfileFromStorage();

  }

banksResponse: any;
allbankslist:any
ibanResponse: any;
Iban: any;



async getBanks() {
  try {
    // Fetch all banks
    const banksResponse = await this.settingsService.getBanks();
    this.allbankslist = banksResponse.data || [];

    // Fetch IBAN data
    this.ibanResponse = await this.settingsService.getIBanByBanks();
    this.IBanList = this.ibanResponse; // Make sure this is set

    const userBankId = this.ibanResponse?.data?.bankId;

    // Find matching bank
    const matchedBank = this.allbankslist.find((bank: any) => bank.id === userBankId);

    // Set bankslist - single item if matched, empty if not
    this.bankslist = matchedBank
      ? [{ id: matchedBank.id, name: matchedBank.name }]
      : [];

    // Update form values if data exists
    if (matchedBank) {
      const bankIdControl = this.userAccountsForm.get('bankId');
      const bankaccountNo = this.userAccountsForm.get('accountNo');
      const bankiban = this.userAccountsForm.get('iban');

      bankIdControl?.setValue(matchedBank.id);

      if (this.ibanResponse?.data?.accountNo) {
        bankaccountNo?.setValue(this.ibanResponse?.data?.accountNo);
      }

      if (this.ibanResponse?.data?.iban) {
        bankiban?.setValue(this.ibanResponse?.data?.iban);
      }
    }

  } catch (error) {
    console.error('Error fetching banks:', error);
    this.bankslist = [];
    this.allbankslist = [];
  }
}
  profile:any
profileData:any

  async getProfileFromStorage() {
    const encryptedProfile = this.#encryptTokenStorage.getItem('profile');
    if (encryptedProfile) {
      this.profileData = encryptedProfile;


    } else {
      console.log('No profile data found in storage');
      // You might want to fetch it from API here if needed
    }
  }

private debounceTimer: any;
IBanList: any;
previousIbanValue: string = '';


async getIBanByBanks(ibanValue: string) {
  clearTimeout(this.debounceTimer);

  // If IBAN is valid (SA + length >= 6), fetch bank details
  if (ibanValue.toLowerCase().startsWith('sa') && ibanValue.length == 6 || ibanValue.length == 19) {
    this.debounceTimer = setTimeout(async () => {
      try {
        this.IBanList = await this.settingsService.getIBans(ibanValue);
        if (this.IBanList?.data) {
          this.userAccountsForm.patchValue({
            bankId: this.IBanList?.data?.id,
            accountNo: this.userAccountsForm.get('accountNo')?.value,
          });

          // Update bankslist
          const matchedBank = this.allbankslist.find((bank: any) => bank?.id === this.IBanList?.data?.id);
          this.bankslist = matchedBank ? [matchedBank] : [];
        }
      } catch (error) {
        console.error('Error:', error);
        this.resetBankFields();
      }
    }, 300);
  }

  else if (!ibanValue || !ibanValue.startsWith('SA') || ibanValue.length < 6) {
    this.resetBankFields();
  }
}

resetBankFields() {
  this.userAccountsForm.patchValue({
    bankId: null,
    accountNo: null,
  });
  this.bankslist = [...this.allbankslist];
}
  async getUserAccounts() {
    try {

      if (this.userAccount) {
        this.userAccountsForm.patchValue({
          bankId: {id: this.userAccount?.bankID, name: this.userAccount?.bankName},
          accountNo: this.userAccount?.accountNo,
          iban: this.userAccount?.iban
        });

      }
    } catch (e: any) {
    }
  }
response: any;
  async updateUserAccountsVerified(otp: string) {
    if (this.userAccountsForm.valid) {
      try {
        const body = {
          bankId: this.userAccountsForm.value?.bankId,
          bankName: this.userAccountsForm.value.bankId?.name,
          accountNo: this.userAccountsForm.value?.accountNo,
          iban: this.userAccountsForm.value?.iban,
          mfaCode: otp
        }
       this.response= await this.settingsService.updateUserAccounts(body)
        this.modalService.close('otp-modal')
        this.toast.success(this.response.message );
      } catch (error: any) {
        this.toast.error(error.message || this.translate.instant('FailedToUpdateUserAccount'));
      }
    } else {
      this.userAccountsForm.markAllAsTouched()
    }
  }

  async updateUserAccounts() {
    if (this.userAccountsForm.valid) {
      try {
        // debugger;
        await this.otpService.send({operationType: OperationType.Change_Bank_Account})
        this.modalService.open('otp-modal');

        // this.modalService.open('otp-modal')
      } catch (error: any) {

      }
    } else {
      this.userAccountsForm.markAllAsTouched()
    }
  }


  async changeMobileNumber() {
    if (this.userAccountsForm.valid) {
      try {
        await this.otpService.send({operationType: OperationType.Change_Mobile_Number})
        this.modalService.open('otp-reset-mobile-modal')
      } catch (error: any) {
      }
    } else {
      this.userAccountsForm.markAllAsTouched()
    }
  }

  async updateEmail() {
    if (this.emailForm.valid) {
      try {
        await this.otpService.send({operationType: OperationType.Change_Email})
        this.modalService.open('otp-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }

  async resetEmail(otp: any) {
    if (this.emailForm.valid) {
      try {
        const body = {
          newEmailAddress: this.emailForm.value.newEmailAddress,
          mfaCode: otp
        }
        await this.settingsService.resetEmail(body)
        this.modalService.close('otp-reset-email-modal')
        this.modalService.open('otp-confirm-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }

  async confirmResetEmail(otp: any) {
    if (this.emailForm.valid) {
      try {
        const body = {
          newEmailAddress: this.emailForm.value.newEmailAddress,
          mfaCode: otp
        }
        await this.settingsService.confirmResetEmail(body)
        this.modalService.close('otp-confirm-reset-email-modal')
      } catch (error: any) {
      }
    } else {
      this.emailForm.markAllAsTouched()
    }
  }


  async resetMobileNumber(otp: any) {
    if (this.phoneForm.valid) {
      try {
        const body = {
          newMobileNumber: this.phoneForm.value.newMobileNumber,
          mfaCode: otp
        }
        await this.settingsService.resetMobileNumber(body)
        this.modalService.close('otp-reset-mobile-modal')
        this.modalService.open('otp-confirm-reset-mobile-modal')
      } catch (error: any) {
      }
    } else {
      this.phoneForm.markAllAsTouched()
    }
  }

  async confirmResetMobileNumber(otp: any) {
    if (this.phoneForm.valid) {
      try {
        const body = {
          newMobileNumber: this.phoneForm.value.newMobileNumber,
          mfaCode: otp
        }
        await this.settingsService.confirmResetMobileNumber(body)
        this.modalService.close('otp-confirm-reset-mobile-modal')
        this.toast.success(this.translate.instant( 'MobileNumberUpdatedSuccessfully'))
      } catch (error: any) {
      }
    } else {
      this.phoneForm.markAllAsTouched()
    }
  }

}
