import { Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ToastService } from '../../../../core/services/toast.service';
import { OpportunitiesService } from '../services/opportunities.service';
import { CommonModule, DatePipe } from '@angular/common';
import { colDef, DataTableModule } from '@bhplugin/ng-datatable';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { OpportunityCompanyDetailsComponent } from './opportunity-company-details/opportunity-company-details.component';
import { OpportunityFinancialInformationComponent } from './opportunity-financial-information/opportunity-financial-information.component';
import { InvestmentService } from '../../investments/services/investment.service';
import { OtpModalComponent } from '../../../../shared/otp/otp-modal/otp-modal.component';
import { OtpService } from '../../../../shared/otp/services/otp.service';
import { ModalService } from '@component/form/modals/modal.service';
import { OperationType } from '../../../../core/enums/operation-type.enum';
import { AuthService } from '../../../auth/services/auth.service';
import Swal from 'sweetalert2';
import { SweetAlertService } from '../../../../core/services/sweet-alert.service';
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';
import { EncryptStorage } from 'encrypt-storage';

@Component({
  selector: 'app-opportunity-detail',
  standalone: true,
  imports: [
    DataTableModule,
    CalendarModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    OpportunityFinancialInformationComponent,
    OpportunityCompanyDetailsComponent,
    OtpModalComponent,
    DateFormatPipe,
  ],
  templateUrl: './opportunity-detail.component.html',
  styleUrl: './opportunity-detail.component.css',
})
export class OpportunityDetailComponent {
  id: any;
  OpportunityId!: any;
  userAccountId!: any;
  activeTab!: colDef;
  tabs!: Array<colDef>;
  OpportunityData: any;
  companyInfoData: any;
  loanRequestDetailData: any;
  listOfSelectedItems: any;
  amountopportunityForm!: FormGroup;
  BalanceCount: any;
  inverstmentlist: any;
  amountinvestment: any;
  investDate: any;
  accountDetails: any;
  readonly OpportunityStatus = {
    Published: 1,
    Open: 2,
    Closed: 3,
    Expired: 4,
    Cancelled: 6,
  };
  constructor(
    private toastr: ToastService,
    public translate: TranslateService,
    private otpService: OtpService,
    private opportunitiesService: OpportunitiesService,
    private InvestmentService: InvestmentService,
    private fb: FormBuilder,
    protected modalService: ModalService,
    private route: ActivatedRoute,
    private router: Router,
    private authservice: AuthService,
    private sweetAlertService: SweetAlertService
  ) {}

  getStatusClass(status: number): string {
    switch (status) {
      case 1: // Published
        return 'border-[#EBECEF] text-[#3498db]';
      case 2: // Open
        return 'border-[#EBECEF] text-[#19AD27]';
      case 3: // Closed
        return 'border-[#EBECEF] text-[#e74c3c]';
      case 4: // Expired
        return 'border-[#EBECEF] text-[#f39c12]';
      case 6: // Cancelled
        return 'border-[#EBECEF] text-[#95a5a6]';
      default:
        return 'border-[#EBECEF] text-[#333333]';
    }
  }

  getStatusIcon(status: number): string {
    switch (status) {
      case 1: // Published
        return 'las la-bullhorn';
      case 2: // Open
        return 'las la-unlock';
      case 3: // Closed
        return 'las la-lock';
      case 4: // Expired
        return 'las la-clock';
      case 6: // Cancelled
        return 'las la-ban';
      default:
        return 'las la-question-circle';
    }
  }

  #encryptTokenStorage: EncryptStorage = new EncryptStorage(
    'MDD_Fintech_is_userToken'
  );
  async ngOnInit() {
    this.tabs = [
      {
        title: this.translate.instant('companydetails'),
        field: 'companyInformation',
      },
      {
        title: this.translate.instant('financialInformation'),
        field: 'Riskreport',
      },
    ];

    this.activeTab = this.tabs[0];
    await this.getmaininfodata();
    await this.getBalance();
    await this.getInvestment();
    // await this.getaccountDetails();
    this.initializeForm();
  }
  async getBalance() {
    try {
      this.BalanceCount = await this.authservice.getBalance();

      this.authservice.userBalance.set(this.BalanceCount);
    } catch {
      console.log('error');
    }
  }

  initializeForm(): void {
    this.amountopportunityForm = this.fb.group({
      amount: [
        '',
        [
          Validators.required,

          Validators.min(
            this.OpportunityData?.investableAmount >= 1000
              ? 1000
              : this.OpportunityData?.investableAmount
          ),

          Validators.max(
            this.OpportunityData?.investableAmount >= 1000
              ? this.BalanceCount
              : this.OpportunityData?.investableAmount
          ),
        ],
      ],
    });
  }
  OpportunityDataamount: any;
  async getmaininfodata() {
    try {
      this.route.params.subscribe(async (params) => {
        this.OpportunityId = params['id'];

        const res = await this.opportunitiesService.getOpportunityById(
          this.OpportunityId
        );
        this.OpportunityDataamount = res.amount;

        if (!res) {
          this.toastr.error('No data received from API');
          return;
        }

        // Access data based on your actual API structure
        this.OpportunityData = res;
        this.loanRequestDetailData = res.loanRequestDetail;
        this.companyInfoData = res.companyFinancialInfoDto || res.companyInfo;
      });
    } catch (e: any) {
      console.error('Error:', e);
      this.toastr.error(e.message || 'Failed to load opportunity data');
    }
  }
  changeTab(selectedTab: colDef) {
    this.activeTab = selectedTab;
  }
  resultinvestment: any;
  async investInOpportunity() {
    if (this.amountopportunityForm.valid) {
      this.resultinvestment = await this.sweetAlertService.confirm(
        'AreYouSure',
        'confirmInvestment'
      );
    }
    if (this.resultinvestment.isConfirmed) {
      try {
        this.otpService.send({
          operationType: OperationType.Invest,
        }).then(async (result) => {
          this.modalService.open('otp-create-amount-modal');
        }).catch((e: any) => {
          console.error('Error sending OTP:', e);
        });
    }
    catch (e: any) {
        console.error('Error sending OTP:', e);
      }
  }
}

  async createamount(otp: any) {
    try {
      const body = {
        opportunityId: this.OpportunityId,
        amount: this.amountopportunityForm.get('amount')?.value,
        mfaCode: otp,
      };
      await this.InvestmentService.createamountinvestmet(body);
      this.modalService.close('otp-create-amount-modal');
      this.toastr.success('Amount invested successfully');
      await this.getBalance();
      this.router.navigate(['/opportunities']);
    } catch (e: any) {
      console.error('Error:', e);
    }
  }

  async getaccountDetails(): Promise<boolean> {
    try {
      const encryptedProfile = this.#encryptTokenStorage.getItem('profile');

      if (encryptedProfile) {
        this.accountDetails = encryptedProfile.id;
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
  investstatusName: any;
  investstatus: any;
  async getInvestment() {
    try {
      const hasAccountDetails = await this.getaccountDetails();

      if (!hasAccountDetails || !this.accountDetails) {
        console.error('Cannot load investments - missing account details');
        return;
      }

      if (!this.OpportunityId) {
        console.error('Cannot load investments - missing opportunity ID');
        return;
      }

      const body = {
        opportunityId: this.OpportunityId,
        userAccountId: this.accountDetails,
      };
      const investment = await this.opportunitiesService.getInvestment(body);
      this.inverstmentlist = investment;

      if (this.inverstmentlist.data?.length > 0) {
        this.listOfSelectedItems = [...this.inverstmentlist.data];
        this.investDate = this.listOfSelectedItems[0].investDate;
        this.investstatusName = this.listOfSelectedItems[0].statusName;
        this.investstatus = this.listOfSelectedItems[0].status;

        this.amountinvestment = this.listOfSelectedItems[0].amount;
      } else {
        this.listOfSelectedItems = [];
      }
    } catch (e: any) {
      console.error('API Error:', e);
      this.toastr.error(e.message || 'Failed to load investments');
    }
  }

  async cancelInvestment() {
    const result = await this.sweetAlertService.confirm(
      'AreYouSure',
      'confirmCancelInvestment' // optional - you can create this translation key
    );

    if (result.isConfirmed) {
      // User clicked "Yes"operationType	20
      await this.otpService.send({
        operationType: OperationType.Cancel_Investment,
      });
      this.modalService.open('otp-cancel-amount-modal');
    }
  }

  async CancelInvestment(otp: string) {
    try {
      const body = {
        opportunityId: this.OpportunityId,
        mfaCode: otp,
        amount: this.amountinvestment,
      };
      await this.opportunitiesService.cancelInvestment(body);
      this.modalService.close('otp-cancel-amount-modal');
      this.toastr.success(
        this.translate.instant('Investment cancelled successfully')
      );
      Swal.fire(
        'Success',
        this.translate.instant('Investment cancelled successfully')
      );
      await this.getBalance();
      this.router.navigate(['/opportunities']);
    } catch (e: any) {
      console.error('Error:', e);
      this.toastr.error(e.message || 'Failed to cancel investment');
      Swal.fire('Error', e.message || 'Failed to cancel investment', 'error');
    }
  }

  goBack() {
    this.router.navigate(['/opportunities']);
  }

  get currentLang() {
    return this.translate.currentLang;
  }
  getLabel(key: string): string {
    return this.translate.instant(key);
  }

  calculateProgressWidth(amount: number, maxAmount: number = 5000000): number {
    return Math.min((amount / maxAmount) * 100, 100);
  }
}
