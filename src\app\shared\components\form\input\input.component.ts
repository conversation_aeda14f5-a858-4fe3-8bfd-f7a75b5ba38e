import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {NgClass} from "@angular/common";
import {InputHelperService} from "../../../../core/services/input-helper.service";

@Component({
  selector: 'app-input',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NgClass,

  ],
  templateUrl: './input.component.html',
  styleUrl: './input.component.scss'
})
export class InputComponent implements OnInit {
  @Input() label?: string;
  @Input() iconSrc?: string;
  @Input() control: any;
  @Input() placeholder?: string;
  @Input() startIcon?: string;
  @Input() endIcon?: string;
  @Input() id: string = '';
  @Output() startIconClick = new EventEmitter<any>();
  @Output() endIconClick = new EventEmitter<any>();
  required: boolean = false;
  @Input() type: "button" | "checkbox" | "color" | "date" | "datetime-local" | "email" | "file" | "hidden" | "image" | "month" | "number" | "password" | "radio" | "range" | "reset" | "search" | "submit" | "tel" | "text" | "time" | "url" | "week" = 'text';
  @Input() readonly: boolean = false;
  @Input() autocomplete: boolean = true;
  @Input() autofocus: boolean = false;
  @Input() name?: string;
  @Output() onSpace = new EventEmitter<any>();
  @Output() onChange = new EventEmitter<any>();
  @Output() onKeyup = new EventEmitter<any>();
  @Input() inputModel?: string;
  @Input() maxLength?: number;
  @Output() inputModelChange = new EventEmitter<string>();

  constructor(
    protected inputHelperService: InputHelperService
  ) {
  }

  ngOnInit(): void {
    this.required = this.inputHelperService.isRequired(this.control);
    this.name = this.name ?? this.inputHelperService.getFormControlName(this.control) ?? this.label ?? this.placeholder;
  }

  handleOnChange(event: Event): void {
    this.onChange.emit((event.target as HTMLInputElement).value);
  }
}
