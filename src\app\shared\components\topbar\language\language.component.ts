import { Location } from '@angular/common';
import { Component, inject, ChangeDetectionStrategy } from '@angular/core';
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { changeDir } from '@store/actions';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-language',
  standalone: true,
  
  templateUrl: './language.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LanguageComponent {
//private store = inject(Store);

  constructor(
    private translate: TranslateService,
    private store: Store,
  ) {}

  changeDir() {
    const currentDir = localStorage.getItem('dir');
    const newDir = currentDir === 'rtl' ? 'ltr' : 'rtl';
    const newLang = newDir === 'rtl' ? 'ar' : 'en';

    // Update storage and translation
    localStorage.setItem('dir', newDir);
    localStorage.setItem('lang', newLang); 
    this.translate.use(newLang); 
    window.location.reload()
    this.store.dispatch(changeDir({ newDir }));

   
  }
}