import { Injectable } from '@angular/core';

import {InvestmentModel} from "../models/investment.model";
import { IQuery, PAGINATION } from '../../../../core/models/pagination.model';
import { BaseResponse } from '../../../../core/models/api-response.model';
import { ApiService } from '../../../../core/services/api.service';

@Injectable({
  providedIn: 'root'
})
export class InvestmentService {


   pagination = PAGINATION;
  private path = '/client/Investments';

  constructor(
    private apiService: ApiService,
  ) {
  }

  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);



  async getList(query?: IQuery) {
    query = {...query, limit: this.pagination.pageSize} as IQuery;
    const response:any = await this.apiService.post<BaseResponse<InvestmentModel>>(`${this.path}/Search`, query);
    this.pagination.loading = false;
    this.pagination.totalRows = response.totalRecords;
    return response.data;
  }


    async createamountinvestmet(body:any){

    return await this.apiService.post(`${this.path}`, body);
  }
}
