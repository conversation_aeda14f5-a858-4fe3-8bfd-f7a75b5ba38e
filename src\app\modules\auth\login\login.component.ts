import { Component } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { Router, RouterLink } from "@angular/router";
import { InputComponent } from "@component/form/input/input.component";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ButtonComponent } from "@component/form/button/button.component";
import { AuthService } from "../services/auth.service";
import { ModalService } from "@component/form/modals/modal.service";
import { IValidateLoginOTPBody } from "../models/login.model";
import { ToastService } from "../../../core/services/toast.service";
import { phoneNumberValidator } from '../../../shared/validators/phone.validator';
import { EncryptStorage } from 'encrypt-storage';
import { OtpModalComponent } from '../../../shared/otp/otp-modal/otp-modal.component';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    RouterLink,
    InputComponent,
    TranslateModule,
    ButtonComponent,
    OtpModalComponent,
    CommonModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  loginForm!: FormGroup
  isPasswordHidden = true;
  token?: any;
  encryptStorage = new  EncryptStorage('User_info_login');
  #encryptTokenStorage:EncryptStorage= new EncryptStorage('MDD_Fintech_is_userToken')

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    protected modalService: ModalService,
    private toast: ToastService,
    private translate: TranslateService,
  ) {
  } 

  ngOnInit() {
    localStorage.clear()
    this.loginForm = this.fb.group({
      nationalId: [null, [Validators.required,Validators.pattern('^(1|2){1}[0-9]{9}$'),Validators.maxLength(10),Validators.minLength(10)]],
      password: [null, [Validators.required]]//passwordValidator()
    })
  }

  async onSubmit() {
    this.loginForm.markAllAsTouched()
    if (this.loginForm.valid) {
      try {
        const send ={
          nationalId: this.loginForm.value.nationalId,
          password: this.loginForm.value.password,
          deviceToken: "string",
          accountType: 1
        }
        this.token = await this.authService.login2(send)
   
        
        this.modalService.open('otp-modal')
      } catch (e: any) {
        this.toast.error(
          this.translate.currentLang == 'ar' ? e.error.result['arabicMessage'] : e.error.result['englishMessage'])
      }
    } else {
      console.log('Error')
    }
  }
  logindata:any

  async verifyOtp(otp: string) {
    try {
      const body: IValidateLoginOTPBody = {
        token: this.token.data ?? localStorage.getItem('token'),
        code: otp,
      } as IValidateLoginOTPBody;
      this.logindata=  await this.authService.validateLoginOTP2(body);
       
      if(this.logindata?.passwordExpired==true){
    
      this.toast.error(this.translate.instant( "Password is expire please chnage password"))
      this.modalService.close('otp-modal')
      this.router.navigate(['/change-password'])
      
      }else if(this.logindata?.passwordExpired==false){
      const decodedToken = decodeURIComponent(this.logindata?.token!);
      this.#encryptTokenStorage.setItem('token', decodedToken);
      this.#encryptTokenStorage.setItem('tokenExpiry', this.logindata?.tokenExpiryDate);
      this.encryptStorage.setItem('userInfo', this.logindata);
      this.modalService.close('otp-modal')
      this.router.navigate(['/'])
      }
     
    } catch (error: any) {
      this.toast.error(error.error ?
        this.translate.currentLang == 'ar' ? error.error['arabicMessage'] : error.error['englishMessage'] : error);
    }
  }



  togglePasswordVisibility = () => this.isPasswordHidden = !this.isPasswordHidden;
}
