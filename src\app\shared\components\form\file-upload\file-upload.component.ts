import {Component, EventEmitter, Input, OnChanges, OnInit, Output, signal, SimpleChanges, ViewContainerRef} from '@angular/core';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {ReactiveFormsModule} from "@angular/forms";
import {FilePreviewComponent} from "../file-preview/file-preview.component";

import {JsonPipe} from "@angular/common";
import {InputHelperService} from "../../../../core/services/input-helper.service";

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    TranslateModule,
    // LucideAngularModule,
    // NgxTippyModule,
    
  ],
  templateUrl: './file-upload.component.html',
  styleUrl: './file-upload.component.scss'
})
export class FileUploadComponent implements OnInit,OnChanges {
  @Input() label!: string;
  @Input() control!: any;
  @Input() errorMessage?: any;
  @Input() placeholder?: string;
  @Input() isMultiple: boolean = false;
  @Input() isConvertToBase64: boolean = false;
  selectedFileName =signal<string>(''); // Initialize selectedFileName as an empty string
  required: boolean = false;
  name: string = 'text';
  @Input() endIcon?: string;
  @Output() endIconClick = new EventEmitter<any>();

  constructor(
    protected inputHelperService: InputHelperService,
    private viewContainerRef: ViewContainerRef,
    private translate: TranslateService,
  ) {
  }

  onFileSelected(event: Event) {
    const files = (event.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      if (this.isMultiple) {
        // Reset selected file names and values
        this.selectedFileName?.set('');
        const fileArray = [];
        // Iterate through all selected files
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          this.selectedFileName?.update(el=> el + file.name + (i < files.length - 1 ? ', ' : ''));
          // this.selectedFileName += file.name + (i < files.length - 1 ? ', ' : ''); // Add file names separated by comma
          fileArray.push(file);
        }
        // Set the control value to the array of files
        this.control.setValue(fileArray);
      } else {
        const selectedFile = files[0];
        // Convert the selected file to Base64
        if (this.isConvertToBase64) {
          const reader = new FileReader();
          reader.onload = (e: ProgressEvent<FileReader>) => {
            if (e.target && typeof e.target.result === 'string') {
              const dataUrl: string = e.target.result;
              const base64String = dataUrl.split(',')[1];
              this.control.setValue({
                fileName: selectedFile.name,
                fileContent: base64String,
                fileContentType: selectedFile.type,
              });
              this.selectedFileName ?.set(selectedFile.name);
            }
          };
          reader.readAsDataURL(selectedFile);
        } else {
          this.control.setValue(selectedFile);
        }
      }
    } else {
      this.selectedFileName ?.set('');
      // Clear the FormGroup's value when no file is selected
      this.control.setValue(null);
    }
  }

  isBlob(value: any): boolean {
    return value instanceof Blob;
  }
  ngOnInit(): void {
    this.required = this.inputHelperService.isRequired(this.control);
    this.name = this.inputHelperService.getFormControlName(this.control) ?? this.label ?? this.placeholder ?? this.name;
    this.control.valueChanges.subscribe((value: any) => {
      if(value && typeof value == 'object' &&  this.isBlob(value)){
        this.selectedFileName.set(value['fileName']?value['fileName']:value['name']);
      }else if(value && typeof value == 'string'){
        this.selectedFileName.set(value);
      }
    });
    // this.selectedFileName = this.control?.value?.fileName ?? '';
  }

  filePreview() {
    const filePreviewRef = this.viewContainerRef.createComponent(FilePreviewComponent);
    this.selectedFileName = this.control.value?.fileName;
    if (this.inputHelperService.isUrl(this.control.value?.fileContent)) {
      filePreviewRef.instance.open(
        {
          name: this.control.value.fileName,
          url: this.control.value?.fileContent,
          contentType: this.control.value.fileContentType
        }
      );
    } else {
      filePreviewRef.instance.open(
        {
          name: this.control.value.fileName,
          content: this.control.value.fileContent,
          contentType: this.control.value.fileContentType
        }
      );
    }
    filePreviewRef.instance.componentRef = filePreviewRef; // Passing the ComponentRef to the instance
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['control']) {

      if(this.control.value && (typeof this.control.value == 'object' && this.isBlob(this.control.value) || typeof this.control.value == 'string')){
        this.selectedFileName.set(this.control.value&&this.control.value['fileName']?this.control.value['fileName']:this.control.value);
      }
    }
  }
}
