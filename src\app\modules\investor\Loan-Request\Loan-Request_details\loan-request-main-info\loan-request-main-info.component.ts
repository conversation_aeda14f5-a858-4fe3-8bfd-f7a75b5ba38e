import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CalendarModule } from 'primeng/calendar';
import { ToastService } from '../../../../../core/services/toast.service';
import { LoanRequestService } from '../../Service/loan-request.service';

@Component({
  selector: 'app-loan-request-main-info',
  standalone: true,
  imports: [
    DataTableModule,

      CalendarModule,
      FormsModule,

      CommonModule,
      TranslateModule,

    ],
    providers: [
      DatePipe
    ],
  templateUrl: './loan-request-main-info.component.html',
  styleUrl: './loan-request-main-info.component.css'
})
export class LoanRequestMainInfoComponent {


     @Input() loanRequest!: any;
     loanrequestData: any;


     listOfSelectedItems:any

     constructor(
       private toastr: ToastService,
       public translate: TranslateService,
       protected LoanRequestservice: LoanRequestService,
        private router: Router,
     ) {


     }


     async ngOnInit() {
         await this.getmaininfodata();
     }



     async getmaininfodata() {
      try {
        this.listOfSelectedItems = this.loanRequest?.details;
      } catch (e: any) {
        this.toastr.error(e);
      }
    }

async previewFile(filePath: string) {
  try {
    const file = this.listOfSelectedItems.find((item: any) => item.id === filePath);
    if (file && file.filePath) {
      window.open(file.filePath, '_blank'); // This will open the file in a new tab
    }
  } catch (e: any) {
    this.toastr.error(e);
  }
}



       goBack() {
    this.router.navigate(['/loanRequest']);
  }

     get currentLang() {
      return this.translate.currentLang;
    }
   }
