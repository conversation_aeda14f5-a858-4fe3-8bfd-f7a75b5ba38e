import { Component, OnInit } from '@angular/core';
import { SettingsService } from '../../services/settings.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { ToastService } from '../../../../core/services/toast.service';
import { CommonModule, DatePipe } from '@angular/common';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MDModalModule } from '@component/form/modals';
import { EnumSelectPipe } from '../../../../token-mask.pipe';
import { SettingsComponent } from '../../settings/settings.component';

import { colDef } from '@bhplugin/ng-datatable';
import { EditFinticInfoComponent } from '../edit-fintic-info/edit-fintic-info.component';
import { AuthService } from '../../../auth/services/auth.service';
import { EncryptStorage } from 'encrypt-storage';
import { ProfileService } from '../../services/profile.service';
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';

@Component({
  selector: 'app-account-setting',
  standalone: true,
  imports: [
      TranslateModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MDModalModule,
        TranslateModule,
        SettingsComponent,
        DateFormatPipe,
        EditFinticInfoComponent
  ],
  providers:[DatePipe],
  templateUrl: './account-setting.component.html',
  styleUrl: './account-setting.component.css'
})

export class AccountSettingComponent implements OnInit {

  AccountDetailsForm!: FormGroup;
  response: any;
  activeTab!: colDef;
  tabs!: Array<colDef>;
  profileData: any;
  accountDetails: string | null = null;
  allcounts: any;

  #encryptTokenStorage: EncryptStorage = new EncryptStorage('MDD_Fintech_is_userToken');

  constructor(
    private translate: TranslateService,
    private router: Router,
    public authService: AuthService,
    private profileService: ProfileService
  ) {}

  async ngOnInit(): Promise<void> {
    this.tabs = [
      { title: this.translate.instant("EditInfo"), field: "EditInfo" },
      { title: this.translate.instant("EditFinticInfo"), field: "EditFinticInfo" },
      { title: this.translate.instant("Notifications"), field: "Notifications" },
      { title: this.translate.instant("pauseAccount"), field: "pauseAccount" },
    ];

    this.activeTab = this.tabs[0];

 this.profileData = await this.profileService.loadProfileData();
  }


  goBack() {
    this.router.navigate(['/settings/qualification-Request']);
  }

  changeTab(selectedTab: colDef) {
    this.activeTab = selectedTab;
  }


}
