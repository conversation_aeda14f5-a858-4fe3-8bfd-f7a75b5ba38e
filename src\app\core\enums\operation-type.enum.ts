export enum OperationType {
  Register = 1,
        Invest = 2,
        Request_Withdraw = 3,
        Request_Deposit = 4,
        Approve_Deposit = 5,
        Approve_Withdraw = 6,
        Change_Password = 7,
        Change_Bank_Account = 8,
        <PERSON>gin = 9,
        Create_Opportunity = 10,
        Pay_Withdraw = 11,
        Create_Approved_Withdraw = 12,
        Create_Approved_Deposit = 13,
        Approve_Transaction = 14,
        Rest_Password = 15,
        Enable_Face_ID = 16,
        CLose_Opportunity = 17,
        Cancel_Opportunity = 18,
        Close_Investment = 19,
        Cancel_Investment = 20,
        Transfer_Balance = 21,
        Change_Email = 22,
        Confirm_Change_Email = 23,
        Change_Mobile_Number = 24,
        Confirm_Change_Mobile_Number = 25,
        Create_Paid_Withdraw = 26,
        Register_Email = 27,
        Register_Mobile = 28,
        Qualification_Request = 29,
        Qualification_Approve_Reject = 30,
        Loan_Request = 31,
        status_Bank_Account=32,
        Company_Registration = 33,
        Aprrove_Payment = 34,
        Client_Approve_Loan_Request = 35,
        Approve_Opportunity=36,
        Create_Manual_Opportunity = 37
}
