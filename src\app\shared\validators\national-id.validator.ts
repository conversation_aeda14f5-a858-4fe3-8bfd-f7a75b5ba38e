import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function nationalIDValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    // Check if the value is a number and if its length is exactly 10
    const isNumber = /^\d+$/.test(value);
    const hasCorrectLength = value.length === 10;

    // The national ID is valid if it is a number and its length is 10
    const nationalIDValid = isNumber && hasCorrectLength;

    return !nationalIDValid ? { invalidNationalID: true } : null;
  };
}
