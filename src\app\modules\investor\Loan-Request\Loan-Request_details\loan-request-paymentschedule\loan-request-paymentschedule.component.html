 <div class="box col-span-12 lg:col-span-6">
    <div class="bb-dashed mb-4 flex flex-wrap items-center justify-between gap-4 pb-4 lg:mb-6 lg:pb-6">
      <h4 class="h4">{{ 'Paymentschedule' | translate }}</h4>
      <div class="flex flex-wrap md:flex-nowrap items-center gap-4">
        <div
          class="bg-primary/5 dark:bg-bg3 border border-n30 dark:border-n500 flex gap-3 rounded-[30px] focus-within:border-primary p-1 items-center justify-between min-w-[200px] xxl:max-w-[319px] w-full">
          <input (change)="handleSearch($event)"
                 #searchValue
                 class="bg-transparent border-none text-sm ltr:pl-4 rtl:pr-4 py-1 w-full" placeholder="Search"
                 type="text"/>
                 <i class="fa-solid fa-sliders"></i>
          <button

            (click)="searchValue.click()"
            class="bg-primary shrink-0 rounded-full w-7 h-7 lg:w-8 lg:h-8 flex justify-center items-center text-n0">
            <i class="las la-search text-lg"></i>
          </button>
        </div>
        <div class="flex items-center gap-3 whitespace-nowrap">
          <i class="las la-sliders-h"></i>
        </div>
        <!-- <div class="flex items-center gap-3 whitespace-nowrap">
          <span>{{ 'sortBy' | translate }} : </span>
          <app-dropdown/>
        </div> -->
      </div>
    </div>
    <div class="mt-5 overflow-x-auto">
      

       
      <div class="datatable">
       

        <ng-datatable
          (changeServer)="handePageChange($event.current_page)"
          [columns]="columns"
          [isServerMode]= "false"
          [loading]="depositService.pagination.loading"
          [page]="depositService.pagination.page"
          [rows]="deposits()"
          [showPageSize]="false"
        
          [pageSize]="15"
          [pageSizeOptions]="[15]"
          [totalRows]="depositService.pagination.totalRows"
          class="whitespace-nowrap table-hover"
          firstArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M13 19L7 12L13 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M16.9998 19L10.9998 12L16.9998 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
          lastArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M11 19L17 12L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path opacity="0.5" d="M6.99976 19L12.9998 12L6.99976 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg> '
          nextArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
          paginationInfo=" {{ 'pagination_info' | translate:{ start: '{0}', end: '{1}', total: '{2}' } }}"
          previousArrow='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 rtl:rotate-180"> <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>'
        >
        <ng-template let-value="data" slot="transactionDate">
          <span >
            {{ value.transactionDate | dateFormat }}
          </span>
      
        </ng-template>
        <ng-template let-value="data" slot="amount">
          {{ value.amount | number }}
        </ng-template>
        
        </ng-datatable>
      </div>

    </div>
  </div>