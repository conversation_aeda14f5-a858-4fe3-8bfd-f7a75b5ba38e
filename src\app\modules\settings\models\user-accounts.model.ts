export interface IUserAccounts {
  userBankAccountID: string
  userID: string
  userName: string
  bankID: string
  bankName: string
  accountNo: string
  iban: string
  notes: string
  details: string
  isDeleted: boolean
  lastUpdateBy: string
  lastUpdateByName: string
  lastUpdateOn: string
  actionName: string
  mfaCode: string
  walletID: string
  walletName: string
}


export enum QualificationReason
{
    Have_assets_worth_three_million=0,
    Have_you_ever_worked_in_the_financial_sector=1,
    Degree_in_the_field_of_finance=2,
  
}