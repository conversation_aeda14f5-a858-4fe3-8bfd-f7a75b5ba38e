import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { interval, Subscription } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { InputComponent } from '../../../../shared/components/form/input/input.component';
import { LoadingBarService } from '@ngx-loading-bar/core';
import { OtpService } from '../../../otp/services/otp.service';
import { OperationNumber } from '../../../../shared/constants/enums';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-otp-confirm',
  standalone: true,
  imports: [
    InputComponent,
    AsyncPipe,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './otp-confirm.component.html',
  styleUrls: ['./otp-confirm.component.css'],
})
export class OtpConfirmComponent implements OnInit {
  otpForm!: FormGroup;
  @Output() isOTPAdded = new EventEmitter<boolean>();
  @ViewChild('submitButton') submitButton!: ElementRef<HTMLButtonElement>;
  countdown: number = 0;
  countdownSubscription: Subscription | null = null;
  readonly countdownStart = 60; // Countdown start time in seconds
  @Output() submitOtp = new EventEmitter<string>();
  @Output() reSend = new EventEmitter<void>();
  @Input() title: string = 'verificationSent';
  @Input() onSubmitOTP?: (otp: string) => void;
  @Input() isSubmitting: boolean = false;

  operationNum?: OperationNumber;

  constructor(
    private cdr: ChangeDetectorRef,
    protected loadingBar: LoadingBarService,
    private fb: FormBuilder,
    private otpService: OtpService,
    private toastr: ToastrService,
    private translate: TranslateService
  ) {}

  get otpControls() {
    return (this.otpForm.get('otp') as FormArray).controls;
  }
  ngOnInit() {
    this.otpForm = this.fb.group({
      otp: this.fb.array(new Array(6).fill('').map(() => this.fb.control(''))),
    });
  }

  ngAfterViewInit(): void {
    this.focusInput(0);
    this.startCountdown();
    this.cdr.detectChanges(); // This ensures the change detection cycle is completed
  }

  handleFocus(index: number): void {
    const otpArray = this.otpForm.get('otp') as FormArray;
    otpArray.at(index).markAsTouched();
  }

  handleInput(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (input.value) {
      if (index < otpArray.length - 1) {
        this.focusInput(index + 1);
      } else {
        this.submitButton.nativeElement.focus();
        // const submitButton = document.querySelector('button[type=submit]') as HTMLButtonElement;
        // submitButton.focus();
      }
    }
  }

  handleKeyDown(event: KeyboardEvent, index: number): void {
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (
      !/^[0-9]{1}$/.test(event.key) &&
      event.key !== 'Backspace' &&
      event.key !== 'Delete' &&
      event.key !== 'Tab' &&
      !event.metaKey
    ) {
      event.preventDefault();
    }
    if (event.key === 'Delete' || event.key === 'Backspace') {
      if (index > 0) {
        otpArray.at(index - 1).setValue('');
        this.focusInput(index - 1);
      }
    }
  }

  handlePaste(event: ClipboardEvent): void {
    event.preventDefault();
    const clipboardData = event.clipboardData;
    if (!clipboardData) {
      return;
    }
    const text = clipboardData.getData('text');
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (!new RegExp(`^[0-9]{${otpArray.length}}$`).test(text)) {
      return;
    }
    const digits = text.split('');
    otpArray.controls.forEach((control, index) =>
      control.setValue(digits[index])
    );
    this.submitButton.nativeElement.focus();
    // const submitButton = document.querySelector('button[type=submit]') as HTMLButtonElement;
    // submitButton.focus();
  }

  private focusInput(index: number): void {
    const input = document.getElementById(
      'otp-input-' + index
    ) as HTMLInputElement;
    input?.focus();
    input?.select();
  }

  verify(): void {
    if (this.otpForm.valid) {
      this.submitOtp.emit(this.otpForm.value.otp.join(''));
      this.isOTPAdded.emit(true);
    } else {
      this.otpForm.markAllAsTouched();
      this.isOTPAdded.emit(false);
    }
  }

  startCountdown() {
    this.countdown = this.countdownStart;
    this.clearCountdown();
    this.countdownSubscription = interval(1000).subscribe(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        this.clearCountdown();
      }
    });
  }

  clearCountdown() {
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
      this.countdownSubscription = null;
    }
  }

  resendOTP() {
    // this.otpService.send({operationType: this.operationNum.}).subscribe((res : any) => {
    //   this.startCountdown();
    //   this.toastr.success(
    //     this.translate.currentLang === 'en'
    //       ? res.englishMessage
    //       : res.arabicMessage
    //   );
    // });
  }
}
