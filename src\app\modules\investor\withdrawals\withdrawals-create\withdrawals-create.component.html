<div class="flex gap-4">
  <div class="box mb-4 flex-1 xxxl:mb-6">
    <div class="mb-6 pb-6 bb-dashed flex justify-between items-center">
      <h4 class="h4">{{ 'WithdrowsList' | translate }}</h4>
      <button class="h4 justify-end font-semibold text-lg " routerLink="/withdrawals">{{ 'Back' | translate }}</button>
    </div>
    <h4 class="mt-11 font-extrabold text-xl">{{ 'ConvertWithdrawtocredit'|translate}}</h4>
    <form [formGroup]="form" class="flex flex-col space-y-4">

      <div class="col-span-2 md:col-span-2 mt-8">
        <app-input [control]="form.get('amount')" [label]="'Theamounttobewithdrawn'" (keyup)="checkBalance($event)" />
        <!-- Validation Messages -->

        <div *ngIf="form.get('amount')?.errors?.['insufficientBalance']">
          ❌ {{ "Amountexceedsyourbalanceof" |translate}} {{ BalanceCount | currency: 'SAR'}}
        </div>

        <div *ngIf="form.get('amount')?.errors?.['min']">
          ❌{{"Minimumamountis1." |translate}}
        </div>


      </div>
  
      <div class="col-span-2 md:col-span-2 mt-8">
          @if( this.acountdetails?.data){ 
        <label> {{ "BankAccount"| translate }} </label>
       
        <div class="bg-[#F9F9FB] rounded-md p-4 my-6 mx-4 w-2/3 h-36 dark:bg-inherit px-6 dark:border-2 dark:border-n500">
          <div class="grid grid-flow-row-dense grid-cols-3 grid-rows-3 ">
            <div class="col-span-2 text-[#98989B] font-medium text-xs">
              <p class="my-3"><i class="las la-map-marker mx-2"></i> {{ "bankName" |translate }} : {{bankAccount}}</p>
              <p class="my-2">{{ "IBAN" |translate }} : {{ibanaccount}}</p>
            </div>

            <div [ngClass]="{
        'text-green-600  border-green-500 bg-green-100 ': this.acountdetails?.data?.status == 2,
        'text-red-500 border-red-500 bg-red-100': this.acountdetails?.data?.status == 1,
        'text-yellow-500 border-yellow-500 bg-yellow-100': this.acountdetails?.data?.status == 3
      }" class="justify-end w-24 rounded-3xl text-center border-2 my-4 mx-20 p-3">
              {{ getStatusName(this.acountdetails?.data?.status) || ("notfound" |translate) }}
            </div>

          </div>

        </div>
        }
        <!------not applied----->
        @if(this.acountdetails?.data?.status != 2){
        <div class="bg-[#F9F9FB] rounded-md p-4 my-6 mx-4 w-full h-36 dark:bg-inherit dark:border-2 dark:border-n500">

          <div class=" text-[#98989B] text-center font-medium text-xs mx-auto my-6 w-1/2">
            <p class="my-3"> {{ "Youcannotsubmitatransferrequestfromyourinvestmentportfolio" |translate }} </p>

          </div>





        </div>
        }
        <!----------nofound------------>
        @if(this.acountdetails?.data==null){
        <div class="bg-[#F9F9FB] rounded-md p-4 my-6 mx-4 w-full h-36">

          <div class="col-span-2 text-[#98989B] font-medium text-xs mx-auto my-6 flex justify-center">
            <p class="my-3"> {{ "Youcannotrequesttowithdrawmoneyfromyourwallet" |translate }} <a
                class="underline-offset-2 text-blue-600 cursor-pointer" routerLink="/settings">{{
                "Amendingthebankaccount" |translate}}</a> </p>

          </div>




        </div>
        }



      </div>


      @if(this.acountdetails?.data?.status == 2){
      <div class="col-span-2 flex justify-end w-full gap-2 md:gap-6 mt-2">
        <button (click)="createWithdraw()" class="btn" type="submit">{{ 'send' | translate }}</button>
      </div>
      }

    </form>
  </div>
</div>
@if (modalService.isOpen('otp-modal')) {
<app-otp-modal (submitOtp)="createApprovedWithdraw($event)" />
}