import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalService } from '@component/form/modals/modal.service';
import { acceptLegalDocument } from "./accept-legal-document.service"
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-accept-legal-document',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './accept-legal-document.component.html',
  styleUrl: './accept-legal-document.component.css'
})
export class AcceptLegalDocumentComponent implements OnInit {
  /*
  
        Policy = 1,
        TermsAndConditions = 2,
        Agreement = 3
  */
  temp: any
  currentUserObj:any
  StatusMap: { [key: number]: string } = {
    1: 'Policy',
    2: 'TermsAndConditions',
    3: 'Agreement'
  };
  activeIndex: number | null = null;
  documentIds :string[]=[]
 
  
  constructor(private modalService: ModalService, private acceptLegalDocumentService: acceptLegalDocument, private translate: TranslateService,
  ) {


  }

  async ngOnInit(): Promise<void> {
     await this.getLegalDocuments()
  }

  accordionItems = [
    {
      title:"",
      content:""
    }
  ] ;
  async getLegalDocuments() {
    const response = await this.acceptLegalDocumentService.getLegalDocuments()
    this.temp = response
     if (!this.temp?.data?.length) {
    return; // No data, do nothing
  }

  // Open modal because there's data
  this.modalService.open("acceptLegalDocument");
    this.accordionItems = this.temp.data.map((doc: any) => {
      this.documentIds.push(doc.id)
      const documentType = this.translate.instant(this.StatusMap[doc.documentType]) || "Unknown Document Type"
      const content = this.translate.currentLang === 'ar' ? doc.contentAr : doc.contentEn 
      return {
        title: documentType,   
        content: content       
      };
    });
  }
 
   
  toggleSection(index: number) {
    this.activeIndex = this.activeIndex === index ? null : index;
  }
 async acceptAllDocumentsSequentially() {
   var response = await this.acceptLegalDocumentService.acceptLegalDocuments(this.documentIds)

   if(response.success){
    this.modalService.close("acceptLegalDocument")
    }
  }
}
 
