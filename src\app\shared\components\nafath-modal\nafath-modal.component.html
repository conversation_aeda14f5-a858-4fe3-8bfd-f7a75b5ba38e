


<ng-modals [id]="'nafath-modal'" [placement]="'modal-top'"

           className="fixed flex flex-col  transition-all md:w-[30rem] duration-300 ease-in-out left-2/4 z-[1050] -translate-x-2/4 translate-y-8">


      <div class="flex justify-center">
        <div class="max-w-md mx-auto text-center bg-white px-4 sm:px-8 py-10 rounded-xl shadow">
          <header class="mb-4">

            <div class="flex gap-2 text-center mb-4 justify-center">
                          <h1 class="text-2xl font-bold">{{ 'verifyIdentityThrough' | translate }} </h1>
                          <h1 class="text-2xl font-black text-teal-600"> {{ 'nafath' | translate }}</h1>


            </div>

            <p class="text-[15px] text-slate-500">{{ 'verificationCodeIs' | translate }}</p>
          </header>
          <div id="otp-form" class="w-full ">
            <div class="flex w-full items-center justify-center  gap-3" >
              <input
                      readonly
                      [value]="truncatedRandom"
                      disabled
                     class="w-16 h-16 text-center select-none  text-2xl font-extrabold text-slate-900 bg-slate-100 border border-slate-200 appearance-none rounded-full p-4 outline-none focus:bg-white focus:border-teal-400 focus:ring-2 focus:ring-teal-100"
                     maxlength="1"
                     type="text">
            </div>
              <span
                class="w-full  mt-5 inline-flex justify-center whitespace-nowrap rounded-lg bg-teal-600/10 text-teal-600  px-4 py-3 text-sm font-bold   transition-colors duration-150"
                >
                {{ 'openNafathApp' | translate}}
              </span>

          </div>

        </div>
      </div>


</ng-modals>





