export interface IBankAccount {
  userBankAccountID: string
  userID: string
  userName: string
  bankID: string
  bankName: string
  accountNo: string
  iban: string
  notes: string
  details: string
  isDeleted: boolean
  lastUpdateBy: string
  lastUpdateByName: string
  lastUpdateOn: string
  actionName: string
  mfaCode: string
  walletID: string
  walletName: string
}
 export interface Icreditwallet{
  id:number;
  notes:string;
  lastUpdateBy:string
  lastUpdateById: string,
lastUpdateDate: string,
deletedAt: string,
isDeleted: boolean,
userAccountId: string,
userId: string,
accountNumber: string,
iban: string,
bank: string
loanRequestId: string
 }


