import {Component, Input} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {ReactiveFormsModule} from "@angular/forms";
import {NgClass} from "@angular/common";

@Component({
  selector: 'app-textarea',
  standalone: true,
  imports: [
    TranslateModule,
    ReactiveFormsModule,
    NgClass
  ],
  templateUrl: './textarea.component.html',
  styleUrl: './textarea.component.scss'
})
export class TextareaComponent {
  @Input() label?: string;
  @Input() placeholder?: string;
  @Input() control: any;
  @Input() rows?: number;
}
