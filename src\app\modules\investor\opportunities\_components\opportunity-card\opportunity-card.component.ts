import { Component, Input } from '@angular/core';
import { Opportunity } from '../../models/opportunity';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonComponent } from '@component/form/button/button.component';


@Component({
  selector: 'app-opportunity-card',
  standalone: true,
  imports: [RouterLink, CommonModule, ButtonComponent, TranslateModule],
  templateUrl: './opportunity-card.component.html',
  styleUrl: './opportunity-card.component.css'
})
export class OpportunityCardComponent {
  @Input({ required: true })
  opportunity!: Opportunity;
}
