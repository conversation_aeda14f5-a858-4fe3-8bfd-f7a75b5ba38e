import {Component} from '@angular/core';
import {ButtonComponent} from "@component/form/button/button.component";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {InputComponent} from "@component/form/input/input.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {passwordValidator} from "../../../shared/validators/password.validator";
import {confirmPasswordValidator} from "../../../shared/validators/confirm-password.validator";
import {ActivatedRoute, Router} from "@angular/router";
import {AuthService} from "../services/auth.service";
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [
    ButtonComponent,
    FormsModule,
    InputComponent,
    TranslateModule,
    ReactiveFormsModule
  ],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.css'
})
export class ResetPasswordComponent {
  formGroup: FormGroup;
  isPasswordHidden = false;
  token?: string;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private translate: TranslateService,
    private router: Router,
    private toast:ToastrService
  ) {
    this.formGroup = this.fb.group({
      newPassword: [null, [Validators.required, passwordValidator()]],
      confirmPassword: [null, [Validators.required]]
    }, {
      validators: confirmPasswordValidator('newPassword', 'confirmPassword')
    });
  }

  ngOnInit() {
    this.token = history.state.token;
  }

  togglePasswordVisibility = () => this.isPasswordHidden = !this.isPasswordHidden;

  async onSubmit() {
   
    if (this.formGroup.valid && this.token) {
      try {
        const body = {
          password: this.formGroup.value.newPassword,
          token: this.token,
        }
        await this.authService.resetPassword(body)
        this.router.navigate(['/auth'],);
      } catch (e: any) {
        this.toast.error(e.error?.message || this.translate.instant('An error occurred while resetting the password'));
      }
    } else {
      this.formGroup.markAllAsTouched()
    }
  }
}
