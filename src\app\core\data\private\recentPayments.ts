import { TransactionStatus } from "../dashboards/style2Transactions";

export const recentPaymentsData = [
  {
    id: 31,
    account: '123 *** *** 456',
    icon: 'assets/images/usa-sm.png',
    transferaccount: '789 *** *** 012',
    medium: 'Paypal',
    date: '04/15/2029',
    money: 31579.88,
    status: TransactionStatus.Successful,
    time: '11:30 PM'
  },
  {
    id: 32,
    account: '456 *** *** 789',
    icon: 'assets/images/euro-sm.png',
    transferaccount: '321 *** *** 345',
    medium: 'Skrill',
    date: '04/16/2029',
    money: 23918.74,
    status: TransactionStatus.Cancelled,
    time: '08:45 AM'
  },
  {
    id: 33,
    account: '789 *** *** 012',
    icon: 'assets/images/jp-sm.png',
    transferaccount: '654 *** *** 678',
    medium: 'Stripe',
    date: '04/17/2029',
    money: 18093.27,
    status: TransactionStatus.Pending,
    time: '02:15 PM'
  },
  {
    id: 34,
    account: '012 *** *** 345',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '987 *** *** 890',
    medium: 'TransferWise',
    date: '04/18/2029',
    money: 55028.46,
    status: TransactionStatus.Successful,
    time: '05:00 AM'
  },
  {
    id: 35,
    account: '345 *** *** 678',
    icon: 'assets/images/cn-sm.png',
    transferaccount: '210 *** *** 234',
    medium: 'Revolut',
    date: '04/19/2029',
    money: 17624.11,
    status: TransactionStatus.Pending,
    time: '01:30 PM'
  },
  {
    id: 36,
    account: '678 *** *** 901',
    icon: 'assets/images/rs-sm.png',
    transferaccount: '543 *** *** 456',
    medium: 'Neteller',
    date: '04/20/2029',
    money: 96234.79,
    status: TransactionStatus.Successful,
    time: '10:20 PM'
  },
  {
    id: 37,
    account: '901 *** *** 234',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '876 *** *** 567',
    medium: 'Square',
    date: '04/21/2029',
    money: 4312.09,
    status: TransactionStatus.Cancelled,
    time: '07:40 AM'
  },
  {
    id: 38,
    account: '234 *** *** 567',
    icon: 'assets/images/usa-sm.png',
    transferaccount: '109 *** *** 890',
    medium: 'Venmo',
    date: '04/22/2029',
    money: 22897.63,
    status: TransactionStatus.Successful,
    time: '03:55 PM'
  },
  {
    id: 39,
    account: '567 *** *** 890',
    icon: 'assets/images/rs-sm.png',
    transferaccount: '432 *** *** 123',
    medium: 'Google Pay',
    date: '04/23/2029',
    money: 50473.15,
    status: TransactionStatus.Successful,
    time: '11:10 AM'
  },
  {
    id: 40,
    account: '890 *** *** 123',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '765 *** *** 456',
    medium: 'Apple Pay',
    date: '04/24/2029',
    money: 41687.24,
    status: TransactionStatus.Successful,
    time: '09:05 PM'
  },
  {
    id: 41,
    account: '123 *** *** 456',
    icon: 'assets/images/usa-sm.png',
    transferaccount: '789 *** *** 012',
    medium: 'Paypal',
    date: '04/15/2029',
    money: 31579.88,
    status: TransactionStatus.Successful,
    time: '11:30 PM'
  },
  {
    id: 42,
    account: '456 *** *** 789',
    icon: 'assets/images/euro-sm.png',
    transferaccount: '321 *** *** 345',
    medium: 'Skrill',
    date: '04/16/2029',
    money: 23918.74,
    status: TransactionStatus.Cancelled,
    time: '08:45 AM'
  },
  {
    id: 43,
    account: '789 *** *** 012',
    icon: 'assets/images/jp-sm.png',
    transferaccount: '654 *** *** 678',
    medium: 'Stripe',
    date: '04/17/2029',
    money: 18093.27,
    status: TransactionStatus.Pending,
    time: '02:15 PM'
  },
  {
    id: 44,
    account: '012 *** *** 345',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '987 *** *** 890',
    medium: 'TransferWise',
    date: '04/18/2029',
    money: 55028.46,
    status: TransactionStatus.Successful,
    time: '05:00 AM'
  },
  {
    id: 45,
    account: '345 *** *** 678',
    icon: 'assets/images/cn-sm.png',
    transferaccount: '210 *** *** 234',
    medium: 'Revolut',
    date: '04/19/2029',
    money: 17624.11,
    status: TransactionStatus.Pending,
    time: '01:30 PM'
  },
  {
    id: 46,
    account: '678 *** *** 901',
    icon: 'assets/images/rs-sm.png',
    transferaccount: '543 *** *** 456',
    medium: 'Neteller',
    date: '04/20/2029',
    money: 96234.79,
    status: TransactionStatus.Successful,
    time: '10:20 PM'
  },
  {
    id: 47,
    account: '901 *** *** 234',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '876 *** *** 567',
    medium: 'Square',
    date: '04/21/2029',
    money: 4312.09,
    status: TransactionStatus.Cancelled,
    time: '07:40 AM'
  },
  {
    id: 48,
    account: '234 *** *** 567',
    icon: 'assets/images/usa-sm.png',
    transferaccount: '109 *** *** 890',
    medium: 'Venmo',
    date: '04/22/2029',
    money: 22897.63,
    status: TransactionStatus.Successful,
    time: '03:55 PM'
  },
  {
    id: 49,
    account: '567 *** *** 890',
    icon: 'assets/images/rs-sm.png',
    transferaccount: '432 *** *** 123',
    medium: 'Google Pay',
    date: '04/23/2029',
    money: 50473.15,
    status: TransactionStatus.Successful,
    time: '11:10 AM'
  },
  {
    id: 50,
    account: '890 *** *** 123',
    icon: 'assets/images/uk-sm.png',
    transferaccount: '765 *** *** 456',
    medium: 'Apple Pay',
    date: '04/24/2029',
    money: 41687.24,
    status: TransactionStatus.Successful,
    time: '09:05 PM'
  }
]
