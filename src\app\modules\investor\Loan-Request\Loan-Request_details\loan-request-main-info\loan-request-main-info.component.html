<div class="grid grid-flow-row-dense grid-cols">

    <div class="container-fluid">

    <div class="box mb-4 xxxl:mb-6 m-2">
            <div class="card  m-4">
                <div class="col-span-3">
                    <div class="p-4 ">

                        <p class="text-3xl font-bold border-b-2 border-[#015C8E] border-dashed py-3">
                            {{"MainInfo" | translate }}</p>
                        
                            <form class="grid grid-cols-1 gap-4 xxxl:gap-6 mt-7">
                              
 @for( item of listOfSelectedItems; track item){
      <div class="border rounded-xl p-4 flex flex-col gap-2 rtl text-right shadow-sm w-auto my-3 bg-[#015C8E05]">
        <!-- Top row -->
        <div class="flex justify-between flex-wrap items-start text-sm">
          <!-- جهة مصدرة -->
          <div class="flex-1 w-1/3">
            <span class="text-gray-500">{{"purchaseorder"| translate}} : </span>
            <span class="font-bold">{{item.type==1?("Governmental"|translate):("private" |translate) }} </span>

          </div>
          <!-- الرقم المرجعي -->
          <div class="flex-1 w-1/3">
            <span class="text-gray-500">{{'CompetitionNo'|translate}} : </span>
            <span class="font-bold">{{item.clientCRNumber}}</span>
          </div>
          <!-- الملف + trash -->
          <div class="flex items-center gap-2 w-1/3 ">
          
            <span class="text-gray-500 cursor-pointer underline" (click)="previewFile(item.id)">الملف</span>
          </div>
        </div>

        <!-- Bottom row -->
        <div class="flex justify-start items-center flex-wrap text-sm">
          <!-- القيمة -->
          <div class="flex-1 w-1/3">
            <span class="text-gray-500">{{'amountloan'|translate}} : </span>
            <span class="font-bold"> {{(item.amount | number:'1.0-0' )|| 0 }} {{'SAR' |translate}}</span>
          </div>
          <!-- رقم المنافسة -->
          <div class="flex-1 w-1/3">
            <span class="text-gray-500">{{'RefNumber'|translate}} : </span>
            <span class="font-bold">{{item.invoiceVAT || 0}}</span>
          </div>
          <div class="flex-1 w-1/3"></div>
        </div>
      </div>

    }
                              </form>

                    </div>

                </div>
              

            </div>
        </div>
    </div>
   

</div>