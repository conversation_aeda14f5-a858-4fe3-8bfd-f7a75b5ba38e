import { Injectable } from '@angular/core';
import { PAGINATION } from '../../../../core/models/pagination.model';
import { ApiService } from '../../../../core/services/api.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
   pagination = PAGINATION;
     private path='/client'
  initPagination = () => this.pagination = this.apiService.initPagination(this.pagination);
  constructor(  private apiService: ApiService) { }

  
async getDashboardDetails(body:any){
      return await this.apiService.post<any>(`${this.path}/investments/statistics`,body); 
 }
async getDepositstatistics(body:any){
      return await this.apiService.post<any>(`${this.path}/deposits/statistics`,body); 
 }
async getWithdrawstatistics(body:any){
      return await this.apiService.post<any>(`${this.path}/withdraw/statistics`,body); 
 }


}
