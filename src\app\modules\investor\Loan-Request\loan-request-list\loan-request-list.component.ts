import { DecimalPipe, NgClass, DatePipe, CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { DataTableModule, colDef } from '@bhplugin/ng-datatable';
import { ModalService } from '@component/form/modals/modal.service';
import { DropdownComponent } from '@component/shared/dropdown/dropdown.component';
import { TopBannerComponent } from '@component/shared/top-banner/top-banner.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UserService } from '../../../../core/services/user.service';
import { PaymentType } from '../../../../shared/enums/payment.enum';
import { DateFormatPipe } from '../../../../shared/pipe/date-format.pipe';
import { PaymentRequestModel, PaymentRequestSearch } from '../../deposits/models/deposit.model';
import { WithdrawalsService } from '../../withdrawals/services/withdrawals.service';
import { LoanRequestService } from '../Service/loan-request.service';
import { single } from 'rxjs';
import { AddLoanrequestPopupComponent } from '../Add-loanrequest-popup/Add-loanrequest-popup.component';

@Component({
  selector: 'app-loan-request-list',
  standalone: true,
  imports: [
      DataTableModule,
      DateFormatPipe,
      DropdownComponent,
      TranslateModule,
      DecimalPipe,
      NgClass,
      TopBannerComponent,
      CommonModule,
      AddLoanrequestPopupComponent
  ],
    providers: [
      DatePipe
    ],
  templateUrl: './loan-request-list.component.html',
  styleUrl: './loan-request-list.component.css'
})
export class LoanRequestListComponent {
columns: Array<colDef> = [];
LoanRequests:any;
  search: PaymentRequestSearch = {isServerMode: true} as PaymentRequestSearch;
protected modalService: ModalService = inject(ModalService)
 #router:Router=inject(Router)
  reqId!:string
  constructor(
    public translate: TranslateService,
    protected LoanRequestService: LoanRequestService,
    private router: Router,

  ) {
    this.LoanRequestService.initPagination();
  }

  async ngOnInit() {
    this.columns = [
      {title: this.translate.instant('loanRequestNo'), field: 'loanRequestNo',},
      {title: this.translate.instant('orderDate'), field: 'requestDate',},
      {title: this.translate.instant('amount'), field: 'requestedAmount',},
      {title: this.translate.instant('Peroid'), field: 'periodInDays',},
      {title: this.translate.instant('requestTypeName'), field: 'requestTypeName',},
      {title: this.translate.instant('status'), field: 'requestStatusName',},
      {title: this.translate.instant('Actions'), field: 'actions',},
     

    ]
    await this.getList();
  }
DataList:any

  LoanRequest=signal<any[]>([])
  async getList() {
    try {
      this.search.paymentType = PaymentType.Deposit;
      this.LoanRequest.set(await this.LoanRequestService.getList(this.search) ?? [])
     this.DataList = this.LoanRequest();
      this.LoanRequests = this.DataList.map((item: any) => {
     
        
      })
    } catch (e: any) {
      console.log(e);
    }
  }

  async handleSearch(e: any) {
    this.search = {find: (e.target as HTMLInputElement).value, isServerMode: false} as PaymentRequestSearch;
    this.LoanRequests = [];
    this.LoanRequestService.initPagination();
    await this.getList();
    this.search.isServerMode = true;
  }

  handleSelectItem(item: any) {
    this.router.navigate(['/loanRequest/edit', item.id]);
  }
  async handePageChange(currentPage: number) {
    this.search.page = currentPage;
    await this.getList();
  }
PaymentNow(id: string) {
    this.reqId = id;
   this.modalService.open('loan-request-popup');
  
}

  handleClosePopup(event:boolean){
    if(event){
      this.#router.navigate(['/loanRequest'])
      this.modalService.close('loan-request-popup')
    }
  }

  openCreateloanRequest() {
    this.router.navigate(['/loanRequest/create']); // Not logged in, redirect to login page
  }
  nextstep() {
    this.router.navigate(['/loanRequest/create']); // Not logged in, redirect to login page
  }
  Update(id	: string) {
    this.router.navigate(['/loanRequest/Details/' + id	]);
  }

}
