import {ActivatedRouteSnapshot, CanActivate, CanActivateFn, Router, RouterStateSnapshot} from '@angular/router';
import {AuthService} from "../../modules/auth/services/auth.service";
import {Injectable} from "@angular/core";


@Injectable({providedIn: 'root'})
export class AuthorizedGuard implements CanActivate {
   constructor(
    private router: Router,
    private authService: AuthService,
  ) {}

   canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    
    if (this.authService.isLoggedIn()) {
      const employeeTypes = route.data['employeeTypes'];
      if (this.authService.hasRequiredRole(employeeTypes)) {
        return true; // User has the required role, allow access
      } else {
        this.router.navigate(['/orders']); // Not the required role, redirect to 'orders'
        return false;
      }
    } else {
      this.router.navigate(['/login']); // Not logged in, redirect to login page
      return false;
    }
  }

}


