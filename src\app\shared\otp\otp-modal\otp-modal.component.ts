import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import {Form<PERSON>rray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {Async<PERSON><PERSON><PERSON>, Ng<PERSON>lass, NgForOf} from "@angular/common";
import {ModalService} from "@component/form/modals/modal.service";
import {ModalComponent} from "@component/form/modal/modal.component";
import {ButtonComponent} from "@component/form/button/button.component";
import {MDModalModule} from "@component/form/modals";
import {TranslateModule} from "@ngx-translate/core";
import {LoadingBarService} from "@ngx-loading-bar/core";
import {interval, Subscription} from "rxjs";

@Component({
  selector: 'app-otp-modal',
  standalone: true,
  imports: [
    FormsModule,
    NgForOf,
    ReactiveFormsModule,
    NgClass,
    ModalComponent,
    ButtonComponent,
    MDModalModule,
    TranslateModule,
    AsyncPipe,
  ],
  templateUrl: './otp-modal.component.html',
  styleUrl: './otp-modal.component.css'
})
export class OtpModalComponent implements AfterViewInit {
  otpForm!: FormGroup;
  @Input() id = 'otp-modal';
  @ViewChild('submitButton') submitButton!: ElementRef<HTMLButtonElement>;
  @Output() submitOtp = new EventEmitter<string>();
  @Output() reSend = new EventEmitter<void>();
  @Input() title: string = 'mobilePhoneVerification';
  @Input() subTitle: string = 'enterVerificationCode';

  countdown: number = 0;
  countdownSubscription: Subscription | null = null;
  readonly countdownStart = 60; // Countdown start time in seconds
  constructor(
    private fb: FormBuilder,
    protected modalService: ModalService,
    private cdr: ChangeDetectorRef,
    protected loadingBar: LoadingBarService,
  ) {
  }

  get otpControls() {
    return (this.otpForm.get('otp') as FormArray).controls;
  }

  ngOnInit(): void {
    this.otpForm = this.fb.group({
      otp: this.fb.array(new Array(6).fill('').map(() => this.fb.control('')))
    });
  }

  ngAfterViewInit(): void {
    this.focusInput(0);
    this.startCountdown();
    this.cdr.detectChanges();  // This ensures the change detection cycle is completed
  }

  handleKeyDown(event: KeyboardEvent, index: number): void {
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (
      !/^[0-9]{1}$/.test(event.key) &&
      event.key !== 'Backspace' &&
      event.key !== 'Delete' &&
      event.key !== 'Tab' &&
      !event.metaKey
    ) {
      event.preventDefault();
    }
    if (event.key === 'Delete' || event.key === 'Backspace') {
      if (index > 0) {
        otpArray.at(index - 1).setValue('');
        this.focusInput(index - 1);
      }
    }
  }

  handleInput(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (input.value) {
      if (index < otpArray.length - 1) {
        this.focusInput(index + 1);
      } else {
        this.submitButton.nativeElement.focus();
        // const submitButton = document.querySelector('button[type=submit]') as HTMLButtonElement;
        // submitButton.focus();
      }
    }
  }

  handleFocus(index: number): void {
    const otpArray = this.otpForm.get('otp') as FormArray;
    otpArray.at(index).markAsTouched();
  }

  handlePaste(event: ClipboardEvent): void {
    event.preventDefault();
    const clipboardData = event.clipboardData;
    if (!clipboardData) {
      return;
    }
    const text = clipboardData.getData('text');
    const otpArray = this.otpForm.get('otp') as FormArray;
    if (!new RegExp(`^[0-9]{${otpArray.length}}$`).test(text)) {
      return;
    }
    const digits = text.split('');
    otpArray.controls.forEach((control, index) => control.setValue(digits[index]));
    this.submitButton.nativeElement.focus();
    // const submitButton = document.querySelector('button[type=submit]') as HTMLButtonElement;
    // submitButton.focus();
  }

  verify(): void {
    if (this.otpForm.valid) {
      this.submitOtp.emit(this.otpForm.value.otp.join(''));
    } else {
      this.otpForm.markAllAsTouched();
    }
  }

  startCountdown() {
    this.countdown = this.countdownStart;
    this.clearCountdown();
    this.countdownSubscription = interval(1000).subscribe(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        this.clearCountdown();
      }
    });
  }

  clearCountdown() {
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
      this.countdownSubscription = null;
    }
  }

  private focusInput(index: number): void {
    const input = document.getElementById('otp-input-' + index) as HTMLInputElement;
    input.focus();
    input.select();
  }
}
