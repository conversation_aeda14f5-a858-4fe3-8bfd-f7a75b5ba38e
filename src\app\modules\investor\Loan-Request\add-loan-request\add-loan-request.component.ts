import { DecimalPipe, Ng<PERSON><PERSON>, DatePipe, NgStyle } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DataTableModule } from '@bhplugin/ng-datatable';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LoanRequestService } from '../Service/loan-request.service';
import { LoanRequestType } from '../enums/LoanRequestType.enum';
import { EncryptStorage } from 'encrypt-storage';
import { ToastService } from '../../../../core/services/toast.service';

@Component({
  selector: 'app-add-loan-request',
  standalone: true,
    imports: [
    DataTableModule,
    NgClass,
    TranslateModule,

],
      providers: [
        DatePipe
      ],
  templateUrl: './add-loan-request.component.html',
  styleUrl: './add-loan-request.component.css'
})
export class AddLoanRequestComponent implements OnInit {

  encryptStorage = new  EncryptStorage('App_LoanRequest');
  loantypes:{title:string,text:string,loanRequesttype:LoanRequestType}[] = [
    {title:'Invoicing', text: 'InvoicesDesc',loanRequesttype:LoanRequestType.Invoicing },
    {title:'PurchaseOrder', text: 'PurchaseOrderDesc',loanRequesttype:LoanRequestType.PurchaseOrder },
    { title:'SupplyChain', text: 'PurchaseOrderDesc',loanRequesttype:LoanRequestType.SupplyChain }
  ];
  selectedLoanItem:{title:string,text:string,loanRequesttype:LoanRequestType}|undefined  = undefined
  id: string = '';
  LoanRequest:any = []
  constructor(
     public translate: TranslateService,
      protected LoanRequestService: LoanRequestService,
      private toast: ToastService,
      private activeRoute: ActivatedRoute,
      private router: Router,

) {
  this.LoanRequestService.initPagination();
}
  ngOnInit(): void {
    this.encryptStorage.removeItem('LoanRequetDraft')
    this.id = this.activeRoute.snapshot.params['id'];
    if(this.id !== 'undefined'){
      this.getItemById(this.id);
    }
  }

  handleSelectItem(item: { title: string; text: string; loanRequesttype: LoanRequestType }) {
    this.selectedLoanItem = item;
  }

  async getItemById(id: string) {
    try {
      const res:any = await this.LoanRequestService.getLoanRequest(id);
      this.LoanRequest = res.data;
     
      this.selectedLoanItem = this.loantypes.find((item) => item.loanRequesttype === this.LoanRequest.requestType);
      this.encryptStorage.setItem('LoanRequetDraft',this.LoanRequest)
    } catch (e: any) {
      console.log(e);
    }
  }
  nextStep() {
    if(this.selectedLoanItem){
     this.encryptStorage.setItem('LoanRequestType',this.selectedLoanItem.loanRequesttype)
      this.router.navigate(['/loanRequest/addInfo/']); // + `${this.LoanRequest.id}`Not logged in, redirect to login page
    }else{
      this.toast.error(this.translate.currentLang == 'ar' ? 'يرجى اختيار نوع التمويل' : 'Please select loan type')
    }
  }

}
