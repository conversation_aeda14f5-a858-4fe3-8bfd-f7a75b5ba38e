<ng-modals [id]="id" [placement]="'modal-top'"
           className="fixed flex flex-col transition-all md:w-[50rem] duration-300 ease-in-out left-2/4 z-50 -translate-x-2/4 translate-y-8">
  <div id="loan-request-popup" 
       class="w-[37rem] mx-auto mt-10 overflow-y-scroll rounded-xl h-[30rem] border border-gray-200 shadow-md bg-white text-center p-6 space-y-6 text-gray-800">
    <!-- Header -->
    <div class="flex justify-end">
      <span (click)="onClose()" class="text-black cursor-pointer text-end">
        {{"Cancel" | translate}}
      </span>
    </div>
    
    <div class="flex justify-between items-center text-sm font-semibold">
      <span class="text-xl font-bold m-auto">
        {{ "onesteptofinishyourrequest" | translate }}
      </span>
    </div>
  
    <hr class="border-dashed border-t border-gray-300" />
  
    <!-- Main Message -->
    <div class="space-y-2">
      <p class="text-gray-600 text-lg">
        {{ "Youmustpaytheapplicationstudyfeeof" | translate }}
      </p>
      <p class="text-2xl font-bold text-[#0373f3]">
        {{paymentDetails()?.amount || 0}} {{"SAR" | translate}}
      </p>
      <p class="text-sm text-gray-500">
        *{{"Theamountpaidwillbedeductedfromtheadministrative" | translate }}
      </p>
    </div>
  
    <!-- Payment Button -->
    @if (!paymentDetails()?.isPaid) {
      <div>
        <button [disabled]="!authService.userBalance() || authService.userBalance() === '0'" 
                (click)="handlePayment()" 
                class="px-8 py-2 text-lg font-semibold border-2 border-[#1e4d6b] rounded-full hover:bg-[#1e4d6b] hover:text-white transition">
          {{ "Paymentfromthewalletbalance" | translate }}
        </button>
      </div>
    }
    
    <!-- Wallet Balance -->
    <div class="text-sm">
      <p>
        {{ "currentcredit" | translate }}
        {{authService.userBalance() || 0}} {{"SAR" | translate}}
      </p>
      <a href="#" 
         class="text-[#0373f3] font-medium hover:underline"
         (click)="toggleDepositDiv(); $event.preventDefault()">
        {{ "Makeadeposittothewalletnow" | translate }}
      </a>
      
      @if (showDepositDiv) {
        <div class="items-center justify-center mt-4">
          <hr class="border-dashed border-t border-gray-300" />
          <p class="text-lg font-cairo font-semibold text-center text-gray-500 mt-2">
            {{ "depositMethodside" | translate }}
          </p>
        </div>
        <app-deposit-method></app-deposit-method>
      }
    </div>
  
    <!-- Footer Note -->
    <p class="text-xs text-gray-500">
      {{ "After your request, only the due amount will be paid." | translate }}
    </p>
  </div>
</ng-modals>