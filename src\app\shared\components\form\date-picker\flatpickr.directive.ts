import {Directive, ElementRef, Inject, Input, OnInit} from '@angular/core';
import flatpickr from 'flatpickr';
import {Options} from 'flatpickr/dist/types/options';

@Directive({
  selector: '[mwlFlatpickr]'
})
export class FlatpickrDirective implements OnInit {
  @Input() flatpickrOptions: Options = {};

  constructor(@Inject(ElementRef) private el: ElementRef){
  }

  ngOnInit() {
    this.initFlatpickr();
  }

  private initFlatpickr() {
    flatpickr(this.el.nativeElement, this.flatpickrOptions);
  }
}
