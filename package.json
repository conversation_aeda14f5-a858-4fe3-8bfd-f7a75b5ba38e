{"name": "mdd-plus-investor", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:staging": "ng build --configuration=staging", "watch": "ng build --watch --configuration development", "test": "ng test", "upgrade": "ncu && ncu -u && npm install --force"}, "private": true, "dependencies": {"@angular/animations": "^18.0.4", "@angular/cdk": "^18.0.4", "@angular/common": "^18.0.4", "@angular/compiler": "^18.0.4", "@angular/core": "^18.0.4", "@angular/forms": "^18.0.4", "@angular/material": "^18.0.4", "@angular/platform-browser": "^18.0.4", "@angular/platform-browser-dynamic": "^18.0.4", "@angular/router": "^18.0.4", "@bhplugin/ng-datatable": "^0.1.0", "@iconify/json": "^2.2.222", "@ng-bootstrap/ng-bootstrap": "^19.0.0", "@ng-select/ng-select": "^13.3.0", "@ngrx/effects": "^18.0.0", "@ngrx/store": "^18.0.0", "@ngx-loading-bar/core": "^6.0.2", "@ngx-loading-bar/http-client": "^6.0.2", "@ngx-loading-bar/router": "^6.0.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "angular-mentions": "^1.5.0", "apexcharts": "^3.49.1", "dropzone": "^6.0.0-beta.2", "encrypt-storage": "^2.14.6", "flatpickr": "^4.6.13", "moment": "^2.30.1", "ng-apexcharts": "^1.11.0", "ngx-dropzone": "^3.1.0", "ngx-dropzone-wrapper": "^17.0.0", "ngx-hijri-gregorian-datepicker": "^1.1.0", "ngx-toastr": "^19.0.0", "primeng": "^17.18.1", "rxjs": "~7.8.1", "sweetalert2": "^11.12.0", "tailwind-merge": "^2.3.0", "tslib": "^2.6.3", "xlsx": "^0.18.5", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.5", "@angular/cli": "^18.0.5", "@angular/compiler-cli": "^18.0.4", "@iconify/tailwind": "^1.1.1", "@types/jasmine": "~5.1.4", "autoprefixer": "^10.4.19", "jasmine-core": "~5.1.2", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "~5.4.5"}}